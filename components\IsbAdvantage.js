import React from 'react'

export const IsbAdvantage = ({landingPageResponse, classes}) => {
  return (
    <div className={`col-lg-12 col-md-12 col-12 py-md-4  mx-auto ${classes.equalPadding}`}
    >
        <div className={` d-flex justify-content-center mb-3 mt-md-0 mt-4 `}>
          <h2 className={`${classes.main_blue_head}  `}>{landingPageResponse.achievements_title}</h2>
        </div>
      <div className={`${classes.card_main} px-4 px-lg-0 p-0 card `}>

        <div className="row d-flex justify-content-evenly">
          {landingPageResponse.achievements.map((ach, i) => {
            return (

              <div key={i} className="col col-xs-3 col-sm-3 col-6">
                <div className={`d-flex justify-content-center mt-3  `}>
                  <h3 className={` ${classes.rank} p-0 text-white`}>
                    {ach.rank_title}
                  </h3>
                </div>
                <div className={`text-center ${classes.rank_description}`}>
                  {/* px-lg-5 */}
                  <p className=" py-2 text-white ">
                    {ach.rank_description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

    </div>
  )
}
