import React, { useState, useEffect, useRef } from 'react';
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Image from 'next/image';
import classes from '../pages/lpv2/[learning_track_id]/index.module.css';

export const AiComponent = ({ apiUrl, landingPageResponse, componentFlag }) => {

  const [isClient, setIsClient] = useState(false);
  const aiComponentData = landingPageResponse.AI_Component

  useEffect(() => {
    setIsClient(true);
  }, [])

  

  const settings = {
    // dots: true,
    infinite: true,
    speed: 400,
    autoplay: true,
    autoplaySpeed: 1000,
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: false,
    // nextArrow: <CaseletNextArrow />,
    // prevArrow: <CaseletPrevArrow />,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          speed: 400,
          arrows: false,
          slidesToShow: 2,
          autoplay: true,
          autoplaySpeed: 1000,
        },
      },
    ],
  };

  if (!isClient || aiComponentData == null) {
    return null;
  }

  return (

    <div className="bg-white">
      <div className={`container bg-white ${classes.equalPadding} `}>
      <div className="row justify-content-center">
        <div className={`${componentFlag ?"px-2 px-md-0":"px-4"} col-12 p-0 pb-2 pb-md-2 pt-4  bg-white m-auto d-flex flex-column gap-2`}>
          <h2 className={`text-center pb-3 pb-md-4 pt-3  m-0 lh-lg ${classes.main_blue_head}`}>{aiComponentData.title}</h2>
          <Slider {...settings} className={`${componentFlag?"px-3":"px-0"} pb-0 pb-md-4`}>
            {aiComponentData.ai_image_component.map((ai) => (
              <div key={ai.id} className="d-lg-flex bg-white pb-4 pb-md-0">
                <div className={`col bg-white mt-md-4 py-md-4 py-2 mt-2 ps-md-4 ps-lg-4 ps-sm-2 ps-xs-0 pe-2 mb-2 `} >
                  <div className={`d-flex flex-sm-column flex-md-row flex-column justify-content-center align-items-center text-center text-md-start text-lg-start ${classes.profRow} w-100`}>
                    <div className={`${classes.centeredImage}`}>
                      <Image
                        width="0"
                        height="0"
                        sizes="100vw"
                        alt="aiImage"
                        className={`${classes.aiimagestyle}`}
                        src={`${apiUrl}${ai.ai_image.data.attributes.url}`}
                      />
                    </div>
      
                  </div>
                </div>
              </div>
            ))}
          </Slider>
      
           <div className={`${!componentFlag ? "pb-4":"pb-md-2"} d-flex flex-column justify-content-center align-items-center pb-0 `}>
             <div className={classes.ai_bottom_text}
              dangerouslySetInnerHTML={{
                __html: aiComponentData?.bottom_text
              }} />
      
      
                   </div>
                    {componentFlag && <div className={classes.responsive_div}></div>}
           </div>
           </div>
        </div>
    </div>
  );
};
 