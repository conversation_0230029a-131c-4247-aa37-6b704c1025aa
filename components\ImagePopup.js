import { useState, useEffect } from 'react';
import classes from '../pages/v2/[learning_track_id]/index.module.css';
import Image from 'next/image';

const ImagePopup = ({ apiUrl, imageData}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [closeFlag, setCloseFlag] = useState(false);

  useEffect(() => {
    setIsClient(true);
      setCloseFlag(true)
  }, [closeFlag]);
  useEffect(() => {
    document.addEventListener('keydown', function(e) {
      if(e.key=='Escape'||e.key=='Esc'|| e.key==27){
        setIsOpen(false)
        setCloseFlag(false)
      }
  });
  },[])
  
  const handleModalOpen= ()=>{
    setIsOpen(true);
    setCloseFlag(false)

  }

  const handlemodalClose=()=>{
    setIsOpen(false);
    setCloseFlag(false)
  }
 if(!isClient){
  return null
 }
  return (
    <div className={classes.videopopup_main}>
     <div className={classes.popupvideo_sub} >
     <Image onClick={() =>handleModalOpen()} sizes="(max-width: 768px) 100vw, 33vw" src={apiUrl +imageData.course_image.data.attributes.formats.thumbnail.url}  height={86} width={155} alt='course_short_image'></Image>
     </div>
     {imageData && <p>{imageData.image_description}</p>}
      

      <div className={classes.container}>
        <div className={classes.video_container}>
          <div className={`${classes.popup_video} img-fluid align-content-center ${isOpen ? 'd-block' : 'd-none'}`}>
          <Image onClick={() =>handleModalOpen()} src={apiUrl + (imageData.course_image  && imageData.course_image.data.attributes.url)} height={200} width={355} alt='course_short_image'></Image>
           { <span onClick={() => handlemodalClose()}>&times;</span>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImagePopup;


 
 