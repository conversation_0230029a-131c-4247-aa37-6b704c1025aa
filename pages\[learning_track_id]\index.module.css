@media screen and (min-width: 1200px){
  .container, .container-lg, .container-md, .container-sm, .container-xl{
  max-width: var(--isb-container-max-width) !important;
 }
}
.card_main{
  background:#26ABCD;
  background-size: cover;
  border-radius: 0px;

}
.rank_des{
  font-family: Open Sans;
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0em;
  text-align: center;
  gap: 10px;
  color: #585865;
  
  }
  .rank{
  
    font-family: Arial;
  font-size: 64px;
  font-weight: 700;
  line-height: 74px;
  letter-spacing: 0em;
  text-align: left;
  
  font-family: Arial;
  font-size: 42px;
  font-weight: 700;
  line-height: 41px;
  letter-spacing: 0em;
  text-align: left;
  color:#000000;
  }
  .highlight_section_main{
    max-width: 1200px;
  
  }
  .highlightbackground{
    /* background-color: #fff; */
    background-color: var(--isb-lightblue-color);
  }
  .highlightbackground .blueHeading{
    display: flex;
    justify-content: center;
    text-align: center;
    padding-top: 2rem;
    padding-bottom: 1rem;
  }
  .prgrmhighlightcards_title{ 
    color: #000000 !important;
    font-weight:700 !important;
    font-size: 16px !important;
    /* text-wrap: nowrap; */
    text-align: center;
    margin-bottom: 4px;
    }
    .advnew_highlightcards_description p{ 
      color: #414040 !important;
      display: flex;
      text-align: center;
      padding: 0px 16px;
      line-height: 22px;
    }
    .prgm_highlights{
      max-width: 350px !important;
      margin-bottom: 18px;
     }
     .lp_whyisbonline{
      max-width: 295px !important;
      margin-bottom: 18px;
     }
  @media only screen and (max-width : 768px) {
    .borderedcls{
      border-right: none !important;
    }}
.fontTwenty {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 27px;
  color: #000000;
}
.blueBoldText{
  font-weight: 700;
  font-size: 20px;
  line-height: 27px;
  color: #057092;
  margin-bottom: 0;
  padding-left: 10px;
}

.imageWrapper {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
 
.videoContainers iframe{
 min-height: 90px !important;
 max-height: 125px !important; 
}
.playIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* Adjust the size and other styles of the play icon as needed */
}
.caseletStyle{
  margin-bottom: 2.5rem;
}
.sideHeads {
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  color: var(--isb-blue-color);
  text-wrap: nowrap;
}
.sideHeads::after, .sideHeads::before{
  border-top: 0;
    border-bottom: 0;
    content: '';
    flex-grow: 0;
    width: 40px;
    height: 3px;
    background: var(--isb-blue-color);
}
.breadCrumb p {
  font-size: 12px;
  color: white;
}
.storiescard{
  max-width: 300px;
}
 .storiescard_sub{
  min-width: 290px;
 }
/* .storiescardcontent h2::before{
  content:'';
  height: 5px;
  width: 5px;
  border-radius: 50%;
  background-color: white;
  padding: 4px 16px;
  margin-right: 10px;

} */
 .storiescardcontent{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
 }
.storiescardcontent h2{
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  text-align: left;
  margin-bottom: 18px;  
}
.storiescardcontent p{
  font-size: 14px;
  font-weight: 400;
  line-height: 22.68px;
  text-align: left;
  
}
.storiescardcontent h6{
  font-family: Open Sans;
  font-size: 17px;
  font-weight: 600;
  line-height: 21px;
  text-align: left;
  margin-bottom: 0px;
  
}
.storiescardcontent p:last-child{
  font-weight: 400;
  line-height: 19.48px;
  text-align: left;
  margin-bottom: 5px;
  align-content: start;
   
}
.storiescardcontent img{
border-radius: 50%;
}

.breadCrumblast {
  font-size: 12px;
  color: white;
  text-decoration: none !important;
}


.breadCrumb p:hover {
  font-size: 12px;
  color: white;
  text-decoration: underline;
}


.hideCrumb {
  max-width: var(--isb-container-max-width);
}


.fontTwentyBlueHeading {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
  line-height: 27px;
  color: #057092;
}


.sliderContainer {
  background-color: #FFFFFF;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
}


.description {
  font-size: 17px;
  vertical-align: center;
  font-weight: 400 !important;
  line-height: normal;
  color: white;
}


.container {
  position: relative;
  text-align: center;
  color: white;
  font-weight: 700;
}


.centered {
  position: absolute;
  top: 100%;
  font-size: 14px;
  max-height: 135px;
  transform: translate(-0%, -100%);
  padding: 6px;
  background-color: rgba(29, 28, 28, 0.603);
}


.wrapper {
  display: flex;
  justify-content: space-between;
}
 
.tabcontainer{
  max-width: 1000px;
  padding: 0px;
}
.certificate{
  width: fit-content !important;
 display: flex !important;
 justify-content: center;
 align-items: center !important;
 margin: auto;
height: 100%;
 background-color: transparent !important;
 border-radius: 10px !important;
}
 .certificate > div:first-child{
background-color:transparent;
border: none;
 }
.modaldialog > div:first-child{
  background-color: transparent;
  border: none;
  border-color: transparent;
}
.profilediv{
  align-items: center;
  margin-top: 20px;
}
.profilediv div:last-child h6{
margin-bottom: 2px;
}
.profilediv div:last-child{
display: flex;
flex-direction: column;
align-items: flex-start;
text-align: left;
justify-content: flex-start;

}
 .banner_active_item{
  text-decoration: underline;
  text-decoration-color: var(--isb-blue-color);
  text-underline-offset: 4px;
  text-decoration-thickness: 2px;
  color: var(--isb-blue-color) !important;
  font-size: 15px;
  font-weight: 600;
 }
 .banner_inactive_item{
  text-decoration: none;
  font-weight: 600;
  color: black;
  font-size: 15px;
 }
 
 .syllabus_sub h2{
font-size: 24px;
font-weight: 600;
line-height: 25px;
text-align: left;
 }
 .syllabus_sub p{
text-align: left;
margin-bottom: 5px;

 }
 
 .syllabus_main a{
  text-decoration-color: var(--isb-blue-color) !important;
 }
 .syllabus_main div:last-child p{
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--isb-blue-color);
  
 }
 .syllabus_main div:last-child{
  display: flex;
  align-items: last baseline;
  text-decoration: underline;
  text-decoration-color: var(--isb-blue-color);
  text-underline-offset: 5px;
 }
 .sectionsWrapper {
  display: flex;
  flex-direction: column;
}
@media (min-width: 768px) {
  .sectionsWrapper {
    flex-direction: row;
  }
}
 .card {
  width: 100%;
  padding: 0;
  border: 1px solid #05719252 ;
  /* border-radius: 8px; */
  margin-bottom: 15px;
}
.toggletextstlye{
  text-decoration: underline;
  text-underline-offset: 2px;
  text-decoration-color: var(--isb-blue-color);
  text-decoration-thickness: 1px;
  color: var(--isb-blue-color);
  padding: 0px 10px 10px 0px;
  font-weight: 600;
  cursor: pointer;
  width: fit-content;
  white-space: nowrap;
  text-wrap: nowrap;
}
.side_heads{
  font-size: 1.5rem;
  text-align: center;
}
.navtab-ul {
  overflow-x: auto;
  white-space: nowrap;
}
 
.nav_item{
  cursor: pointer;
  text-wrap: wrap;
  word-break: break-all;
  word-wrap: break-word;
  padding: 8px 10px;
  font-weight: 600;
  align-items: center;
  display: inline-flex;
  max-width: 250px;
}
 
@media (min-width: 768px) {
  .side_heads {
    font-size: 2rem;
  }

  .navtab_ul {
    justify-content: center;
  }

  .nav-item {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
.leftSection {
  padding: 1rem;
  background-color: var(--isb-blue-color);
  /* border-radius: 8px; */
}
.leftSection *{
  color: white;
}
.leftSection p:first-child{
font-size: 16px;
font-weight: 600;
line-height: 22px;
text-align: left;
}
.leftSection p:nth-child(2){
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
  text-align: left;
  }
.leftSection p:last-child{
font-size: 24px;
font-weight: 600;
line-height: 22px;
text-align: left;
}

.rightSection {
  padding: 0rem ;
}
.rightLeftSection {
  padding-right: 1rem;
  padding-top: 9px;
}
.rightLeftSection h2{
font-size: 20px;
font-weight: 700;
line-height: 22px;
text-align: left;

}
.rightLeftSection p{
  text-align: left;
  max-height: 130px;
  overflow: auto;
vertical-align: center;

}
.rightRightSection {
  /* min-width: 360px; */
}
.rightRightSection, .rightRightSectionWidth{
  padding-left: 8px;
  padding-top: 10px;
  max-width: 360px;
  overflow-x: auto;
  overflow-y: hidden;
}
 
.video_container_main h2{
  font-size: 15px;
  line-height: 19px;
  letter-spacing: 0;
  text-rendering: auto;
  font-weight: 600;
text-align: left;
padding-left: 1rem;
margin-top: 10px;
margin-bottom: 0;
padding-left: 8px;
color: #181818;
-webkit-font-smoothing: antialiased;

}
 .videoWidth{
  width: 800px;
 }

.videoFrame {
  padding: 0.5rem;
  width: 10rem;

  
}
 .react_player iframe{
  height: 150px !important;
 }
.videoFrame p{
  margin-bottom: 0;
font-size: 14px;
font-weight: 400;
line-height: 22px;
text-align: left;

}
 
.videoFrame{
  width: 100%;
  max-height: 2rem;
  min-height: 1rem;
}
.modulesdiv .module_head_title{
  margin-bottom: 10px;
   }
   .modulesdiv{
    margin-bottom: 8px;
   }
 .modulesdiv h4{
   font-size: 24px;
   font-weight: 600;
   line-height: 22px;
   text-align: left;
   color: var(--isb-blue-color);
  }
  .modulesdiv h5{
    font-size: 17.5px;
    font-weight: 600;
    line-height: 22px;
    text-align: left;
    color: #000000;
    margin-bottom: 5px;
    
  }
  .modulesdiv ul{
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-bottom: 15px;
  }
.expandableSection {
  margin-top: 1rem;
}

.toggleButton {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.toggleButton:hover {
  background-color: #0056b3;
}
.hiddenContent{
  margin-left: 200px;
}
.sticky_banner{
  background: #E6F1FA;
  position: sticky !important;
   width: 100%;
   position: -webkit-sticky;
   z-index: 1000;
   transition-delay: 500ms;
   outline: 1px solid #daeff5;
}
 
 /* .active_tab{
   border-radius: 50px;
   position: relative;
   color: var(--isb-blue-color) !important;
    font-size: 17px;
    font-weight: 600;
    text-align: center;
    padding-left: 10px;
    padding-right: 10px;
    background-color: #fff;
    text-wrap: wrap;
    max-width: 250px;
    
  } */
  .lpactive_tab{
    /* text-wrap: wrap; */
    /* max-width: 250px; */
    background-color: #EFF9FE;
    border-radius: 50px;
    position: relative;
    color: var(--isb-blue-color) !important;
     font-size: 17px;
     font-weight: 600;
     text-align: center;
     padding-left: 10px;
     padding-right: 10px;
  }
.wwb_profile_img_div{
  min-width: 55px;
   
}
.wwb_profile_img img{
  width: 55px;
  aspect-ratio: auto 55 / 55;
  height: 55px;
}
 
.navtab_ul{
  flex-wrap: nowrap;
}
 .navtab_ul li{
 max-width: 333px;
 text-align: center;
 font-weight: 500;
 color: black;
 cursor: pointer;
 align-items: center;
 display: flex;
 align-items: center;
 word-wrap: break-word;
 
 }
 .inactiveadvantageitem{
  background-color: #F9FAFA;
 }
 .activeadvantageitem{
  background-color: #EFF9FE;
 }
 .notActiveFirst {
  background-color: #F9FAFA;
}
 .advantageContent{
  /* display: flex; */
  /* flex-direction: column; */
 }
 .advitemUl_list{
  padding-right: 30px;
  padding-left: 10px;
 }
 .advitemlist{
  padding-left: 0;
  padding-right: 10px;
  line-height: 31px;
 }
 .advitemlist p{
  margin-bottom: 5px;
 }
 .advantageitem{
   display: flex;
   align-items: flex-start;
   padding: 10px;
  cursor: pointer;
  margin-bottom: 10px;
  position: relative;
  flex-direction: column;
 }
 .advantageitem:hover, .inactiveadvantageitem:hover{
  background-color: var(--isb-lightblue-color);
 }

 .mediaContainer iframe{
  width: 80vw;
  min-height: 170px !important;
  max-height: 200px !important;

}
.listItemContent {
  word-wrap: break-word;
  /* white-space: normal; */
  word-break: break-word;
  text-wrap: wrap;
   cursor: pointer;
}

 @media (max-width: 460px) {
  .rightSection{
    /* min-width: 350px; */
  }
  .advantageitem {
      display: flex;
      flex-direction: column;
      margin-bottom: 1.5rem;
  }
.advitemlist, .advitemUl_list{
  padding: 3px;
  margin-bottom: 10px;
}
.mediaContainer iframe{
  width: 100% !important;
      justify-content: center;
      display: flex;
      margin: auto;
}
.mediaContainer img{
  width: 100%;
      justify-content: center;
      display: flex;
      margin: auto;
}
  .mediaContainer {
      width: 100%;
      justify-content: center;
      display: flex;
      margin: auto;
  }
}

 
@media (min-width: 1200px) {
  .blueThumbtab{
    max-width: 200px;
    min-width: 175px;
   }
}
 @media screen and (min-width: 381px) and (max-width:768){
  .blueThumbtab{
    min-width: 100px;
    max-width: 100;
  }
  .facultytext{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  .alignRight{
    display: flex;
    justify-content: center;
    text-align: center;
  }
 }
 @media (max-width: 380px) {
  .facultytext{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
.rightRightSection{
  max-width: 320px;
  padding: 16px 0.5rem;
  min-width: 200px;
}
.blueThumbtab{
min-width: 100px;
max-width: 100%;
}
.rightLeftSection{
  padding: 16px 0.2rem;
  min-width: 320px;
}
.leftSection{
  max-width: 100%;
}
 }
 @media (max-width: 768px) {
  /* .active_tab{
    background-color: var(--isb-lightblue-color);
  } */
  .nav_item{
    max-width: 160px !important;
    font-size: 16px;

  }
  .tabContainer{
    background-color: transparent !important;
  }
  .wwb_tabContentMain{
background-color: var(--isb-lightblue-color);
  }
  .lptriangle{
bottom: -30px !important;
  }
  .container .popup_video span{
    position: absolute;
    top: 15px !important;
    right: 0px !important;
  }
  .caseletStyle{
    margin-bottom: 10px;
  }
  .rightLeftSection p{
    max-height: 100%;
  }
  .rightSection{
    /* min-width: 350px ; */
  }
  .rightLeftSection{
    min-width: 170px;
    padding-right: 0px;
  }
 }
 @media (min-width: 768px) {

  .advantageContent {
    flex-direction: row;
  }
}
 .triangle {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid var(--isb-lightblue-color); 
  position: absolute;
  bottom: -10%;
  left: 50%;
  transform: translate(-50%, 100%);
}

.lptriangle{
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid #EFF9FE;
  position: absolute;
  bottom: -10%;
  left: 50%;
  transform: translate(-50%, 100%);
}
.tabContainer{
  background-color: #EFF9FE;
}
.star_outline {
  color: transparent; 
  stroke: orange; 
  stroke-width: 25px; 
}
.custombulleticon{
  list-style: none;
  position: relative;
  padding-left: 50px; 
  font-size: 17px;
  min-height: 55px;
  padding-bottom: 10px;
}
.custombulleticon::before{
  content: '';
  position: absolute;
  left: 0;
  top: 5px;
  width: 45px; 
  height: 100%; 
  background: url('../../assets/checked (1) 1.svg') no-repeat top;
  background-size: auto;
  min-height: 35px;

}

.banner{
  background-color: #E6F1FA;
  width: 100%;
}
.bannerstyles{
   background-color: #E6F1FA;
 }
 .overview_li{
   
  display: flex;
 }
 .overview_li li{
  list-style-type: none;
  flex-direction: column;
 }

.sidebar {

  top: 110px !important;
  height: fit-content;
  
}

.showApplication {
  display: none;
}

.image_border {
  border-radius: 0px;
  min-height: 188px;
}
.boxshadow {
  border: none;
  border-radius: 0%;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12);
}
.fontSixteen {
  font-style: normal;
  font-weight: 400;
  font-size: 17px;
  line-height: 26px;
  color: #708FAB !important;
  text-decoration: none;
}
.fontTwentyBlue {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 27px;
  color: #057092;
}
.fontEighteenBlue {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 27px;
  color: #057092;
}


.blackHeading {
  font-weight: 700;
  font-size: 30px;
  line-height: 54.47px;
  color: #000;
}
.para {
  color: black !important;
  text-decoration: none;
  align-items: baseline;
  font-size: 15px;
  vertical-align: center;
  margin-top: 5px;
}

.carousel.carousel-slider .control-arrow {
  padding: 10px;
}

.grey_text {
  color: #585865;
  text-decoration: none;
  font-size: 14px;
  align-items: baseline;
  vertical-align: center;
  font-weight: 400;
}


.custm_btn {
  background-color: #057092;
}


.custm_font {
  color: #057092;
  font-weight: 600;
  font-size: 16px;
}


.custm_btn_outline {
  color: #057092;
  border-radius: 0;
  font-size: 18px;
  font-weight: 600;
  border-color: #057092;
}
.bg_grey {
  background-color: #708FAB
}

.thumbnail_btn ol {
  background-color: white;
  text-align: center;
  border-radius: 8px;
  margin-left: 25px;
  min-width: 150px;
  min-height: 90px;
  font-weight: 600;
  flex-direction: column;
  display: flex;
  align-items: center;
}
.nav-tabs .nav-link.active {
  color: var(--bs-nav-tabs-link-active-color);
  background-color: var(--bs-nav-tabs-link-active-bg);
}

.custm_btn_outline:hover {
  color: #057092;
}

.form-group_checkbox {
  margin: 8px;
}

.form-group_checkbox {
  display: block;
  margin-bottom: 15px;
}


.form-group_checkbox input {
  padding: 0;
  height: initial;
  width: initial;
  margin-bottom: 0;
  display: none;
  cursor: pointer;
}


.form-group_checkbox label {
  position: relative;

  cursor: pointer;
}

.form-group_checkbox label:before {
  content: '';
  background-color: transparent;
  border: 1.5px solid #0000008c;
  padding: 10px;
  display: inline-block;
  max-height: 25px;
  position: relative;
  vertical-align: middle;
  cursor: pointer;
  margin-right: 10px;
  border-radius: 2px;
}


.form-group_checkbox input:checked+label:after {
  content: '';
  display: block;
  position: absolute;
  top: 4px;
  left: 9px;
  width: 6px;
  height: 14px;
  border: solid #0000008c;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}


.resp_btn_caursl {
  justify-content: space-between;
}

.viewbtn {
  background-color: #057092 !important;
  padding: 8px 24px;
  color: #FFFFFF;
  border: none;
  border-radius: 0px !important;
}


.viewbtn:hover {
  background-color: #057092 !important;
  padding: 8px 24px;
  font-weight: 600;
  color: #FFFFFF;
  border: none;
  border-radius: 0px !important;
}
 
.purpleapplicationBtn:hover {
text-decoration: none;
background-color: var(--isb-purple-color) !important;
color: white;
}
.purpleapplicationBtn {
  background-color: var(--isb-purple-color) !important;
  padding: 8px 48px;
  display: flex;
  color: #FFFFFF;
  border: none;
  border-radius: 0px !important;
  margin-bottom: 5px;
  width: 100%;
  justify-content: center;
}
@media screen and (min-width:767px) and (max-width:1100px){

  .purpleapplicationBtn {
    width: fit-content !important;
  }
}
.applicationBtn {
  background-color: #057092 !important;
  padding: 8px 24px;
  margin-bottom: 0;
  color: #FFFFFF;
  border: none;
  border-radius: 0px !important;
}


.applicationBtn:hover {
  background-color: #057092 !important;
  padding: 8px 48px;
  font-weight: 600;
  color: #FFFFFF;
  border: none;
  border-radius: 0px !important;
}


.buttonPos {
  position: relative;
}


.buttonPosition {
  bottom: 0px;
  position: absolute;
}
 
.modalstyle{
  margin: auto;
  height: 100%;
  display: flex;
  overflow-y: auto;
  max-height: 640px;
  min-width: 350px;
  border-radius: 0px !important;
}
.modal-content  {
  -webkit-border-radius: 0px !important;
  -moz-border-radius: 0px !important;
  border-radius: 0px !important; 
}

.fold1bg {
  background-image: url('../../assets/violet_bg.webp');
  background-size: cover;
  background-color: #66266B;
}




.btnprimary {
  background-color: #057092;
  border-radius: 0px;
}

.text_color b {
  color: #057092;
}

 

.lessonContent {
  color: #7C8495 !important;
  text-decoration: none;
  align-items: baseline;
  padding: 2px;
  margin: 0px;
  vertical-align: center;
}


.header {
  width: 100%;
  height: 5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #FFFFFF;
  padding: 0 10%;
  position: sticky;
}

.navelems {
  right: 0;
  padding-right: 32px;
  padding-left: 32px;
}


.content {
  padding: 25PX;
  background-color: #F9F9F9;
  margin-top: 10px;
}

.fold1 {
  padding: 0;
  background-color: white;
  margin-top: 48px;
  margin-left: 48px;
  margin-right: 48px;
}


.bottomFold {
  padding: 0;
  background-color: white;
  margin-top: 48px;
  margin-left: 48px;
  margin-right: 28px;
}


.profFold {
  padding: 0;
  background-color: white;
  margin-right: 48px;
}

.equalPadding {
  max-width: var(--isb-container-max-width);
}


.fontwhite {
  font-size: 0px !important;
  background-color: red;
}

.fold1btn {
  min-height: 38px;
  background-color: #fff !important;
  border: 2px solid #fff !important;
  color: #66266B !important;
  font-weight: 600;
  border-radius: 0px !important;
  fill: #66266B !important;
}

.fold1btn:hover {
  background-color: transparent !important;
  border: 3px solid #fff !important;
  color: #fff !important;
  font-weight: 600;
  border-radius: 0px !important;
  fill: white !important;
}
@media screen and (min-width:1400px){
  .modalstyle{
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
  }
}
@media screen and (min-width:1024px) and (max-width: 1024px) {
  .modalstyle{
    width: 80vw;
    position: relative;
    margin: auto;
    right: 0;
    overflow-y:auto ;
  }
  .storiescard{
    max-width: 290px;
  }
 
}

@media screen and (min-width:500px) and (max-width: 799px) {
 
  .modalstyle{
    width: 79vw;
    position: relative;
    margin: auto;
    overflow-y:auto ;
  }
}
@media (min-width:800px)  {
  .modalstyle{
    overflow-y:auto ;
    width: 28vw;
  
  }
}

@media screen and (min-width:0px) and (max-width: 499px) {
  .mobileiframeWrapper{
    min-height: 216px !important;
  }
  .listItemContent{
    font-size: 12px;
  }
  .nav_item{
    max-width: 112px !important;
  }
  .leftSection h2{
  font-size: 21px;
  }
  .StickyapllyNowBtn{
    background-color: #057092 !important;
    padding: 4px 6px !important;
    display: flex;
    color: #FFFFFF;
    border: none;
    border-radius: 0px !important;
    text-wrap: nowrap;
    font-size: 15px;
  }
  .fold1bg{
  background-color: white;
  background-image: none;
}
.fold1bg *{
  color: black;
}
.fold1btn {
  min-height: 38px;
  background-color: var(--isb-blue-color) !important;
  border: 2px solid var(--isb-blue-color) !important;
  color: white !important;
  font-weight: 600;
  border-radius: 0px !important;
  fill: var(--isb-blue-color) !important;
  font-size: 16px;
  line-height: 22px;
}

.fold1btn:hover {
  background-color: transparent !important;
  border: 3px solid var(--isb-blue-color) !important;
  color: var(--isb-blue-color) !important;
  font-weight: 600;
  border-radius: 0px !important;
font-size: 16px;
line-height: 22px;
  fill: black !important;
}
  .rightRightSection{
  max-width: 100%;
  min-width: 100%;
}
  .downloadBrochure .fold1btn{
width: 100%;
  }
  .card{
    border: none;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.12);

  }
  .roundedCircle{
    height: 100px;
  width: 100px;
  }
  .hiddenContent{
    margin-left: 10px !important;
  }
  .storiescard{
    max-width: 350px;
  }
.modalstyle{
  width: 100vw;
  max-height: 665px;
  overflow-y:auto ;
  position: relative;
  margin: auto;
  padding: 10px;

}

  .hideCrumb {
    display: none;
  }


  .sliderContainer {
    background-color: transparent;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0);
  }

  .showApplication {
    display: block;
  }


  .displayNone {
    display: none;
  }


  .resp_btn_caursl {
    display: none;
    justify-content: space-between;
    margin-top: 18px;
  }

  .alignLeft{
    text-align: center;
  }
  
}


@media screen and (min-width:500px) and (max-width: 976px) {
 
   
  .roundedCircle{
    height: 120px;
  width: 120px;
  }

  .hideCrumb {
    display: none;
  }


  .sliderContainer {
    background-color: transparent;
    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0);
  }

  .showApplication {
    display: block;
  }


  .resp_btn_caursl {
    display: none;
    justify-content: space-between;
    margin-top: 18px;
  }

  .displayNone {
    display: none;
  }

  .alignLeft{
    text-align: center;
  }
}


.learnTrackTitle {
  color: #057092;
  font-size: 16px;
  font-weight: 700;
}

.imageFold {
  max-height: 290px;
  object-fit: cover;
}



.image_container {


  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;


}

.hidden {
  content-visibility: hidden;
}

.auto {
  content-visibility: auto;
}
.video_box {
  position: relative;
  display: flex;
  justify-content: center;
  min-height: 295px;
}

.video_overlays {
  position: absolute;

}

.centeredContent{
  height: 80%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.StickyapllyNowBtn{
  background-color: #057092 !important;
  padding: 4px 10px ;
  display: flex;
  color: #FFFFFF;
  border: none;
  border-radius: 0px !important;
  text-wrap: nowrap;
}
.StickyapllyNowBtn:hover {
  background-color: #057092 !important;
  font-weight: 600;
  color: #FFFFFF;
  border: none;
  border-radius: 0px !important;
}

 @media (min-width: 989px) {
 
  .first_div {
    display: none !important;
  }

  .second_div {
    display: block !important;
  }

  .alignLeft {
    text-align: left;
    font-size: 16px;
    width: 100%;
  }

  .alignRight{
    text-align: start !important;

    color: #057092;
  }

}

@media (max-width:1024px) {
}

@media (max-width: 375px) {

   .navtab_ul li{
    display: flex;
   }
}
@media (max-width: 990px) {
  .advantageitem{
    align-items: center;
  }
  .activeadvantageitem, .advantageitem, .inactiveadvantageitem {
    background-color: transparent;
  }
  .advantageitem:hover, .inactiveadvantageitem:hover{
    background-color: transparent;
   }
  .nav_item{
    max-width: 220px;
  }
  .roundedCircle{
    height: 120px !important;
  width: 120px !important;
  }
  .first_div {
    display: block !important;
  }

  .second_div {
    display: none !important;
  }

}



.roundedCircle {
  border-radius: 50%;
  border: 5px solid lightgray;
  height: 140px;
  width: 140px;
  object-fit: cover;
}
.moduleTitle {
  color: #057092 !important;
  text-decoration: none;
  align-items: baseline;
  vertical-align: center;
  font-size: 21px;
  font-weight: 650;
  margin: 0;
}


.payment_plan_dtls a{
font-size: 12px;
}
.payment_plan_dtls{
  display: flex;
  flex-direction: column;

}


.application_custm_font {
  color: #000000;
  font-weight: 400;
  font-size: 20px;
}
.application_custm_font_new {
  color: #000000;
  font-weight: 700;
  font-size: 20px;
  margin-bottom: 0;
}
.paymentPlanLink{
  padding: 10px 0px 0px 0px;
  display: flex;
  justify-content: center;
}
.contentext > .contentext_child{
  max-width:1000px;   
  
}
.modal_content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px 25px;
  border: 1px solid #888;
  max-width: 80%;
 
}
.closebtn {
  float: right;
  font-size: 28px;
  font-weight: bold;
  text-align: start;
  margin-top: -18px;
  width: 15px;
}
.closebtn:hover,
.closebtn:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
.paymodal_body{
  max-height: 80vh;
  overflow: auto;
}
.paymodal_body::-webkit-scrollbar {
  width: 8px !important;
}
.paymodal_body::-webkit-scrollbar-thumb{
  background: #afb1b3 !important;
  border-radius: 8px !important;
}
@media (max-width: 990px) {
  .modal_content{
    padding: 15px 10px !important;
    max-width: 100% !important;

    margin: 10px !important;
    
  }
  .closebtn{
    margin-top: -10px !important;
  }
}
.contentext_child p{
  color: #585865;
  text-decoration: none;
  align-items: baseline;
  font-size: 17px;
  vertical-align: center;
  font-weight: 400;
}
.fin_modal {
  position: fixed; 
  z-index: 1020;  
  left: 0;
  top: 0;
  width: 100%;  
  height: 100%;  
  overflow: hidden;  
  background-color: rgb(0,0,0);  
  background-color: rgba(0,0,0,0.4); 
  align-items: center;
  justify-content: center;
}
.fontTwentyBlueHeading_new {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 27px;
  color: #057092;
}
.application_custm_font_blue {
  color: #057092;
  font-weight: 600;
  font-size: 16px;
}

.main_white_head {
  color: #FFFFFF;
  font-weight: bold;
  font-size: 24px;
}


.highlightcards_title {
  color: #000000 !important;
  font-weight: 700 !important;
  font-size: 14px !important;
}


.main_blue_head {
  color: #057092;
  font-weight: bold;
  font-size: 24px;
}

.sc_main{
  height: 300px;
}
.carddescWhite p{
  color: white;
  height: 8rem;
  overflow: auto;
}
.carddescBlack p{
  color: black;
  font-size: 17px;
  align-content: center;
}
.videoContainer p{
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  text-align: left;
  max-width: 300px;
  padding-top: 8px;
  max-height: 200px;
  overflow: hidden;
}
.videoContainer video,
.videoContainer img {
  width: 100%;
  height: auto;
}
.showhidelink{
  width: fit-content;
  position: relative;
}

/* video styles---- */
 .videopopup_main p{
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0;
  text-rendering: auto;
  max-width: 175px;
  margin-bottom: 5px;
  font-smooth: 2em;
 }
.popupvideo_sub{
  background-color: #000;
  width: fit-content;
  min-width: 155px;
  min-height: 86px;
  aspect-ratio: auto 155 / 86;
  margin-bottom: 6px;
}
 
.container .video_container .video {
  height: 250px;
  width: 350px;
  overflow: hidden;
}
.container .video_container .video iframe{
 min-width: 500px !important;
  min-height: 500px;
  object-fit: cover;
  transition: .2s linear;

}
.container .popup_video{
  position: fixed;
  top: 0;
  left:0;
  z-index: 1021;
  background-color: rgb(0,0,0,.8);
  height: 100%;
  width: 100%;
  animation: fadeInAnimation .5s;
  
}
.container .popup_video image{
  min-width: 350px !important;
  min-height: 80 !important;
}
.container .popup_video iframe{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 5px;
  object-fit: cover;
  max-width: 748px !important;
  min-height: 421px !important;
  animation: fadeInAnimation ease 0.5s;
}
.container .popup_video span{
  position: absolute;
  top:30px; right: 18px;
  font-size: 30px;
  color: #ffff;
  font-weight: bolder;
  cursor: pointer;
  transform: translate(-50%, -50%);
  animation: fadeInAnimation ease 1s;
  }
  .stories_footer_content p, .stories_footer_content h6 {
    text-align: left;
    color: black;
    margin-bottom: 0;
}

  .stories_footer_content{
    min-height: 70px;
   align-content: center;
  }
@media (max-width:727px){
  .container .popup_video iframe{
   background-color: transparent !important;

  } 
}

@media (max-width:828px){
  .container .popup_video iframe{
    max-width: 650px !important;
    min-height: 365px !important;

  }
}
.spanwrapper{
position: relative;
width: 780px;
}



@keyframes fadeInAnimation {
  0% {
      opacity: 0;
  }
  100% {
      opacity: 1;
  }
}
@media (max-width:767px){
  .triangle{
    bottom: -30px;
    border-bottom: 20px solid var(--isb-lightblue-color); 
  }
}
@media (max-width:767px){

  .wwb_main{
    background-color: white;
  }
  
  
  .wwb_tabContentMain {
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(230, 241, 250, 1);
    padding: 16px 10px 16px 10px;
    border-radius: 12px;

  }
  .syllabus_sub{
    display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
  }
  .stories_slider_main{
    margin: auto ;

  }
}

@media (max-width:1024px){
.container .popup_video iframe{
  width: 95%;
}
  
}
 .oAdivplayer{
  height: auto !important;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  justify-content: center;
 }
.oAdivplayer iframe{
  min-height: 300px !important;
  max-height: 300px !important;
  width: 535px;
  background-color: #000;
}
@media screen and (min-width:500px) and (max-width:575px){
  .rightRightSection{
    max-width: 570px;
  }
  
}
@media (max-width: 575px) {
 
  .toggletextstlye{
    padding-top: 10px;
   }
   .rightRightSection{
    padding-left: 0;
   }
}

/* Small (SM) */
@media (min-width: 576px) and (max-width: 767.98px) {
 .rightRightSection{
  max-width: 80vw;
 }
 .toggletextstlye{
  padding-top: 10px;
 }
 .rightRightSection{
  padding-left: 0;
 }
}

/* Medium (MD) */
@media (min-width: 768px) and (max-width: 991.98px) {
  /* Styles for medium screens */
  .toggletextstlye{
    padding-top: 10px;
   }
}

.stats {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.stats .point {
  padding: 8px 8px 16px 8px;
  width: calc(50% - 2px);
  box-sizing: border-box;
  margin-top: 4px;
  text-align: center;
  background-color: rgb(239, 249, 254)
  
}
 
.stats .point .number {
  font-size: 22px;
  line-height: 44px;
  color: #1a7c7c;
}
.stats .point .desc{
  font-size: 14px;
}


@media (max-width: 990px) {
  .sc_main{
    height: 335px;
  }
  .carddescBlack p{
    height: 160px;
    overflow: auto;
  }
  .stats .point {
    padding: 6px;
    width: calc(50% - 2px);
    box-sizing: border-box;
    
  }
  .stats .point .number {
     
    line-height: 44px;
    color: #1a7c7c;
  }
 
  .regular_icon{
    padding-left: 0px;
  }
  .regular_icon ol{
  padding-left: 0px;
  }

  .modulesdiv ul{
    padding-left: 18px;
    padding-right: 10px;
  }
  .storycard_video_container{
    min-width: 150px !important;
  }
  .p_inside_span li{
    margin-top: 8px;
  }
  .stats .point .desc{
    line-height: 25px;
  }
}

.p_inside_span p{
  margin-bottom: 0px !important;
}
.p_inside_span ul{
  margin-bottom: 2px;
}


.storycard_video{
  max-height: 80px;
  display: flex;
  align-items: center;
}
.storycard_video_container{
  position: relative;
  max-height: 200px;
  min-width: 315px;
}
.features_exercises_title{
  font-size: 17.5px;
    font-weight: 600;
    line-height: 22px;
    text-align: left;
    color: #000000;
}
.exp_free_lesson_cta{
  background: var(--isb-blue-color);
  color: white;
  border: none;
  padding: 10px;
  margin-right: 10px;
  text-decoration: none !important;
}
.mobileiframeWrapper{
  min-height: 210px !important;
}
.forminput{
  max-height: 32px;
  border-radius: 5px !important;
  width: 100% !important;
  border-color: #708FAB !important;
  background-color:#EFF9FE !important;
  font-size: 14px;
}
.formLabelText{
  font-size: 13px;
  font-weight: 500 !important;
}
.formLabelTextcolor{
  color: red;
}
.error_tooltip {
  position: absolute;
  color: white;
  padding: 0px 8px;
  border-radius: 4px;
  margin-bottom: 2px;
  display: block;
  color: red;
  font-size: 12px;
  }
  .bg_phone .flag-dropdown .selected-flag {
    background-color: #D6EFFB !important;
  }
  .land_btn{
    background-color: #057092 !important;
      border:none;
      padding: 8px 35px;
      font-weight: 600;
      flex-wrap: wrap;
      margin-right: 8px;
      margin-left: 8px;
      border-radius: 5px!important;
      width: 275px;
  }
  .land_btn:disabled{
    background-color:grey !important;
    opacity: 50%;
  }