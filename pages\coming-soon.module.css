
.fold1bg {
    background-image: url('../assets/isb_campus.svg');
    background-size: cover;
    box-shadow: inset 0 0 0 1000px rgba(0,0,0,.72);
    background-repeat: no-repeat;

  }
  .equalPadding{
    max-width: 1000px;
  }

  .blackHeading{
    font-weight: 700;
    font-size: 26px;
    line-height: 35.47px;
    color: #000;
  }

  .whiteHeading{
    font-weight: 700;
    font-size: 26px;
    line-height: 35.47px;
    color: #fff;
  }

.image_border{
border-radius: 0%;
 min-height: 138px;
}
    
.learnTrackTitle{
   color: black;
   font-size: 16px;
   font-weight: 700;
}
.boxshadow{
  border: none;
  border-radius: 0%;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12); 
}

.buttonPosition{
    bottom: 0px;
    position: absolute;
}

.viewbtn {
background-color: #057092 !important;
padding: 6px 20px;
border:none;
border-radius: 0px!important;
color: #fff;
}
      
.viewbtn:disabled{
 background-color: grey !important;
 padding: 6px 20px;
 border:none;
 border-radius: 0px!important;
 color: white!important;
}    
      
.execBlock{
    background-color: #057092 !important;
}

.lightBlueBg{
    background-color: #F9FAFA;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
}

.lightBlueBorder{
    background-color: #F9FAFA;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #ECECEC;
}

.blueBoldText{
    font-weight: 700;
    font-size: 20px;
    line-height: 27px;
    color: #057092;
}

.formLabelText{
    font-size: 15px;
    font-weight: 500;
}

.checkBox{
    height: 12px; 
    width: 18px;
    margin: 0px;
}

.disablePointer{
    pointer-events: none;
}

.blurBg{
    filter: blur(5px) brightness(0.5);
  
}

@media screen and (min-width:0px) and (max-width: 499px) {

    .paragraph{
        font-size: 14px;
    }
    
    .title{
        font-size: 24px;
    }
    
  
    .mobileFoldbg {
        background-image: url('../assets/isbcampus.jpg');
        background-size: cover;
        box-shadow: inset 0 0 0 1000px rgba(0,0,0,.4);
        background-repeat: no-repeat;
    
      }

}

@media screen and (min-width:500px) and (max-width: 976px){
    .paragraph{
        font-size: 14px;
    }
    
    .title{
        font-size: 24px;
    }
    

    .mobileFoldbg {
        background-image: url('../assets/isb_campus.svg');
        background-size: cover;
        box-shadow: inset 0 0 0 1000px rgba(0,0,0,.7);
        background-repeat: no-repeat;
    
      }
}