import React from "react";
import Link from "next/link";
import Image from "next/image";
import aboutimage from "../assets/aboutpage.png";
import { useRouter } from "next/router";
import classes from "./about-page.module.css";
import BottomFold from "../components/bottom_fold_one";
import Head from "next/head";

export const Aboutpage = (props) => {
  const data = props.apiData.data.attributes;
  const router = useRouter();
  const { query } = router;
  const qpms =
    query.utm_source != undefined
      ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`
      : ``;

   const schemaData =   {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": props.baseURL || "https://online.isb.edu/",
        "potentialAction": {
          "@type": "SearchAction",
          "target": `${props.baseURL}/search?q={search_term_string}`,
          "query-input": "required name=search_term_string"
        },
        "hasPart": [
           
          {
            "@type": "SiteNavigationElement",
            "position": 1,
            "name": "About Us",
            "url": `${props.baseURL+"/about-page"}`
          },
        ]}
          
  return (
    <>
    <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
      </Head>
      <div div className={`${classes.fold1bg}`}>
        <div className={`${classes.hideCrumb} mx-auto pt-lg-5 px-3 px-lg-0`}>
          <div className={`d-flex ${classes.breadCrumb} px-0`}>
            <Link style={{ textDecoration: "none" }} href={`/${qpms}`}>
              <p className="text-white m-0">ISB Online</p>
            </Link>
            <svg
              style={{ fill: "#ffffff" }}
              width="24"
              height="18"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_3275_9282)">
                <path d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z" />
              </g>
              <defs>
                <clipPath id="clip0_3275_9282">
                  <rect width="24" height="24" fill="#057092" />
                </clipPath>
              </defs>
            </svg>
            <p className={`text-white m-0 ${classes.breadCrumblast}`}>
              About Us
            </p>
          </div>
        </div>
        <div
          className={`row flex-row-reverse col-lg-12 m-0 mx-auto ${classes.equalPadding} py-0 pb-lg-5 py-md-0`}
        >
          <Image
            width="0"
            height="0"
            sizes="100vw"
            className="img-fluid col-lg-6 col-md-12 p-0"
            src={aboutimage}
            alt="aboutImage"
          />

          <div className="col-lg-6 px-lg-0 px-3 py-lg-0 py-4 col-md-12 align-items-center d-flex justify-content-center pe-lg-4">
            <div>
              <h1 className="text-white">
                <b>{data.about_page_title}</b>
              </h1>
              <p className={`my-3 ${classes.description}`}>
                {data.about_page_description}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className={`${classes.fold2bg}`}>
        <div className={`${classes.equalPadding} mx-auto`}>
          <div className={`${classes.div} py-4`}>
            <h2 className={`${classes.h1}`}>{data.second_fold_title}</h2>
            <p className="px-3 px-lg-0 m-0">{data.second_fold_description}</p>
            <p className="px-3 px-lg-0 m-0 pt-2 pb-2">
              {data.second_fold_description1}
            </p>
          </div>
        </div>
      </div>

      <div className={`${classes.fold3bg}`}>
        <div className={`${classes.equalPadding} mx-auto`}>
          <div className={`${classes.div}`}>
            <h2 className={`${classes.h1} mt-4`}>{data.third_fold_title}</h2>
            <p className="px-5 text-center mt-1">
              {data.third_fold_description}
            </p>
          </div>

          <div className={`${classes.equalPadding} mx-auto`}>
            {data.about_page_body.map((content, i) => {
              return (
                <>
                  <div
                    key={i}
                    className={`row py-lg-5 py-md-4 py-3 px-lg-0 px-3 ${
                      i % 2 === 0
                        ? "flex-lg-row-reverse flex-lg-row-reverse"
                        : ""
                    }`}
                  >
                    <div className="col-lg-5 col-md-5 col-12">
                      <Image
                          height={283}
                          width={380}
                         className="img-fluid"
                        src={
                          props.apiUrl + content.image.data[0].attributes.url
                        }
                        alt={
                          props.apiUrl +
                          content.image.data[0].attributes.alternativeText
                        }
                      />
                    </div>
                    <div className="col-lg-7 col-md-7 col-12 px-lg-4 px-md-4 mt-lg-0 mt-md-0 mt-3">
                      <h2 className={classes.contentTitle}>{content.title}</h2>
                      <p>{content.description}</p>
                    </div>
                  </div>
                </>
              );
            })}
          </div>
        </div>
      </div>
      <BottomFold data={props.bottomFoldData}></BottomFold>
    </>
  );
};
export async function getStaticProps(context) {
  const APIUrl = process.env.API_BASE_URL;
  const baseURL = process.env.NEXT_PUBLIC_BASE_URL;

  const [response, bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/about-page?populate[about_page_body][populate]=*`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      apiData: response,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData,
      baseURL:baseURL
    },
  };
}
export default Aboutpage;
