import countryCodes from '../public/countryCodes.json' 

export const GetLocation = async () => {
  if (!navigator.geolocation) {
    alert("Geolocation is not supported by your browser");
    return null;
  }

  return new Promise((resolve, reject) => {
    navigator.geolocation.getCurrentPosition( async (position) => {
        const { latitude, longitude } = position.coords;
        try {
          const response = await fetch(`https://api.openweathermap.org/geo/1.0/reverse?lat=${latitude}&lon=${longitude}&limit=1&appid=${process.env.NEXT_PUBLIC_OPENWEATHER_API_KEY}`);

          if (!response.ok) {
            reject('Failed to fetch geocoding data');
            return;
          }

          const data = await response.json();

          if (data && data.length > 0) {
            const city = data[0].name; 
            const state = data[0].state;
            const countryCode = data[0].country;
            const country = countryCodes[countryCode] || countryCode;
            resolve({city, state, country });
          } else {
            reject('Geocoding API error: No data returned');
          }
        } catch (error) {
          reject(`Error fetching data: ${error.message}`);
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        reject(`Unable to retrieve your location: ${error.message}`);
      }
    );
  });
};
