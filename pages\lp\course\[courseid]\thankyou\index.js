import Link from "next/link";
import Image from "next/image";
import classes from "../../../../me_thankyou.module.css";
import image from "../../../../../assets/heart.svg";
import BottomFold from "../../../../../components/bottom_fold_one";
import { NextSeo } from "next-seo";

export default function thankyouPage(props) {
  const landingPageResponse = props.apiData.data[0].attributes.brochure.data.attributes.url;
  const pdf =props.apiUrl+landingPageResponse

  const tqData = props.tqData.data.attributes;
  return (
    <>
          <NextSeo
          title={tqData.meta_thank.title}
        description={tqData.meta_thank.description}

        openGraph={{
          type: `website`,
          title:`${tqData.meta_thank.title}`,
        
          description:`${tqData.meta_thank.description}`,
          locale: "en",
          images: [
            {
              url :`${props.apiUrl + tqData.meta_thank.image.data.attributes.url}`,
              alt: "image.png",
            },
          ],
          site_name: `ISB Online`,
        }}
        twitter={{
          handle: "@handle",
          site: "@site",
          cardType: `${tqData.meta_thank.title}`,
        }}
        additionalMetaTags={[
          {
            name: "keywords",
            content:`${tqData.meta_thank.keywords}`
          },
          {
            name: "robots",
            content:`${tqData.meta_thank.robots}`
          },
        ]}
      />

      <div className={`${classes.linearGradient}`}>
        <div className={`${classes.equalPadding} mx-auto py-lg-5 py-4`}>
          <Image
            className="mx-auto d-block"
            height={55}
            src={image}
            alt="thankyou_message"
          />
          <p className={`text-center mt-4 ${classes.whiteHeading}`}>
            {tqData.fold_one_title}
          </p>
     
          <div className="text-center px-3 m-0" style={{ color: "white" }}>
              <span><a href={pdf} target="_blank" rel="noreferrer" className="text-white font-weight-bold" >Click here </a></span>
              <span> to download the brochure if you are unable to access the file.</span>
             </div>
        </div>
      </div>

      <div className="bg-white">
        <div className={`${classes.equalPadding} mx-auto`}>
          <h2 className={`text-center ${classes.blueHeading} pt-4 m-0 mb-3`}>
            {tqData.Insight_title} 
          </h2>

          <div className="row row-cols-1 row-cols-md-3 g-4">
            {tqData.insights.map((card, index) => (
              <div className="col" key={index}>
                <div
                  className={`card h-100 rounded-0 ${classes.boxshadow} mx-2`}
                >
                  <a
                    target="_blank"
                    rel="noreferrer"
                    href={`/${card.insight_url}`}
                    style={{ textDecoration: "none" }}
                  >
                    <Image
                      src={
                        props.apiUrl + card.insight_image.data.attributes.url
                      }
                      className="card-img-top h-auto rounded-0"
                      alt="image"
                      width="0"
                      height="0"
                      sizes="100vw"
                    />
                    <div className="card-body">
                      <p className="text-center">{card.insight_title}.</p>
                    </div>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="py-4"></div>
      </div>

      <div className={`${classes.fold1bg} col-12`}>
        <div
          className={`${classes.equalPadding} mx-auto ${classes.imagePadding}  `}
        >
          <p className={`${classes.text}`}>{tqData.third_fold_text} </p>

          <p className={`${classes.sub_text} text-center `}>
            {tqData.third_fold_subtext} 
          </p>

          <div className="text-center">
            <Link rel="canonical" href={`/course/${props.course_id}`}>
              <button className={`${classes.showBtn} `} type="submit">
                {tqData.btn_title}
              </button>
            </Link>
          </div>
        </div>
      </div>
      <BottomFold data={props.bottomFoldData}></BottomFold>
    </>
  );
}

export const getStaticPaths = async (context) => {
  const { req, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;
  const res = await fetch(`${APIUrl}/api/course-landingpages?populate=deep,3`);
  const response = await res.json();
  const paths = response.data.map((course) => {
    return {
      params: {
        courseid: course.attributes.course_id,
    
      },
    };
  });
  return {
    paths,
    fallback: false,
  };
};

export async function getStaticProps(context) {
  const meetupId = context.params.courseid;

  const APIUrl = process.env.API_BASE_URL;
  const [tracklandingpage, tqData, bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/course-landingpages?filters[course_id][$eq]=${meetupId}&populate=deep,3`
    ).then((r) => r.json()),

    fetch(`${APIUrl}/api/me-thankyou?populate=deep,3`).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);

  return {
    props: {
      apiData: tracklandingpage,
      tqData: tqData,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData,
      course_id:meetupId
    },
    revalidate: 120,
  };
}
