import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
import styles from './alltopics.module.css';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import TrackPageBottomFold from '../../components/TrackPageBottomFold';
import { TopicsComponentForSlug } from '../../components/PerspectiveComponents';
import TopicsDropdown from '../../components/TopicsDropdown';

export default function AllTopics(props) {
  const router = useRouter();
  const { topic } = router.query;
  const apiUrl = props.apiUrl;
  const [isLoading, setIsLoading] = useState(true);
  const dataProcessed = useRef(false);
  
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isTopicLoading = sessionStorage.getItem('isb_topic_loading') === 'true';
      
      if (isTopicLoading) {
        setIsLoading(true);
        sessionStorage.removeItem('isb_topic_loading');
        
        setTimeout(() => {
          setIsLoading(false);
        }, 100);
      } else {
        setIsLoading(false);
      }
    }
  }, []);
  

  const apiPosts = props.posts?.data?.attributes?.articles?.data || [];
  const apiPost = props.posts?.data?.attributes?.infographics?.data || [];
  const apiPodcast = props.posts?.data?.attributes?.podcasts?.data || [];
  const apiVideo = props.posts?.data?.attributes?.videos?.data || [];
  const allTopicsTitle = props.posts?.data?.attributes?.All_topics_title

  const combinedArray = useMemo(() => {
   
    return [
      ...apiPosts.map(post => ({
        id: post.id,
        attributes: {
          ...post.attributes.post_first_section,
          post_id: post.attributes.post_id,
          article_body: post.attributes.articles_body,
          topics: post.attributes.topics,
          post_type: 'article'
        }
      })),
      ...apiPost.map(post => ({
        id: post.id,
        attributes: {
          ...post.attributes.info_first_section,
          post_id: post.attributes.post_id,
          article_body: post.attributes.info_body,
          topics: post.attributes.topics,
          post_type: 'infographic'
        }
      })),
      ...apiPodcast.map(post => ({
        id: post.id,
        attributes: {
          ...post.attributes.podcaste_first_section,
          post_id: post.attributes.post_id,
          topics: post.attributes.topics,
          post_type: 'podcast'
        }
      })),
      ...apiVideo.map(post => ({
        id: post.id,
        attributes: {
          ...post.attributes.video_first_section,
          post_id: post.attributes.post_id,
          topics: post.attributes.topics,
          post_type: 'video'
        }
      }))
    ];
  }, [apiPosts, apiPost, apiPodcast, apiVideo]);

  const contentTypes = useMemo(() => {
    const typesSet = new Set();
    combinedArray.forEach(item => {
      if (item.attributes && item.attributes.post_type) {
        typesSet.add(item.attributes.post_type);
      }
    });
    return Array.from(typesSet);
  }, [combinedArray]);

  const [selectedTopics, setSelectedTopics] = useState([]);
  const [selectedContentTypes, setSelectedContentTypes] = useState([]);
  const [visibleCards, setVisibleCards] = useState(6);

  const [shouldUpdateUrl, setShouldUpdateUrl] = useState(false);
  const [isChangingFromUrl, setIsChangingFromUrl] = useState(false);
  
  useEffect(() => {
    if (router.isReady && !isChangingFromUrl) {
      setShouldUpdateUrl(true);
    }
  }, [selectedTopics, selectedContentTypes, router.isReady, isChangingFromUrl]);
  
  useEffect(() => {
    if (router.isReady) {
      setIsChangingFromUrl(true);
      
      const isDirectNavigation = typeof window !== 'undefined' && 
        localStorage.getItem('isb_direct_navigation') === 'true';
      
      if (isDirectNavigation) {
        
        const originalTopic = localStorage.getItem('isb_selected_topic');
        
        if (originalTopic) {
          setSelectedTopics([originalTopic]);
          
          localStorage.removeItem('isb_direct_navigation');
          localStorage.removeItem('isb_selected_topic');
          localStorage.removeItem('isb_formatted_topic');
          localStorage.removeItem('isb_topic_loading');
        }
      }
      else if (router.query.topic) {
        const topicFromUrl = decodeURIComponent(router.query.topic).replace(/\+/g, ' ').replace(/_/g, ' ');
        setSelectedTopics([topicFromUrl]);
      } else {
        setSelectedTopics([]);
      }
      
      if (router.query.type && router.query.type !== 'All') {
        const typeFromUrl = router.query.type.toLowerCase();
        setSelectedContentTypes([typeFromUrl]);
      } else {
        setSelectedContentTypes([]);
      }
      
      setTimeout(() => setIsChangingFromUrl(false), 0);
    }
  }, [router.isReady, router.query]);
  
  useEffect(() => {
    if (router.isReady && shouldUpdateUrl) {
      const query = {};
      
      if (selectedTopics.length === 1) {
        query.topic = selectedTopics[0].replace(/\s+/g, '_');
      }
      
      if (selectedContentTypes.length === 1) {
        query.type = selectedContentTypes[0].charAt(0).toUpperCase() + selectedContentTypes[0].slice(1);
      } else {
        query.type = 'All';
      }
      
      Object.keys(router.query).forEach(key => {
        if (key.startsWith('utm_')) {
          query[key] = router.query[key];
        }
      });
      
      const currentTopic = router.query.topic?.replace(/\+/g, ' ') || '';
      const currentType = router.query.type || 'All';
      const newTopic = query.topic?.replace(/\+/g, ' ') || '';
      const newType = query.type || 'All';

      if (currentTopic !== newTopic || currentType !== newType) {
        router.push({
          pathname: router.pathname,
          query: query
        }, undefined, { shallow: true });
      }
      
      setShouldUpdateUrl(false);
    }
  }, [shouldUpdateUrl, router.isReady]);

  const allTopics = useMemo(() => {
    const topicsSet = new Set();
    combinedArray.forEach(item => {
      if (item.attributes.topics && item.attributes.topics.data) {
        item.attributes.topics.data.forEach(topicItem => {
          if (topicItem.attributes && topicItem.attributes.topic_name) {
            topicsSet.add(topicItem.attributes.topic_name);
          }
        });
      }
    });
    const sortedTopics = Array.from(topicsSet).sort();
    return sortedTopics;
  }, [combinedArray]);

  const toggleTopic = (topicName) => {
    setSelectedTopics(prev => {
      if (prev.length === 1 && prev[0].toLowerCase() === topicName.toLowerCase()) {
        return [];
      } else {
        return [topicName];
      }
    });
  };

  const toggleContentType = (contentType) => {
    setSelectedContentTypes(prev => {
      if (prev.length === 1 && prev[0] === contentType) {
        return [];
      } else {
        return [contentType];
      }
    });
  };

 

  const filteredCards = useMemo(() => {
    if (selectedTopics.length === 0 && selectedContentTypes.length === 0 && !router.query.topic && (!router.query.type || router.query.type === 'All')) {
      return combinedArray;
    }
    let result = combinedArray;
    
    if (selectedContentTypes.length > 0) {
      result = result.filter(item => 
        selectedContentTypes.includes(item.attributes.post_type)
      );
    }
    
    if (selectedTopics.length > 0) {
      
      const normalizeTopic = (topicName) => {
        if (!topicName) return '';
        return topicName.trim().toLowerCase().replace(/[_\s]+/g, ' ');
      };
      
      const normalizedSelectedTopics = selectedTopics.map(normalizeTopic);
      
      result = result.filter(item => {
        if (!item.attributes || !item.attributes.topics || !item.attributes.topics.data) {
          return false;
        }
        
        const itemTopics = item.attributes.topics.data
          .filter(t => t.attributes && t.attributes.topic_name)
          .map(t => normalizeTopic(t.attributes.topic_name));
        
        const hasMatchingTopic = normalizedSelectedTopics.some(selectedTopic => 
          itemTopics.includes(selectedTopic));
          
        return hasMatchingTopic;
      });
      
    }
    
    if (selectedTopics.length === 0 && router.query.topic) {
      const topicFromUrl = decodeURIComponent(router.query.topic)
        .replace(/\+/g, ' ')
        .replace(/_/g, ' ')
        .trim()
        .toLowerCase()
        .replace(/\s+/g, ' ');
        
      
      result = result.filter(item => {
        if (!item.attributes.topics || !item.attributes.topics.data || !Array.isArray(item.attributes.topics.data)) {
          return false;
        }
        
        return item.attributes.topics.data.some(t => {
          if (!t.attributes || !t.attributes.topic_name) return false;
          
          const normalizedTopicName = t.attributes.topic_name.trim().toLowerCase().replace(/\s+/g, ' ');
          return normalizedTopicName === topicFromUrl;
        });
      });
    }
    
    return result;
  }, [combinedArray, selectedTopics, selectedContentTypes, router.query.topic]);

  const filteredCount = filteredCards.length;
  
  const hasMoreCards = filteredCards.length > visibleCards;

  const calculateReadingTime = (articleBody) => {
    if (!articleBody || !Array.isArray(articleBody)) return 0;
    
    let totalWords = 0;
    const wordsPerMinute = 200;
    
    articleBody.forEach(section => {
      if (section.description) {
        const plainText = section.description.replace(/<[^>]*>/g, '');
        totalWords += plainText.trim().split(/\s+/).length;
      }
    });

    return Math.ceil(totalWords / wordsPerMinute);
  };

  const toTitleCase = (str) => {
    return str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const handleCardClick = (post) => {
    const slug = post.attributes.post_id;
    const postType = post.attributes.post_type;
    
    const queryParams = {};
    
    Object.keys(router.query).forEach(key => {
      if (key.startsWith('utm_')) {
        queryParams[key] = router.query[key];
      }
    });
    
    const route = `/perspectives/${postType}/${slug}`;
    
    router.push({
      pathname: route,
      query: queryParams
    });
  };

  const renderCard = (post, index) => {
    const postData = post.attributes;
    const readingTime = calculateReadingTime(postData.article_body);
    return (
      <div key={index} className="col-md-4">
        <div 
          className={`${styles.card} ${styles.verticalCard} ${styles.clickable}`}
          onClick={() => handleCardClick(post)}
          role="button"
          tabIndex={0}
        >
          <div className={styles.imageWrapper}>
            {postData.home_page_image?.data && (
              <Image
                src={`${apiUrl}${postData.home_page_image.data.attributes.url}`}
                alt={postData.post_name}
                fill
                priority
                className={styles.image}
              />
            )}
            {postData.category_icon?.data && (
              <div className={styles.iconWrapper}>
                <Image
                  src={`${apiUrl}${postData.category_icon.data.attributes.url}`}
                  alt="Category"
                  width={20}
                  height={20}
                />
              </div>
            )}
          </div>
          <div className={styles.cardContent}>
            {/* <p className={styles.category}>{postData.subtext}</p> */}
            <TopicsComponentForSlug topiclick={true} Topics={postData.topics.data} />

            <h3 className={styles.title}>{postData.post_name}</h3>
            <p className={styles.description}>{postData.short_summary.length>200 ? postData.short_summary.slice(0, 200)+ "..." :postData.short_summary}</p>
            
            <div className={styles.metaInfo}>
              <span>{postData.author_name}</span>
              {postData.date && <span>{postData.date}{readingTime > 0 && ` | ${readingTime} Min`}</span>}
            </div>
          </div>
      </div>
        </div>
    );
  };

  const loadMoreCards = () => {
    setVisibleCards(prevCount => prevCount + 6);
  };

  // Loading skeleton UI component
  const LoadingSkeleton = () => (
    <div className="container-fluid">
      <div className="row mt-4">
        {[1, 2, 3, 4, 5, 6].map(index => (
          <div key={index} className="col-md-4 mb-4">
            <div className={`${styles.card} ${styles.verticalCard} ${styles.skeleton}`}>
              <div className={styles.imageWrapper} style={{ backgroundColor: '#f0f0f0', height: '250px' }}></div>
              <div className={styles.cardContent}>
                <div className={`${styles.skeletonLine} ${styles.skeletonTopics}`}></div>
                <div className={`${styles.skeletonLine} ${styles.skeletonTitle}`}></div>
                <div className={`${styles.skeletonLine} ${styles.skeletonDescription}`}></div>
                <div className={`${styles.skeletonLine} ${styles.skeletonMetaInfo}`}></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Check for any matching content in dev tools
  useEffect(() => {
    // This runs client-side to diagnose if any topics are actually matching
    if (typeof window !== 'undefined' && router.isReady && router.query.topic) {
      const decodedTopic = decodeURIComponent(router.query.topic).replace(/_/g, ' ');
      
      // Check if there are any items at all with this topic
      const topicMatches = combinedArray.filter(item => {
        if (!item.attributes.topics || !item.attributes.topics.data) return false;
        
        return item.attributes.topics.data.some(t => {
          if (!t.attributes || !t.attributes.topic_name) return false;
          const normalizedName = t.attributes.topic_name.trim().toLowerCase();
          const normalizedSearchTopic = decodedTopic.trim().toLowerCase();
          return normalizedName === normalizedSearchTopic;
        });
      });
      
       
    }
  }, [combinedArray, router.isReady, router.query.topic]);

  return (
    <div className={styles.allTopicshead}>
      
      <div className="container-fluid">
        <Breadcrumb
          items={[
        
            { label: 'Perspectives', href: '/perspectives' },
            { label: 'All Topics', href: '/perspectives/alltopics' }
          ]}
        />
      </div>
      <div className={`${styles.alltopicsmain} " mt-0 mt-md-5 pb-5"`}>
        
        <div className={styles.alltopicsfirstdiv}>
          <div>
            <h1 className={styles.pageTitle}>
              {selectedTopics.length === 1
                ? selectedTopics[0]
                : allTopicsTitle || 'All Topics'}
            </h1>
          </div>
          <div>
            <span className={styles.resultsCount}>
              {filteredCards.length} {filteredCards.length === 1 ? 'Result' : 'Results'}
            </span>
          </div>
        </div>
        {/* Mobile filters - only visible on small screens */}
        <div className="d-md-none mb-4 w-100">
          <MobileFilters 
            selectedContentTypes={selectedContentTypes}
            setSelectedContentTypes={setSelectedContentTypes}
            contentTypes={contentTypes}
            toggleContentType={toggleContentType}
            toTitleCase={toTitleCase}
            selectedTopics={selectedTopics}
            setSelectedTopics={setSelectedTopics}
            allTopics={allTopics}
            toggleTopic={toggleTopic}
          />
        </div>

        <div className="row">
          {/* Left Filter Sidebar - only visible on desktop */}
          <div className="col-md-3 d-none d-md-block">
            {ContentFilterationSection(selectedContentTypes, setSelectedContentTypes, contentTypes, toggleContentType, toTitleCase, selectedTopics, setSelectedTopics, allTopics, toggleTopic)}
          </div>
          {/* Main Content */}
          <div className="col-md-9">
          
           
            <div className="row g-4">
              {isLoading ? (
                <LoadingSkeleton />
              ) : (
                filteredCards.length > 0 ? (
                  filteredCards.slice(0, visibleCards).map((post, index) => renderCard(post, index))
                ) : (
                  <div className={styles.noResultsMessage}>
                    <p>There is no published content available to show for the selected topic.</p>
                  </div>
                )
              )}
            </div>

            {filteredCards.length > visibleCards && (
              <div className="text-center mt-4">
                <button 
                  className={`btn ${styles.loadMoreBtn}`} 
                  onClick={loadMoreCards}
                >
                  LOAD MORE
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      <TrackPageBottomFold faqsection={false} apiUrl={apiUrl} />

    </div>
  );
}

// Desktop filter sidebar component
function ContentFilterationSection(selectedContentTypes, setSelectedContentTypes, contentTypes, toggleContentType, toTitleCase, selectedTopics, setSelectedTopics, allTopics, toggleTopic) {
  return <div className={styles.filterSidebar}>
      <div className={`${styles.filterSection} ${styles.contentTypeFilters}`}>
        <h3>CONTENT TYPE</h3>
        <ul className={styles.filterList}>
          <li>
            <span 
              className={`${styles.filterItem} ${selectedContentTypes.length === 0 ? styles.selectedFilter : ''}`}
              onClick={() => {
                setSelectedContentTypes([]);
              }}>
              All Content
            </span>
          </li>
          {contentTypes.map(type => (
            <li key={type}>
              <span 
                className={`${styles.filterItem} ${selectedContentTypes.includes(type) ? styles.selectedFilter : ''}`}
                onClick={() => toggleContentType(type)}>
                {toTitleCase(type)}s
              </span>
            </li>
          ))}
        </ul>
      </div>

      <div className={styles.filterSection}>
        <h3>TOPICS</h3>
        <ul className={styles.filterList}>
          <li>
            <span 
              className={`${styles.filterItem} ${selectedTopics.length === 0 ? styles.selectedFilter : ''}`}
              onClick={() => {
                setSelectedTopics([]);
              }}>
              All Topics
            </span>
          </li>
          {allTopics.map(topicName => (
            <li key={topicName}>
              <span 
                className={`${styles.filterItem} ${selectedTopics.some(t => t.toLowerCase() === topicName.toLowerCase()) ? styles.selectedFilter : ''}`}
                onClick={() => toggleTopic(topicName)}>
                {topicName}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>;
}

// Mobile filter dropdown component
function MobileFilters({ selectedContentTypes, setSelectedContentTypes, contentTypes, toggleContentType, toTitleCase, selectedTopics, setSelectedTopics, allTopics, toggleTopic }) {
  const [contentTypeDropdownOpen, setContentTypeDropdownOpen] = useState(false);
  const [topicsDropdownOpen, setTopicsDropdownOpen] = useState(false);
  
  // Get the display name for content type dropdown button
  const getContentTypeDisplayName = () => {
    if (selectedContentTypes.length === 0) return 'All Content';
    return toTitleCase(selectedContentTypes[0]) + 's';
  };
  
  // Get the display name for topics dropdown button
  const getTopicDisplayName = () => {
    if (selectedTopics.length === 0) return 'All Topics';
    return selectedTopics[0];
  };
  
  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest(`.${styles.mobileFilterDropdown}`)) {
        setContentTypeDropdownOpen(false);
        setTopicsDropdownOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  return (
    <div className={styles.mobileFiltersContainer}>
      {/* Content Type Dropdown */}
      <div className="row">
        <div className={`${styles.mobileFilterDropdown} col-4 pe-0`}>
          <div
            className={styles.mobileFilterButton}
            onClick={() => {
              setContentTypeDropdownOpen(!contentTypeDropdownOpen);
              setTopicsDropdownOpen(false);
            }}
          >
            <span>{getContentTypeDisplayName()}</span>
          </div>
        
          {contentTypeDropdownOpen && (
            <div className={`${styles.first_drpdwn} ${styles.mobileFilterContent}`}>
              <ul className={styles.mobileFilterList}>
                <li
                  className={`${styles.mobileFilterItem} ${selectedContentTypes.length === 0 ? styles.mobileSelectedFilter : ''}`}
                  onClick={() => {
                    setSelectedContentTypes([]);
                    setContentTypeDropdownOpen(false);
                  }}
                >
                  All Content
                </li>
                {contentTypes.map(type => (
                  <li
                    key={type}
                    className={`${styles.mobileFilterItem} ${selectedContentTypes.includes(type) ? styles.mobileSelectedFilter : ''}`}
                    onClick={() => {
                      toggleContentType(type);
                      setContentTypeDropdownOpen(false);
                    }}
                  >
                    {toTitleCase(type)}s
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
        
        {/* Topics Dropdown */}
        <div className={`${styles.mobileFilterDropdown} col`}>
          <div
            className={styles.mobileFilterButton}
            onClick={() => {
              setTopicsDropdownOpen(!topicsDropdownOpen);
              setContentTypeDropdownOpen(false);
            }}
          >
            <span>{getTopicDisplayName()}</span>
          </div>
        
          {topicsDropdownOpen && (
            <div className={styles.mobileFilterContent}>
              <ul className={styles.mobileFilterList}>
                <li
                  className={`${styles.mobileFilterItem} ${selectedTopics.length === 0 ? styles.mobileSelectedFilter : ''}`}
                  onClick={() => {
                    setSelectedTopics([]);
                    setTopicsDropdownOpen(false);
                  }}
                >
                  All Topics
                </li>
                {allTopics.map(topicName => (
                  <li
                    key={topicName}
                    className={`${styles.mobileFilterItem} ${selectedTopics.some(t => t.toLowerCase() === topicName.toLowerCase()) ? styles.mobileSelectedFilter : ''}`}
                    onClick={() => {
                      toggleTopic(topicName);
                      setTopicsDropdownOpen(false);
                    }}
                  >
                    {topicName}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export async function getServerSideProps({ query }) {
  try {
    
    // Get environment variables with fallbacks to null (not undefined)
    const APIUrl = process.env.API_BASE_URL || null;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || null;
    const lxpStagingUrl = process.env.LXP_STAGING_URL || null;
    const lxpProdUrl = process.env.LXP_PROD_URL || null;

    // Generate a cache key that depends on the query parameters
    // This allows caching different filtered views separately
    const topicParam = query.topic ? query.topic.toLowerCase() : '';
    const typeParam = query.type ? query.type.toLowerCase() : 'all';
    const cacheKey = `edge-main-page-data-${topicParam}-${typeParam}`;
    
    let posts;
    // Increase cache time to 15 minutes for better performance
    const CACHE_TIME = 15 * 60 * 1000; // 15 minutes
    
    // Check if we have cached data for this specific query
    if (global.__edgePageCache && 
        global.__edgePageCache[cacheKey] && 
        global.__edgePageCacheTime && 
        global.__edgePageCacheTime[cacheKey] &&
        (Date.now() - global.__edgePageCacheTime[cacheKey]) < CACHE_TIME) {
      // Use cached data if it's still fresh
      posts = global.__edgePageCache[cacheKey];
    } else {
      // Check if we have a base cache that we can use regardless of filters
      const baseCacheKey = 'edge-main-page-data-base';
      
      if (!query.topic && !query.type && 
          global.__edgePageCache && 
          global.__edgePageCache[baseCacheKey] && 
          global.__edgePageCacheTime && 
          global.__edgePageCacheTime[baseCacheKey] && 
          (Date.now() - global.__edgePageCacheTime[baseCacheKey]) < CACHE_TIME) {
        posts = global.__edgePageCache[baseCacheKey];
        
        global.__edgePageCache[cacheKey] = posts;
        global.__edgePageCacheTime[cacheKey] = Date.now();
        
      } else {
        const populate = [
          'articles.post_first_section.post_name',
          'articles.post_first_section.short_summary',
          'articles.post_first_section.author_name',
          'articles.post_first_section.home_page_image',
          'articles.post_first_section.category_icon',
          'articles.articles_body',
          'articles.post_id',
          'articles.topics',
          
          'infographics.info_first_section.post_name',
          'infographics.info_first_section.short_summary',
          'infographics.info_first_section.author_name',
          'infographics.info_first_section.home_page_image',
          'infographics.info_first_section.category_icon',
          'infographics.info_body',
          'infographics.post_id',
          'infographics.topics',
          // Podcasts
          'podcasts.podcaste_first_section.post_name',
          'podcasts.podcaste_first_section.short_summary',
          'podcasts.podcaste_first_section.author_name',
          'podcasts.podcaste_first_section.home_page_image',
          'podcasts.podcaste_first_section.category_icon',
          'podcasts.post_id',
          'podcasts.topics',
          
          'videos.video_first_section.post_name',
          'videos.video_first_section.short_summary',
          'videos.video_first_section.author_name',
          'videos.video_first_section.home_page_image',
          'videos.video_first_section.category_icon',
          'videos.post_id',
          'videos.topics',
          
          'articles.topics.data',
          'infographics.topics.data',
          'podcasts.topics.data',
          'videos.topics.data',
          
          'All_topics_title',
          'meta_tags',
          'meta_tags.image',
          'meta_tags.image.data.attributes.url',
          'meta_tags.title',
          'meta_tags.description',
          'meta_tags.keywords',
          'meta_tags.robots'
        ].join(',');
        
        const response = await fetch(`${APIUrl}/api/edge-main-page?populate=${populate}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        posts = await response.json();
        
        global.__edgePageCache = global.__edgePageCache || {};
        global.__edgePageCacheTime = global.__edgePageCacheTime || {};
        
        global.__edgePageCache[cacheKey] = posts;
        global.__edgePageCacheTime[cacheKey] = Date.now();
        
        if (!query.topic && !query.type) {
          global.__edgePageCache[baseCacheKey] = posts;
          global.__edgePageCacheTime[baseCacheKey] = Date.now();
        }
      }
    }

    
    if (posts?.data?.attributes) {
      const apiPosts = posts.data.attributes.articles?.data || [];
      const apiPost = posts.data.attributes.infographics?.data || [];
      const apiPodcast = posts.data.attributes.podcasts?.data || [];
      const apiVideo = posts.data.attributes.videos?.data || [];
      
      
      // Sample the first few items and their topics
      const sampleItems = [...apiPosts, ...apiPost, ...apiPodcast, ...apiVideo].slice(0, 3);
      
      sampleItems.forEach((item, index) => {
        if (item.attributes && item.attributes.topics && item.attributes.topics.data) {
          
          const topicNames = item.attributes.topics.data
            .filter(t => t.attributes && t.attributes.topic_name)
            .map(t => t.attributes.topic_name);
        }
      });
    }
    
    return {
      props: {
        apiUrl: APIUrl,
        baseURL: baseUrl,
        lxpStagingUrl: lxpStagingUrl,
        lxpProdUrl: lxpProdUrl,
        posts: posts,
        perspectivesMetaData: posts,
      },
    };
  } catch (error) {
    console.error('Error in getServerSideProps:', error);
    return {
      props: {
        apiUrl: null,
        baseURL: null,
        lxpStagingUrl: null,
        lxpProdUrl: null,
        posts: null,
        error: 'Failed to load data'
      },
    };
  }
}
