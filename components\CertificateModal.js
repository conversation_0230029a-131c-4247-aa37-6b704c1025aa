'use client'
import Image from "next/image";
import classes from "../pages/v2/[learning_track_id]/index.module.css";
import { useEffect } from "react";

export const CertificateModal = (props) => {
  const { handleCloseModal, apiUrl, imgData, certificateModal } = props;
  
  let data = imgData.Certificate_Image?.data.attributes || imgData.certificate_image?.data.attributes;
  const title = imgData.title?.split(" ").includes("Badges");
  
  // Example Base64 placeholder (you should replace this with an actual base64 string)
  const blurDataURL = 'data:image/jpeg;base64,...'; // Replace with a real base64 image string
  
  useEffect(() => {
    const handleKeydown = (e) => {
      if (e.key === 'Escape' || e.key === 'Esc' || e.key === 27) {
        handleCloseModal();
      }
    };
    document.addEventListener('keydown', handleKeydown);
    return () => document.removeEventListener('keydown', handleKeydown);
  }, [handleCloseModal]);
  
  return (
    <div>
      {data && (
        <div className={`${classes.container} d-flex align-items-center`}>
          <div className={classes.video_container}>
            <div className={`${classes.popup_video} ${certificateModal ? 'd-block' : 'd-none'}`}>
              <Image
                priority={true}
                sizes="(max-width: 700px) 30vw, 100vw"
                alt={imgData.title}
                height={title ? (data.width / 1.8) : (data.height / 3.5)}
                width={title ? (data.width / 2) : (data.width / 3.5)}
                src={apiUrl + data.url}
                placeholder="blur"             // Add this prop
                blurDataURL={blurDataURL}       // Add this prop
                className="bg-transparent position-absolute top-50 start-50 translate-middle"
              />
              <span onClick={() => handleCloseModal()}>&times;</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
