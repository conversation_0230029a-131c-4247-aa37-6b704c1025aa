import React from 'react';
import styles from './DataTable.module.css';


const DataTable = ({
  headers = [],
  data = [],
  loading = false,
  error = null,
  emptyMessage = "No data available.",
  className = "",
  responsive = true,
  cellRenderer,
  description,
  footer,
}) => {
  // Default cell renderer that handles emails
  const defaultCellRenderer = (row, key, value, cellIndex) => {
    // Check if the key contains 'email' OR if the header at the same index contains 'email'
    // OR if the value looks like an email address
    const isEmailValue = typeof value === 'string' && 
                         /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
    
    const keyHasEmail = key.toLowerCase().includes('email');
    
    // Also check if the corresponding header is 'Email' (case-insensitive)
    const headerMayBeEmail = headers[cellIndex] && 
                            headers[cellIndex].toLowerCase().includes('email');
    
    if ((keyHasEmail || headerMayBeEmail || isEmailValue) && value) {
      return (
        <td key={cellIndex}>
          <a href={`mailto:${value}`} className={styles.emailLink}>
            {value}
          </a>
        </td>
      );
    }
    
    // Standard field rendering
    return <td key={cellIndex}>{value}</td>;
  };

  // Use the provided cellRenderer or fall back to the default one
  const renderCell = cellRenderer || defaultCellRenderer;

  const tableContent = () => {
    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading data...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className={`alert alert-danger ${styles.errorMessage}`} role="alert">
          Error loading data: {error}
        </div>
      );
    }

    if (!data || data.length === 0) {
      return (
        <div className={`alert alert-info ${styles.emptyMessage}`}>
          {emptyMessage}
        </div>
      );
    }

    return (
      <table className={`table table-bordered ${styles.dataTable} ${className}`}>
        <thead>
          <tr>
            {headers.map((headerText, index) => (
              <th key={index}>{headerText}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => {
            // Get the keys for this row in the order they appear in the original data
            const keys = Object.keys(row);
            
            return (
              <tr key={index}>
                {/* Dynamically render each cell based on its type and value */}
                {keys.map((key, cellIndex) => {
                  const value = row[key];
                  return renderCell(row, key, value, cellIndex);
                })}
              </tr>
            );
          })}
        </tbody>
      </table>
    );
  };

  return (
    <div className={styles.tableContainer}>
      {description && (
        <div className={styles.tableDescription}>
          <p>{description}</p>
        </div>
      )}
      
      {responsive ? (
        <div className="table-responsive">
          {tableContent()}
        </div>
      ) : (
        tableContent()
      )}
      
      {footer && (
        <div className={styles.tableFooter}>
          {footer}
        </div>
      )}
    </div>
  );
};

export default DataTable;
