import Link from "next/link";
import Image from "next/image";
import classes from "./me_thankyou.module.css";
import image from "../assets/heart.svg";
import BottomFold from "../components/bottom_fold_one";

export default function thankyouPage(props) {

  const tqData = props.tqData.data.attributes;

  return (
    <>
      <div className={`${classes.linearGradient}`}>
        <div className={`${classes.equalPadding} mx-auto py-lg-5 py-4`}>
          <Image
            className="mx-auto d-block"
            height={55}
            src={image}
            alt="thankyou_message"
          />
          <p className={`text-center mt-4 ${classes.whiteHeading}`}>
            {tqData.fold_one_title}
          </p>
          <p className="text-center px-3 m-0" style={{ color: "white" }}>
            {tqData.fold_description} 
          </p>
        </div>
      </div>

      <div className="bg-white">
        <div className={`${classes.equalPadding} mx-auto`}>
          <h2 className={`text-center ${classes.blueHeading} pt-4 m-0`}>
            {tqData.Insight_title} 
          </h2>

          <div className="row row-cols-1 row-cols-md-3 g-4">
            {tqData.insights.map((card, index) => (
              <div className="col" key={index}>
                <div
                  className={`card h-100 rounded-0 ${classes.boxshadow} mx-2`}
                >
                  <a  href={`/${card.insight_url}`} style={{textDecoration:"none"}} >
                  <Image
                   src={props.apiUrl + card.insight_image.data.attributes.url}
                    className="card-img-top h-auto rounded-0"
                    alt="image"
                    width="0"
                    height="0"
                    sizes="100vw"
                  />
                  <div className="card-body" >
                    <p className="text-center">{card.insight_title}.</p>
                  </div>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="py-4"></div>
      </div>

      <div className={`${classes.fold1bg} col-12`}>
        <div
          className={`${classes.equalPadding} mx-auto ${classes.imagePadding}  `}
        >
          <p className={`${classes.text}`}>{tqData.third_fold_text} </p>

          <p className={`${classes.sub_text} text-center `}>
            {tqData.third_fold_subtext} 
          </p>

          <div className="text-center">
            <Link rel="canonical" href="">
              <button className={`${classes.showBtn} `} type="submit">
                {tqData.btn_title}
              </button>
            </Link>
          </div>
        </div>
      </div>
      <BottomFold data={props.bottomFoldData}></BottomFold>
    </>
  );
}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;
  const [tqData, bottomFoldData] = await Promise.all([
    fetch(`${APIUrl}/api/me-thankyou?populate=deep,3`).then((r) =>
      r.json()
    ),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      tqData: tqData,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData,
    },
    revalidate: 120,
  };
}
