.thankyoumain{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 40px 18px;
    background-color: #fff;
    margin: auto;
    gap: 20px;
    max-width: 400px;
  }

.check_mark {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color:var(--isb-blue-color);
    position: relative;
  }
.thankyoutitle{
    display: flex;
    gap: 10px;
    flex-direction: column;
}
 .thankyoutitle h1 {
    font-family: Open Sans;
font-size: 20px;
font-weight: 700;
line-height: 27px;
text-align: center;
color: var(--isb-blue-color);

}
.thankyoutitle h2{
    font-family: Open Sans;
    font-size: 16px;
    font-weight: 700;
    line-height: 21px;
    text-align: center;
    color: var(--isb-blue-color);

  }
  .check_mark::after {
    content: '';
    position: absolute;
    top: 55%;
    left: 38%;
    width: 17px;
    height:32px;
    border: 6px solid white;
    border-bottom-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top: none;
    border-left: none;
    transform: translate(-100%, -50%);
    rotate : 45deg;
    
  }
  .text_icon_section{
    display: flex;
    text-align: left;
    gap: 10px;
  }
  .heading{
    cursor: pointer;
  }
  .text_icon_section .textSection h6{
    color: var(--isb-blue-color);
    padding-top: 5px;
    font-size: 16px;
    font-weight: 600;
    line-height: 21px;
    text-align: left;
    margin-bottom: 0;

  }
  .disabled_title {
    pointer-events: none;
    opacity: 0.7;
    cursor: not-allowed; 
}
  .text_icon_section .textSection h6:hover{
    text-decoration: underline;
    text-underline-offset: 2px;
  }
  .text_icon_section span{
font-size: 14px;
font-weight: 400;
line-height: 21px;
text-align: left;

  }
  .download_brchr_btn{
    width: 100%;
height:46px;
padding: 10px 20px;
gap: 10px;
border-radius: 5px;
background: rgba(5, 112, 146, 1);
font-family: Open Sans;
font-size: 20px;
font-weight: 700;
line-height: 25.65px;
text-align: left;
color: white;
border: none;
text-align: center;
text-decoration: none;
}
.download_brchr_btn:hover{
    color: white;
}
.purpleapplicationBtn:hover {
    text-decoration: none;
    background-color: var(--isb-purple-color) !important;
    color: white;
    }
    .purpleapplicationBtn {
      background-color: var(--isb-purple-color) !important;
      padding: 8px 48px;
      display: flex;
      color: #FFFFFF;
      border: none;
      border-radius: 0px !important;
      margin-bottom: 5px;
      width: 100%;
      justify-content: center;
    }
@media screen and (max-width:576px){
    .thankyoumain{
        padding: 15px 0px;
      }
}