import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Image from "next/image";
import classes from "./finance.module.css"
import classNames from "../components/bottom_fold_one.module.css"
import appLogo from "../assets/ISB_Online Logo.png";
import london from "../assets/London_logo.svg";
import arrowright from "../assets/arrow-right.png";
import helpcircle from "../assets/help-circle.png";
import kellog from "../assets/kellogg_logo.svg";
import wharton from "../assets/Wharton_logo.svg";
import fletcher from "../assets/Fletcher_logo.svg";
import aacsb from "../assets/aacsb_logo.svg";
import amba from "../assets/amba_logo.svg";
import equis from "../assets/equis_logo.svg";
import '@react-pdf-viewer/core/lib/styles/index.css';
import Link from 'next/link';
import DataTable from '../components/DataTable';
import BottomFold from '../components/bottom_fold_one';



const Finance = ({ financeTableData, bottomFoldData }) => {
  const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
  const [financingData, setFinancingData] = useState({ 
    header: [], 
    data: [], 
    description: "", 
    footer_text: "",
    finance_options_title:''
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const processFinancingData = () => {
      try {
        setLoading(true);
        
        // Use data from props instead of fetching
        const data = financeTableData;
        
        // Handle both the new format (with header/data) and legacy format
        if (data.data.attributes.table) {
          // Extract header, data, description and footer_text from API response
          const { header, data: tableData } = data.data.attributes.table;
          const { description, footer_text } = data.data.attributes;
          
          setFinancingData({
            header: header || [],
            data: tableData || [],
            description: description || '',
            footer_text: footer_text || '',
            finance_options_title:data.data.attributes.finance_options_title
          });
        } else {
          // Fallback for empty data
          setFinancingData({
            header: [], 
            data: [], 
            description: '',
            footer_text: '',
            finance_options_title:''
          });
        }
      } catch (err) {
        console.error('Error processing financing data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    // Call processFinancingData instead of fetchFinancingData
    processFinancingData();
  }, [financeTableData]);
  
  // Debug - check if data is loaded correctly
  useEffect(() => {
  }, [financingData]);

 
  return (
    <div className="container-fluid px-0 bg-white">
      <Head>
        <title>Financing Options - ISB</title>
        <meta name="description" content="Financing options for ISB programs" />
      </Head>
      
      <div className={`${classes.container_ee}`}>
        <header className={`${classes.header}`}>
          <div className={`${classes.header__logo_box_wrapper}`}>
            <div className={`${classes.header__logo_box}`}>
              <Image width={270} height={60} src={appLogo} alt="ISB Logo" />
            </div>
          </div>
        </header>
        <div className={`${classes.bc_and_menu}`}>
          <div className={`${classes.bc_and_menu__breadcrumb}`}>
          </div>
          <div className={`${classes.bc_and_menu__menu}`}>
            <span className={`${classes.bc_and_menu__menu_item}`}>

            </span>
            <span className={`${classes.bc_and_menu__menu_item}`}>

            </span>
          </div>
        </div>
      </div>
      <main>
        <div className={`${classes.financing_banner} d-flex justify-content-start align-items-center`}>
          <div className={`${classes.sub_banner_main}`}>
            <h3 className='ps-0 d-flex d-md-block justify-content-center justify-content-md-start ps-md-4 ps-lg-0'>{financingData.finance_options_title}
            </h3>
          </div>
        </div>

        <div className={`${classes.contentext} container-md pt-4 pb-3 pt-md-5 pb-md-4 px-md-0 px-3`}>
          {/* <div className="container p-0 m-0 d-flex justify-content-center"> */}
          <div className={classes.contentext}>
            <DataTable 
              headers={financingData.header || []}
              data={financingData.data || []}
              loading={loading}
              error={error}
              emptyMessage="No financing institutions found. Please check back later."
              className={classes.financeTable}
              responsive={true}
              description={financingData.description}
              footer={
                <p className="mb-0">
                  {financingData.footer_text ? (
                    financingData.footer_text.includes('@') ? (
                      <>
                        {/* Extract the text before the email */}
                        {financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}
                        {/* Extract the full email address */}
                        <a href={`mailto:${financingData.footer_text.match(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}`} className='text-decoration-none'>
                          {financingData.footer_text.match(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}
                        </a>
                        {/* Extract the text after the email */}
                        {financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/).length > 1 ? 
                          financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[1] : ''}
                      </>
                    ) : financingData.footer_text
                  ) : 'For any further clarifications, you may write to us at '}
                  {!financingData.footer_text || !financingData.footer_text.includes('@') ? 
                    <a href="mailto:<EMAIL>" className='text-decoration-none'><EMAIL></a> : null
                  }
                </p>
              }
            />
          </div>
        </div>

      </main>

      <div className=" ">
        <footer>
          <div className={`container-fluid px-0 ${classes.footer_content_container}`}>
            <div className={`${classes.logo_wrapper_main} mx-md-auto gap-md-0 gap-4 row`}>

              <div className={`col-lg-6 col-sm-12 gap-4 d-flex flex-column px-md-0 px-4`}>
                <Link href="/faq-page" className='text-decoration-none'>
                  <div className='d-flex gap-3 align-items-center justify-content-between justify-content-md-start justify-content-sm-start' >
                    <div className='d-flex gap-3 align-items-center' >
                      <Image src={helpcircle} alt='Help' />
                      <p className='mb-0 fs-5 fw-bold'>Frequently Asked Questions</p>
                    </div>
                    <Image src={arrowright} alt='arrow' />
                  </div>
                </Link>
                <a href="mailto:<EMAIL>" className='text-decoration-none'>
                  <div className='d-flex gap-3 align-items-center justify-content-between justify-content-md-start justify-content-sm-start'>
                    <div className='d-flex gap-3 align-items-center'>
                      <svg
                        fill="#ffffff"
                        width="32"
                        height="32"
                        viewBox="0 0 512 512"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>mail</title>
                        <path d="M64 128Q64 113 73 105 81 96 96 96L416 96Q431 96 440 105 448 113 448 128L448 144 256 272 64 144 64 128ZM256 328L448 200 448 384Q448 416 416 416L96 416Q64 416 64 384L64 200 256 328Z" />
                      </svg>
                      <p className='mb-0 fs-5 fw-bold'>Contact Us<br /><span style={{ fontSize: "0.9em", fontWeight: "400" }}><EMAIL></span> </p>
                    </div>
                    <Image src={arrowright} alt='' />

                  </div>
                </a>
              </div>
              <div className={`col-lg-6 col-sm-12 px-md-0 ${classes.footer_logo_wrpr_main}  justify-content-md-end w-sm-100 justiy-content-evenly justiy-content-sm-start`}>
                <ul className={`${classes.footer_logo_wrpr}`}>
                  <a
                    aria-label="facebook"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={'https://www.facebook.com/ISBeduOnline'}
                  >
                    {" "}
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10Z"
                        fill="white"
                      />
                      <path
                        d="M15 10C15 7.25 12.75 5 10 5C7.25 5 5 7.25 5 10C5 12.5 6.8125 14.5625 9.1875 14.9375V11.4375H7.9375V10H9.1875V8.875C9.1875 7.625 9.9375 6.9375 11.0625 6.9375C11.625 6.9375 12.1875 7.0625 12.1875 7.0625V8.3125H11.5625C10.9375 8.3125 10.75 8.6875 10.75 9.0625V10H12.125L11.875 11.4375H10.6875V15C13.1875 14.625 15 12.5 15 10Z"
                        fill="#057092"
                      />
                    </svg>
                  </a>
                  {/* Twitter */}
                  <a
                    aria-label="twitter"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={'https://twitter.com/online_isb'}
                  >
                 
<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 15C0 23.2843 6.71573 30 15 30C23.2843 30 30 23.2843 30 15C30 6.71573 23.2843 0 15 0C6.71573 0 0 6.71573 0 15Z" fill="white"/>
<path d="M20.3594 8H9.64062C8.73453 8 8 8.73453 8 9.64062V20.3594C8 21.2655 8.73453 22 9.64062 22H20.3594C21.2655 22 22 21.2655 22 20.3594V9.64062C22 8.73453 21.2655 8 20.3594 8Z" fill="white"/>
<path d="M18.391 8.97656H20.3449L16.0761 13.8555L21.098 20.4947H17.1659L14.0862 16.4681L10.5622 20.4947H8.6071L13.173 15.2761L8.35547 8.97656H12.3874L15.1712 12.657L18.391 8.97656ZM17.7052 19.3252H18.7879L11.7991 10.0847H10.6372L17.7052 19.3252Z" fill="#057092"/>
</svg>

                  </a>
                  {/* Youtube */}
                  <a
                    aria-label="youtube"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={'https://www.youtube.com/@isb.online'}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10Z"
                        fill="white"
                      />
                      <path
                        d="M14.75 7.5625C14.625 7.125 14.3125 6.8125 13.875 6.6875C13.125 6.5 9.9375 6.5 9.9375 6.5C9.9375 6.5 6.8125 6.5 6 6.6875C5.5625 6.8125 5.25 7.125 5.125 7.5625C5 8.375 5 10 5 10C5 10 5 11.625 5.1875 12.4375C5.3125 12.875 5.625 13.1875 6.0625 13.3125C6.8125 13.5 10 13.5 10 13.5C10 13.5 13.125 13.5 13.9375 13.3125C14.375 13.1875 14.6875 12.875 14.8125 12.4375C15 11.625 15 10 15 10C15 10 15 8.375 14.75 7.5625ZM9 11.5V8.5L11.625 10L9 11.5Z"
                        fill="#057092"
                      />
                    </svg>
                  </a>
                  {/* LinkedIn */}
                  <a
                    aria-label="linkedin"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={'https://www.linkedin.com/company/isbonline/'}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10Z"
                        fill="white"
                      />
                      <path
                        d="M7.25 15H5.125V8.3125H7.25V15ZM6.1875 7.375C5.5 7.375 5 6.875 5 6.1875C5 5.5 5.5625 5 6.1875 5C6.875 5 7.375 5.5 7.375 6.1875C7.375 6.875 6.875 7.375 6.1875 7.375ZM15 15H12.875V11.375C12.875 10.3125 12.4375 10 11.8125 10C11.1875 10 10.5625 10.5 10.5625 11.4375V15H8.4375V8.3125H10.4375V9.25C10.625 8.8125 11.375 8.125 12.4375 8.125C13.625 8.125 14.875 8.8125 14.875 10.875V15H15Z"
                        fill="#057092"
                      />
                    </svg>
                  </a>
                  {/* Instagram */}
                  <a
                    aria-label="instagram"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={'https://www.instagram.com/isbeduonline/'}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10Z"
                        fill="white"
                      />
                      <path
                        d="M10 5.75C11.375 5.75 11.5625 5.75 12.125 5.75C12.625 5.75 12.875 5.875 13.0625 5.9375C13.3125 6.0625 13.5 6.125 13.6875 6.3125C13.875 6.5 14 6.6875 14.0625 6.9375C14.125 7.125 14.1875 7.375 14.25 7.875C14.25 8.4375 14.25 8.5625 14.25 10C14.25 11.4375 14.25 11.5625 14.25 12.125C14.25 12.625 14.125 12.875 14.0625 13.0625C13.9375 13.3125 13.875 13.5 13.6875 13.6875C13.5 13.875 13.3125 14 13.0625 14.0625C12.875 14.125 12.625 14.1875 12.125 14.25C11.5625 14.25 11.4375 14.25 10 14.25C8.5625 14.25 8.4375 14.25 7.875 14.25C7.375 14.25 7.125 14.125 6.9375 14.0625C6.6875 13.9375 6.5 13.875 6.3125 13.6875C6.125 13.5 6 13.3125 5.9375 13.0625C5.875 12.875 5.8125 12.625 5.75 12.125C5.75 11.5625 5.75 11.4375 5.75 10C5.75 8.5625 5.75 8.4375 5.75 7.875C5.75 7.375 5.875 7.125 5.9375 6.9375C6.0625 6.6875 6.125 6.5 6.3125 6.3125C6.5 6.125 6.6875 6 6.9375 5.9375C7.125 5.875 7.375 5.8125 7.875 5.75C8.4375 5.75 8.625 5.75 10 5.75ZM10 4.8125C8.5625 4.8125 8.4375 4.8125 7.875 4.8125C7.3125 4.8125 6.9375 4.9375 6.625 5.0625C6.3125 5.1875 6 5.375 5.6875 5.6875C5.375 6 5.25 6.25 5.0625 6.625C4.9375 6.9375 4.875 7.3125 4.8125 7.875C4.8125 8.4375 4.8125 8.625 4.8125 10C4.8125 11.4375 4.8125 11.5625 4.8125 12.125C4.8125 12.6875 4.9375 13.0625 5.0625 13.375C5.1875 13.6875 5.375 14 5.6875 14.3125C6 14.625 6.25 14.75 6.625 14.9375C6.9375 15.0625 7.3125 15.125 7.875 15.1875C8.4375 15.1875 8.625 15.1875 10 15.1875C11.375 15.1875 11.5625 15.1875 12.125 15.1875C12.6875 15.1875 13.0625 15.0625 13.375 14.9375C13.6875 14.8125 14 14.625 14.3125 14.3125C14.625 14 14.75 13.75 14.9375 13.375C15.0625 13.0625 15.125 12.6875 15.1875 12.125C15.1875 11.5625 15.1875 11.375 15.1875 10C15.1875 8.625 15.1875 8.4375 15.1875 7.875C15.1875 7.3125 15.0625 6.9375 14.9375 6.625C14.8125 6.3125 14.625 6 14.3125 5.6875C14 5.375 13.75 5.25 13.375 5.0625C13.0625 4.9375 12.6875 4.875 12.125 4.8125C11.5625 4.8125 11.4375 4.8125 10 4.8125Z"
                        fill="#057092"
                      />
                      <path
                        d="M10 7.3125C8.5 7.3125 7.3125 8.5 7.3125 10C7.3125 11.5 8.5 12.6875 10 12.6875C11.5 12.6875 12.6875 11.5 12.6875 10C12.6875 8.5 11.5 7.3125 10 7.3125ZM10 11.75C9.0625 11.75 8.25 11 8.25 10C8.25 9.0625 9 8.25 10 8.25C10.9375 8.25 11.75 9 11.75 10C11.75 10.9375 10.9375 11.75 10 11.75Z"
                        fill="#057092"
                      />
                      <path
                        d="M12.75 7.875C13.0952 7.875 13.375 7.59518 13.375 7.25C13.375 6.90482 13.0952 6.625 12.75 6.625C12.4048 6.625 12.125 6.90482 12.125 7.25C12.125 7.59518 12.4048 7.875 12.75 7.875Z"
                        fill="#057092"
                      />
                    </svg>
                  </a>
                </ul>

              </div>

            </div>
            <hr />

          </div>

        </footer>
      </div>

     <BottomFold faqsection={false} data={bottomFoldData}></BottomFold>

    </div>
  );
};

export default Finance;

export async function getStaticProps() {
  const APIUrl = process.env.API_BASE_URL;
  
  // Fetch data for the finance page and bottom fold
  const [financeTableData, bottomFoldData] = await Promise.all([
    fetch(`${APIUrl}/api/financing-table?populate=*`).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json())
  ]);

  return {
    props: {
      financeTableData,
      bottomFoldData: bottomFoldData
    },
    revalidate: 240
  };
}
