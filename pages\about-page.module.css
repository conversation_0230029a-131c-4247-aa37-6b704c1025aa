
  

  .breadCrumb p{
    font-size: 12px;
    color: white;
  }
  
  .breadCrumblast{
    font-size: 12px;
    color: white;
    text-decoration: none !important;
  }
  
  .breadCrumb p:hover {
    font-size: 12px;
    color: white;
    text-decoration: underline;
  }
  
  .hideCrumb{
    max-width: 1000px;
  }

  .description{
    font-size: 17px;
    vertical-align: center;
    font-weight: 400 !important;
    line-height: normal;
    color: white;
  }
  .fold1bg{
    background-image: url('../assets/TealBackground.webp');
    background-size: cover;
    background-color: #057092;
  }
.equalPadding{
    max-width: 1000px;
}
  
    
.bluetTitle{
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 27px;
    color: #057092;
    }
.fontEighteenBlack{
        font-style: normal;
        font-weight: 400;
        font-size: 17px;
        line-height: 27px;
        color: black;
}

.contentTitle{
    font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 27px;
  color: #057092;
  }
  
  .fold2bg{
  background:#D7E0E8
  }

  .fold3bg{
    background: #FFFFFF

  }

  @media screen and (min-width:0px) and (max-width: 499px) {

    .hideCrumb{
      display: none;
    }
}
@media screen and (min-width:500px) and (max-width: 976px) {

    .hideCrumb{
      display: none;
    }
}
.p{
    max-width: 1000px;
}

.div{


    display:flex;
    flex-direction:column;
    justify-content:center;
    align-items:center

}
.h1{
    color: #057092;
    font-weight: 700;
}