 
@media (min-width: 992px) {
.bodymastercontainer{
    background-color: var(--isb-edge-body-background-color);
}
.maincontainer {
    /* max-width: 1444; */
    /* padding:0 100px; */
    display: flex;
    justify-content: center;

}
.divider{
    height: 100px;
    /* border-bottom: var(--isb-edge-border); */
    padding: 0 60px 0 59px;
    /* border-left: var(--isb-edge-border); */
    background-color: var(--isb-edge-body-background-color);
}
.subdivider{
    height: 100px;
    /* border-left: var(--isb-edge-border); */
    /* border-right: var(--isb-edge-border); */
    padding: 0 90px;
}
.supersubdivider{
    margin: 0 79px;
    /* border-left: var(--isb-edge-border); */
    height: 100%;
    
}
.bottomsupersubdivider{
    height: 100px;
    /* border-left: var(--isb-edge-border); */
    margin: 0 169px;
}
.bodymaincontainer{
    /* border-left: var(--isb-edge-border); */
    /* border-right: var(--isb-edge-border); */
    margin: 0 60px;
    
    
}
.posts_title{
    /* border-left: var(--isb-edge-border); */
}
.secondsmallCard{
    padding-left: 10px;
    padding-bottom: 20px;
}

.breadcrumbContainer{
    height: var(--isb-edge-breadcrumb-navbar-height);
    background-color: var(--isb-edge-body-background-color);
    border-top: var(--isb-edge-border);
    border-bottom: var(--isb-edge-border);
    align-content: center;
    padding: 0 60px;
}
.largeCard.horizontalCard .content {
    padding: 40px 38px 38px 24px;

}
}

/* Header Styles */
.headerWrapper {
    padding: 2rem;
    background:#ffffffe0;
    margin-bottom: 2rem;
}

.headerImageContainer {
    width: 200px;
    height: 200px;
    position: relative;
}

.headerImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}
 


.breadcrumbsection{
    margin: 0 auto;
    height: 100%;
    /* border-left: var(--isb-edge-border); */
    /* border-right: var(--isb-edge-border); */
    align-content: center;
    padding: 0 20px;
}
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #2196f3;
}

.breadcrumb span {
    color: #999;
}

.headerContent {
    max-width: 800px;
    background: linear-gradient(to right, #32CD32 50%, #ffffffe0 10%);
}

.logoSection {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.logoSection h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #1a237e;
    margin: 0;
}

.description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #6c757d;
    margin: 0;
}

.versionTag {
    background-color: #4caf50;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
}

.img-fluid {
    border-radius: 0.5rem;
    max-height: 150px; /* Adjust based on your design */
}

/* Card Styles */
.card {
    position: relative;
    background: white;
    border: 1px solid #e0e0e0;
    height: 100%;
    border: 1px solid #e0e0e0;
    border-bottom: 3px solid #192890;
    display: flex;
}

.clickable {
    cursor: pointer;
    transition: all 0.3s ease;
}
 
 

.imageWrapper {
    position: relative;
    width: 100%;
}

.imageWrapper img {
    width: 100%;
    object-fit: contain;
    position: relative !important;
}

.categoryIcon {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #AAF05A;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.largeCard .categoryIcon
 {
    right: -15px;
    top: 3%;
}

.centeredIcon {
    right: -15px ;
    top: 5% 
}
.horzontalsinglefulengthcard{
    padding-bottom: 20px;
}
.mediumCard .categoryIcon {
    right: -15PX;
    top: 5%;
}

.smallCard .categoryIcon {
    right: 14px;
    bottom: -13px;
    z-index: 2;
}

.smallCard {
    height: 100%;
    position: relative;
}

.smallCard .imageWrapper {
    /* height: 180px; */
    position: relative;
}

/* Horizontal Card Layout */
.horizontalCard {
    display: flex;
    flex-direction: row;
}

.horizontalCard .imageWrapper {
    position: relative;
    flex: 1;
}

.horizontalCard .content {
    flex: 1.5;
    padding: 1.5rem;
}

/* First large card */
.largeCard.horizontalCard .imageWrapper {
    width: 55%;
    min-width: 50%;
    max-width: 55%;
    flex: none !important;
}

.largeCard.horizontalCard .content {
    width: 50%;
    flex: 1;
    padding: 24px;
}

/* Last card and medium cards */
.lastOdd.horizontalCard .imageWrapper,
.mediumCard.horizontalCard .imageWrapper {
    max-width: 30%;
    flex: 0.8;
}

.lastOdd.horizontalCard .content,
.mediumCard.horizontalCard .content {
    /* flex: 2;  flex css changed from flex: 2 to flex: 1 1 as it is shrinking the card image size*/
    flex: 1 1;  
    padding: 24px;
}

/* Vertical Card Layout */
.verticalCard {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.verticalCard .imageWrapper {
    position: relative;
    height: 350px;
}

.verticalCard .content {
    flex: 1;
}

/* Content Styles */
.content {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 24px;
    height: 100%;
}

.category {
    display: inline-block;
    font-size: 14px;
    color: #5D6F7A;
    font-weight: 600;
    text-transform: uppercase;
    font-family: "Inter", sans-serif;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    /* padding-bottom: 16px; */
}

.title {
    color: #192890;
    line-height: 1.3;
    margin: 0;
    font-weight: 600;
    font-family: Reckless;
}

.largeCard .title,
.mediumCard .title,
.smallCard .title,
.horizontalCard .title,
.lastOdd .title {
    font-size: 26px;
}

.excerpt {
    font-family: "Inter", sans-serif;
    color: #666666;
    line-height: 1.6;
    margin: 0.75rem 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    overflow: auto;
    font-weight: 400;
    flex-grow: 1;
}
.largeCard .excerpt,
.mediumCard .excerpt,
.smallCard .excerpt,
.lastOdd .excerpt {
    font-size: 16px;
    line-height: 1.6;
    margin: 0.75rem 0;
}

.meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: auto;
    font-size: 14px;
    color: #5D6F7A;
    justify-content: space-between;
}

.meta span {
    font-family: "Inter", sans-serif;
    font-weight: 400;
}
.posts_title{
    max-width: var(--isb-container-max-width);
}
.posts_title h1{
    font-family: Reckless;
    font-size: 36px;
    font-weight: 600;
    line-height: 38px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #192890;
    margin-bottom: calc(180px - 140px);
    margin-top: 50px;
}

.topics_horizontalline{
    border: 1.5px solid #00000020;
    width: 100%;
    height: 1px;
    align-self: center;
    margin-top: 5px;
}
.horizontalline{
    border: 1.5px solid #00000020;
    width: 100%;
    height: 1px;
    align-self: center;
    margin-top: 20px;
}
/* Responsive Layout */
@media (max-width: 992px) {
    .horizontalCard .title {
        font-size: 18px;
    }
    
    .horizontalCard .content {
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    .horizontalline{
        border: 1.5px solid #00000020;
        margin-top: 5px;
        margin-right: 3px;
    }
    .posts_title h1{
        margin-top: 0;
        /* padding-top: 15px !important; */
        font-size: 30px;
        font-weight: 600;
        line-height: 100%;
    }
    .largeCard.horizontalCard .imageWrapper {
        width: 100%;
    }
    .largeCard.horizontalCard .content {
        width: 100%;
    }
    .bodymaincontainer{
        padding: 0 1rem;
    }
    .breadcrumbsection{
        background-color: var(--isb-edge-body-background-color);
        padding: 1rem ;
    }
.posts_title h1{
    padding-top: 25px;
    margin-bottom: 50px;
}
 
    .centeredIcon{
        top: 37% !important;
        left: 5%;
    }
    .categoryIcon{
        top: 92% !important;
        left: 16px;
    }
    .headerWrapper {
        padding: 0 0 1.5rem 0;
    }

    .breadcrumb {
        padding: 1rem;
        margin-bottom: 0;
    }

    .headerImageContainer {
        width: 100%;
        height: 250px;
        margin: 0;
    }

    .headerImage {
        border-radius: 0;
    }

    .headerContent {
        padding: 0 1rem;
        text-align: center;
    }

    .logoSection h1 {
        font-size: 2rem;
        margin: 0 0 1rem;
    }

    .description {
        font-size: 0.95rem;
    }

    .excerpt {
        font-size: 14px;
        line-height: 1.6;
    }

    .meta,
    .meta span {
        font-size: 12px;
        line-height: 1.4;
    }

    .headerWrapper {
        padding: 1rem;
    }

    .logoSection h1 {
        font-size: 1.75rem;
    }

    .description {
        font-size: 0.95rem;
    }

    .container {
        padding: 0.5rem;
    }
    
    /* Make all cards vertical on mobile */
    .card {
        flex-direction: column !important;
        min-height: auto;
    }

    .horizontalCard,
    .verticalCard,
    .largeCard,
    .mediumCard,
    .smallCard,
    .lastOdd {
        flex-direction: column;
        min-height: auto;
    }
    
    .horizontalCard .imageWrapper,
    .verticalCard .imageWrapper,
    .largeCard .imageWrapper,
    .mediumCard .imageWrapper,
    .smallCard .imageWrapper,
    .lastOdd .imageWrapper {
        flex: none !important;

        width: 100%;
        height: 200px;
        min-width: 100%;
        position: relative;
        margin-bottom: 1rem; /* Add spacing between image and content */
    }
    
    .horizontalCard .content,
    .verticalCard .content,
    .largeCard .content,
    .mediumCard .content,
    .smallCard .content,
    .lastOdd .content {
        padding: 1.25rem;
        width: 100%;
    }

    .title {
        font-size: 18px;
    }

    .excerpt {
        font-size: 0.875rem;
    }

    .meta {
        font-size: 0.75rem;
    }

    .headerWrapper {
        padding: 1.5rem 1rem;
        text-align: center;
    }

    .breadcrumb {
        font-size: 0.75rem;
        margin-bottom: 1rem;
        text-align: left;
    }

    .logoSection h1 {
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }

    .description {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
        text-align: left;
    }

    /* Adjust image container on mobile */
    .headerWrapper :global(.col-lg-4) {
        justify-content: center !important;
    }

    .headerWrapper :global(.img-fluid) {
        width: 150px;
        height: 150px;
        object-fit: cover;
    }
}

@media (max-width: 576px) {
    .headerImageContainer {
        height: 200px;
    }

    .logoSection h1 {
        font-size: 1.75rem;
    }

    .description {
        font-size: 0.875rem;
    }

    .headerWrapper {
        padding: 0.75rem;
    }

    .breadcrumb {
        font-size: 0.75rem;
    }

    .logoSection h1 {
        font-size: 1.5rem;
    }

    .container {
        padding: 0.25rem;
    }

    /* Consistent padding and image height for all cards */
    .horizontalCard .content,
    .verticalCard .content,
    .largeCard .content,
    .mediumCard .content,
    .smallCard .content,
    .lastOdd .content {
        padding: 1rem;
    }

    .horizontalCard .imageWrapper,
    .verticalCard .imageWrapper,
    .largeCard .imageWrapper,
    .mediumCard .imageWrapper,
    .smallCard .imageWrapper,
    .lastOdd .imageWrapper {
        height: 200px;
        flex: none;
        width: 100%;
        min-width: 100%;
        max-width: 100%;
    }

    /* Adjust spacing between cards */
    .card {
        margin-bottom: 1rem;
    }

    .headerWrapper {
        padding: 1rem;
    }

    .logoSection h1 {
        font-size: 1.75rem;
    }

    .description {
        font-size: 0.875rem;
    }

    .headerWrapper :global(.img-fluid) {
        width: 120px;
        height: 120px;
    }
}
.bodymastercontainer{
    /* background-color: var(--isb-edge-body-background-color); */
    background: linear-gradient(to bottom, #F4F8FA 78%, #C3D3FF 100%);
    
}