@media (min-width: 992px) {
    .breadcrumbContainer{
      height: var(--isb-edge-breadcrumb-navbar-height);
      background-color: transparent;
      /* border-top: var(--isb-edge-border); */
      /* border-bottom: var(--isb-edge-border); */
      align-content: center;
      /* padding: 0 60px; */
      max-width: var(--isb-container-max-width);
      margin: 0 auto;

  }
  .breadcrumbsection{
    margin: 0 auto;
    height: 100%;
    /* border-left: var(--isb-edge-border); */
    /* border-right: var(--isb-edge-border); */
    align-content: center;
    padding: 40px 0 0;
    max-width: var(--isb-container-max-width);
}
 
  }
  @media (max-width: 768px) {
    .breadcrumb ol {
        padding: 0;
    }
    .topRightText p{
        text-align: start !important;
      }
      .breadcrumbsection{
        padding: 0rem ;
    }
    .breadcrumb {
        padding-bottom: 15px;
      }
  }
  .topRightText p{
    margin-bottom: 0;
    text-align: end;
    font-size: var(--isb-edge-p-font-size);
    color: var(--isb-edge-p-font-color);
    }

.breadcrumb {
    
    color: #666;
    font-size: 0.9rem;
}

.breadcrumb ol {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.breadcrumb li {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}

/* .breadcrumb li:not(:last-child)::after {
    content: "/";
    margin: 0 0.3rem;
    color: var(--isb-edge-p-font-color);
} */

.breadcrumb li:not(:last-child)::after {
    content: "→";
    margin: 0 0.3rem;
    color: var(--isb-edge-p-font-color) /* Inherit color from parent to keep default styling */
}

.breadcrumb li:nth-last-child(2)::after {
    color: var(--isb-edge-blue-font-color); /* Apply color only to the last arrow */
}

.current{
    color: #192890;

}
.breadcrumb a {
    color:  #7F7F7F; 
    text-decoration: none;
    transition: color 0.2s;
}

.breadcrumb a:hover {
    color: #2196f3;
    text-decoration: underline;
}

.breadcrumb span {
    color: #192890;
}
