.referralFormSection {
  background-color: #0047AB;
  padding: 60px 0;
  color: white;
}

.referralFormContainer {
  background-color: #0047AB;
  border-radius: 0;
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.formTitle {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 1.5rem;
}

.formSubtitle {
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 2rem;
}

.formField {
  margin-bottom: 1.5rem;
}

.generateButton {
  background-color: #ffffff;
  color: #0047AB;
  border: none;
  font-weight: bold;
  padding: 10px 20px;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  display: block;
  transition: all 0.3s ease;
}

.generateButton:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

.howItWorksSection {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.stepCard {
  text-align: center;
  padding: 20px;
  height: 100%;
  transition: all 0.3s ease;
}

.stepCard:hover {
  transform: translateY(-5px);
}

.stepIcon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e6f0ff;
  border-radius: 50%;
  color: #0047AB;
}

.stepTitle {
  font-weight: bold;
  margin-bottom: 15px;
}

.linkModal {
  color: #333;
}

.linkInput {
  margin-bottom: 20px;
}

.copyButton {
  background-color: #0047AB;
  border: none;
}

.copyButton:hover {
  background-color: #003380;
}

.emailSection {
  margin-top: 30px;
  border-top: 1px solid #dee2e6;
  padding-top: 20px;
}

.sendButton {
  background-color: #0047AB;
  border: none;
}

.sendButton:hover {
  background-color: #003380;
}
.equalPadding{
  max-width: var(--isb-container-max-width);
}

.faqQuestion{
  font-size: 17px;
  font-weight: 500;
  overflow: hidden;
}


.faqAnswer{
  text-align: left;
  color: black !important;
  overflow: hidden;
}

/* Smooth collapse animation styles */
.collapseWrapper {
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.collapseContent {
  padding-top: 0;
  padding-bottom: 0;
  transition: padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.collapseContent.expanded {
  padding-top: 10px;
  padding-bottom: 10px;
}

.black_text{
  color: #000000;
}