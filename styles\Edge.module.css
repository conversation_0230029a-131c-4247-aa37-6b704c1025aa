.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.header {
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: #f0f8ff;
  border-radius: 8px;
}

.logo h1 {
  font-size: 2.5rem;
  color: #1a237e;
  margin-bottom: 1rem;
}

.logo p {
  color: #666;
  font-size: 1.1rem;
  max-width: 600px;
}

.sectionTitle {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #333;
}

/* Grid Layout */
.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* Creates 3 equal columns */
  grid-auto-rows: minmax(min-content, max-content);
  gap: 2rem;
  padding: 1rem;
}

/* First card spans 2 columns */
.card:first-child {
  grid-column: 1 / span 2;
  grid-row: 1;
}

/* Second card */
.card:nth-child(2) {
  grid-column: 3;
  grid-row: 1 / span 2;
}

/* Third and Fourth cards */
.card:nth-child(3),
.card:nth-child(4) {
  grid-column: span 1;
}

/* Fifth card spans full width */
.card:nth-child(5) {
  grid-column: 1 / -1;
}

.card {
  position: relative;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  display: grid;
  grid-template-rows: auto 1fr;
}

.card:hover {
  transform: translateY(-4px);
}

.iconWrapper {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  width: 48px;
  height: 48px;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
  padding: 8px;
  overflow: hidden;
}

.iconWrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.imageWrapper {
  position: relative;
  width: 100%;
  padding-top: 60%;
}

.imageWrapper img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content {
  padding: 1.5rem;
  display: grid;
  grid-template-rows: auto auto 1fr auto;
  gap: 0.75rem;
}

.category {
  display: inline-block;
  font-size: 0.875rem;
  color: #2196f3;
  font-weight: 600;
}

.title {
  font-size: 1.25rem;
  color: #333;
  line-height: 1.4;
}

.excerpt {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
}

.meta {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 1rem;
  color: #888;
  font-size: 0.875rem;
  align-items: center;
}

/* Responsive Layout */
@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card:first-child,
  .card:nth-child(2),
  .card:nth-child(3),
  .card:nth-child(4),
  .card:nth-child(5) {
    grid-column: span 1;
    grid-row: auto;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .header {
    padding: 1.5rem;
  }
  
  .logo h1 {
    font-size: 2rem;
  }
  
  .card {
    grid-column: 1 / -1 !important;
  }
}
