import classes from "./lxp-page.module.css";
import Link from "next/link";
import Image from "next/image";
import BottomFold from "../components/bottom_fold_one";
import { useRouter } from "next/router";

function LXPPage(props) {
  const data = props.apiData.data.attributes;
  const router = useRouter();
  const { query } = router;
  const qpms =
    query.utm_source != undefined
      ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`
      : ``;

  return (
    <>
      <div className={`${classes.fold1bg}`}>
        <div className={`${classes.hideCrumb} mx-auto pt-lg-5 px-3 px-lg-0`}>
          <div className={`d-flex ${classes.breadCrumb} px-3`}>
            <Link style={{ textDecoration: "none" }} href={`/${qpms}`}>
              <p className="text-white m-0">ISB Online</p>
            </Link>
            <svg
              style={{ fill: "#ffffff" }}
              width="24"
              height="18"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_3275_9282)">
                <path d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z" />
              </g>
              <defs>
                <clipPath id="clip0_3275_9282">
                  <rect width="24" height="24" fill="#057092" />
                </clipPath>
              </defs>
            </svg>
            <p className={`text-white m-0 ${classes.breadCrumblast}`}>
              Learning Experience
            </p>
          </div>
        </div>
        <div
          className={`row py-lg-5 flex-row-reverse p-0 ${classes.equalPadding} mx-auto`}
        >
          <Image
            width="0"
            height="0"
            sizes="100vw"
            className={`img-fluid m-100 h-auto col-lg-6 col-md-12 p-0`}
            src={props.apiUrl + data.media.data.attributes.url}
            alt={data.media.data.attributes.alternativeText}
          />
          <div
            className={`col-lg-6 col-md-12 py-lg-0 py-md-4 py-sm-4 py-4 px-3 d-flex flex-column justify-content-center`}
          >
            <h1 className="text-white">
              <b>{data.lxp_page_title}</b>
            </h1>
            <p className="text-white pt-1">{data.lxp_page_description}</p>
            <div></div>
          </div>
        </div>
      </div>
      <div className={`${classes.equalPadding} mx-auto`}>
        {data.lxp_body.map((content, i) => {
          return (
            <>
              <div
                key={i}
                className={`row py-lg-5 py-md-4 py-3 px-lg-0 px-3 ${
                  i % 2 === 0 ? "flex-lg-row-reverse flex-lg-row-reverse" : ""
                }`}
              >
                <div className="col-lg-5 col-md-5 col-12">
                  <Image
                  width={402}
                  height={226}
                    className="img-fluid"
                    src={props.apiUrl + content.image.data[0].attributes.url}
                    alt={
                      props.apiUrl +
                      content.image.data[0].attributes.alternativeText
                    }
                  />
                </div>
                <div className="col-lg-7 col-md-7 col-12 px-lg-4 px-md-4 mt-lg-0 mt-md-0 mt-3">
                  <h2 className={classes.contentTitle}>{content.title}</h2>
                  <p>{content.description}</p>
                </div>
              </div>
            </>
          );
        })}
      </div>
      <div className="text-center">
       
      </div>
      <BottomFold data={props.bottomFoldData}></BottomFold>
    </>
  );
}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;

  const [response, bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/lxp-page?populate[lxp_body][populate]=*&populate[media][populate]=*&populate[meta_tags][populate]=*`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      apiData: response,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData,
    },
    revalidate: 60,
  };
}

export default LXPPage;
