.ribbon_style{
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  --f: .5em; /* control the folded part */
  position: absolute;
  top: 0;
  right: 0;
  line-height: 1.8;
  padding-inline: 1lh;
  padding-bottom: var(--f);
  border-image: conic-gradient(#0008 0 0) 51%/var(--f);
  clip-path: polygon(
    100% calc(100% - var(--f)),100% 100%,calc(100% - var(--f)) calc(100% - var(--f)),var(--f) calc(100% - var(--f)), 0 100%,0 calc(100% - var(--f)),999px calc(100% - var(--f) - 999px),calc(100% - 999px) calc(100% - var(--f) - 999px));
  transform: translate(calc((1 - cos(45deg))*100%), -100%) rotate(45deg);
  transform-origin: -1% 100%;
  background-color: #0d89de;  
 
}

.liStyle p {
  color: white !important;
  font-weight: 300;
}

.liStyle li {
  color: white !important;
  font-weight: 300;
}


.heading {
  font-size: 27px !important;
}

.description {
  font-size: 17px;
  vertical-align: center;
  font-weight: 400 !important;
  line-height: normal;
}

.para {
  color: black !important;
  text-decoration: none;
  background-color: transparent;
  align-items: baseline;
  font-weight: 600;
  vertical-align: center;
  margin: 0;
}

.dropdown {
  position: relative;
  display: inline-block;
  color: black !important;
  text-decoration: none;
  background-color: transparent;
  align-items: baseline;
  font-weight: 600;
  vertical-align: center;
  border-color: transparent;
}

.dropdown::after {
  display: none !important;
}

.dropdown:hover {
  color: black !important;
  text-decoration: none;
  background-color: transparent;
  align-items: baseline;
  font-weight: 600;
  vertical-align: center;
  border-color: transparent;
}

.dropdown::on-click {
  color: black !important;
  text-decoration: none;
  background-color: transparent;
  align-items: baseline;
  font-weight: 600;
  vertical-align: center;
  border-color: transparent;
  margin: 0;
}

.dropdowncontent {
  visibility: hidden;
  opacity: 0;
  position: fixed;
  left: 0;
  right: 0;
  width: 100vw;
  background-color: #E6EBED;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  z-index: 1000;
  margin-top: 2rem;
  transition: all 0.2s ease-in-out;
}

.dropdown:hover .dropdowncontent {
  visibility: visible;
  opacity: 1;
  display: block;
}

.dropdownInner {
  display: flex;
  grid-template-columns: repeat(3, minmax(200px, 300px));
  gap: 2rem;
  max-width: 1200px;
  margin: 10px auto;
  padding: 0 2rem;
  justify-content: center;
}

.dropdownColumn {
  display: flex;
  flex-direction: column;
  /* gap: 0.2rem; */
}

.dropdowncontent a {
  color: black;
  text-decoration: none;
  padding: 0.8rem 1rem;
  transition: color 0.2s;
  border-radius: 4px;
}

.dropdowncontent a:hover {
  color: #0d89de;
}

.viewMoreLink {
  color: #0d89de !important;
  font-weight: 500;
  margin-top: auto;
}

.viewMoreLink:hover {
  background-color: transparent !important;
  text-decoration: underline !important;
}

.faqQuestion {
  font-size: 17px;
  font-weight: 500;
}

.faqAnswer {
  text-align: left;
  color: white !important;
}

.horizontalLine {
  border-top: 3px solid white;
}

.bgImage {
  position: relative;
  width: 100%;
  height: 400px;
}

.bgContent {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 1;
  color: #fff;
  padding: 16px;
  width: 100%;
}

.contentFold {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: left;
  z-index: 1;
  color: #fff;
  padding: 16px;
  width: 100%;
  /* Change the color of the text if needed */
}

.fold1bg {
  background-image: url('../assets/TealBackground.webp');
  background-size: cover;
  background-color: #057092
}

.newsFold {
  background-image: url('../assets/new_background.webp');
  background-size: cover
}

.insightStyle a {
  text-decoration: none;
}

.insightCards {
  min-height: 180px;
  max-height: 180px;
  width: 100%;
}

.insightCardsText a {
  font-size: 16px;
  font-weight: 700;
  color: #7C8495;
}

.fontwhite {
  color: white;
  list-style: 21px !important
}

.buttonPos {
  position: relative;
}

.buttonPosition {
  bottom: 0px;
  position: absolute;
}

.blackHeading {
  font-weight: 700;
  font-size: 26px;
  line-height: 54.47px;
  color: #000;
}

.blueHeading {
  font-weight: 700;
  font-size: 24px;
  line-height: 54.47px;
  color: #057092;
}

.nwlTitle {
  font-weight: 600;
  font-size: 20px;
  line-height: 34.47px;
  color: #057092;
}

.card_body_cal {
  min-height: 120px
}

.learnTrackTitle {
  color: black;
  font-size: 1rem;
  font-weight: 700;
}

.homeImage {
  width: 100vw;
}

.bluebackground {
  background-color: #D7E0E8;
}

.btnprimary {
  background-color: #057092;
  border-radius: 0px;
}

.moduleTitle {
  color: #057092 !important;
  text-decoration: none;
  align-items: baseline;
  vertical-align: center;
  font-size: 20px;
  margin: 0;
}

.lessonContent {
  color: #7C8495 !important;
  text-decoration: none;
  align-items: baseline;
  padding: 2px;
  margin: 0px;
  vertical-align: center;
}

.header {
  width: 100%;
  height: 5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #FFFFFF;
  padding: 0 10%;
  position: sticky;
}

.navelems {
  right: 0;
  padding-right: 59px;
  padding-left: 59px;
}

.content {
  padding: 25PX;
  background-color: #F9F9F9;
  margin-top: 10px;
}

.fold1 {
  padding: 0;
  background-color: rgb(255, 255, 255);

}

.custm_padding {
  padding: 0px;
}

.bottomFold {
  padding: 0;
  background-color: white;
  margin-top: 48px;
  margin-left: 48px;
  margin-right: 28px;
}

.profFold {
  padding: 0;
  background-color: white;
  margin-right: 48px;
}


.sec-start {
  padding: 20px 0px;
}

.custm_btn {
  background-color: #057092;
}

.custm_font {
  color: #000;
}

.fold1btn {
  min-height: 38px;
  background-color: #fff !important;
  border: 2px solid #fff !important;
  color: #057092 !important;
  font-weight: 600;
  border-radius: 0px !important;
  fill: #057092 !important;
}

.fold1btn:hover {
  background-color: transparent !important;
  border: 3px solid #fff !important;
  color: #fff !important;
  font-weight: 600;
  border-radius: 0px !important;
  fill: white !important;
}


.viewbtn {
  background-color: #057092 !important;
  padding: 6px 20px;
  border: none;

  border-radius: 0px !important;
}

.viewbtn:hover {
  background-color: #057092 !important;
  padding: 6px 20px;
  font-weight: 600;
  border: none;

  border-radius: 0px !important;
}

.image_border {
  border-radius: 0px;

}

.courseCardHeight {
  min-height: 482px;
}

.boxshadow {
  border: none;
  border-radius: 0%;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12);
}

.learningtracktext {
  font-size: 14px;
  text-align: center;
  color: #7C8495;
}


.newwaylearn {
  background-color: #D7E0E8;
  padding: 40px 0px;
}

.new-way-learn-title {
  color: #057092;
  font-size: 20px;
  padding-bottom: 20px;
}



.newwaylearnbtn,
.newwaylearnbtn-:hover {
  background-color: #fff !important;
  border: 1px solid #057092 !important;
  color: #057092 !important;
  font-weight: 600;
  border-radius: 0px !important;
  max-width: 180px;
}

.footer {
  background-color: #057092;
}

.allfaqs {
  font-size: 16px;
  color: #fff;
  font-weight: 400;

}

.allfaqHeading {
  font-size: 16px;
  color: #fff !important;
  font-weight: 600;
  text-decoration: none;
}

.announcementsTitle {
  font-size: 14px;
  color: #fff;
  font-weight: 400;
}

.foottitle {
  border-bottom: 1px solid white;
  color: white;
  padding-bottom: 8px;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
}


.sponsorBorder {
  border-right: 1px solid gray;
  padding-right: 22px !important;
}

.sponsorFont {
  font-size: 17px;
  font-weight: 600;
}

.sponsorTextAlign {
  text-align: left;
}

.footerFinal {
  border-bottom: 1px solid black;
}

.collapsible {
  flex-direction: row;
  justify-content: space-between;
  display: flex;
}

.left-footer hr {
  color: #fff !important;
  background-color: #fff !important;
}

.footer-plus {
  margin-top: 15px;
  color: #fff;
}

.footer-first-plus {
  margin-top: 10px;
  color: #fff;
}

.footer ul {
  list-style-type: none;
  color: #fff;
  font-size: 14px;
  font-weight: 300;
  padding-left: 0px;
  margin-top: 20px;
}

.footer ul li {
  padding-bottom: 5px;
}

.social-icons .fa {
  background-color: #fff;
  color: #057092;
  width: 23px;
  height: 23px;
  padding-left: 5px;
  padding-top: 4px;
  border-radius: 50%;
  margin-top: 3px;
}

.footerrow {
  padding-top: 25px;
  padding-bottom: 25px;
  padding-left: 12px;
  padding-right: 12px;
  background: #057092;
}

.footerrowWhite {
  padding-top: 25px;
  padding-bottom: 25px;
  padding-left: 12px;
  padding-right: 12px;
  background: #FFFFFF;
}

#testimonial {
  padding: 30px 20px 20px;
  color: #fff;
  background-color: #d7e0e8;
}

#testimonial h2 {
  font-style: italic;
  color: #fff;
  font-size: 26px;
  text-align: center;
}

#testimonial .client-img {
  width: 200px;
  height: 200px;
  margin: 0px auto;
  border-radius: 100%;
  position: absolute;
  left: 0px;
}

#testimonial .carousel-content {
  padding: 20px 0px 20px 100px;
  width: 78%;
  margin: 0 auto;
  position: relative;
  background-color: #fff;
}

#testimonial h3 {
  font-size: 17px;
  color: #fff;
  margin-bottom: 30px;
  font-style: italic;
  text-align: right;
}

.carousal-btn,
.carousal-btn:hover {
  background-color: #fff !important;
  border: 1px solid #057092 !important;
  color: #057092 !important;
  font-weight: 600;
  border-radius: 0px !important;
  margin-left: 16%;
}

#testimonial .testimonial-text {
  font-size: 15px;
  color: #7C8495;
  margin-left: 16%;
  font-weight: 500;
  width: 61%;
}

#testimonial .testimonial-date {
  font-size: 12px;
  color: #7C8495;
  margin-left: 16%;
}

#testimonial .client-img img {
  width: 100%;
  padding-left: 15px;
}

#testimonial .carousel-control-prev,
#testimonial .carousel-control-next {
  font-size: 36px;
}

.main-carousal {
  background-color: #ECECEC;
}

.main-carousal-item img {
  width: 85%;
  margin: 0% 4% 1% 5%;
}

.courses-btn,
.courses-btn:hover {
  background-color: #057092 !important;
  border: 1px solid #057092 !important;
  color: #fff !important;
  font-weight: 600;
  border-radius: 0px !important;
  margin-top: 10px;
  margin-bottom: 30px;
}

.card_response {
  padding-top: 40px
}

.padset {
  padding: 2rem
}

.equalPadding {
  max-width: 1000px;
}

@media screen and (min-width:0px) and (max-width: 499px) {

  .contentFold {
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #057092;
    transform: translate(-50%, -50%);
    text-align: left;
    z-index: 1;
    color: #fff;
    padding: 16px;
    width: 100%;
  }


  .card_response {
    padding: 40px;
  }

  .blackHeading {
    font-size: 24px
  }

  .blueHeading {
    font-size: 24px;
    line-height: 32px;
  }

  .padset {
    padding: 30px;

  }

  .sponsorBorder {
    border-right: 1px solid transparent;
    padding-right: 0px !important;

  }

  .navelems {
    right: 0;
    padding-right: 16px;
    padding-left: 16px;
  }

  .fold1bg {
    background-size: cover;
    background-color: #057092
  }

  .bgImage {
    position: relative;
    width: 100%;
    height: 560px;
  }

  .sponsorTextAlign {
    text-align: center;
  }



}
.perspective_title{
  display: none;
}
@media (max-width:768px){
  
  .navbar_logo{
    width: 117px;
    height: 100%;
  }
}
@media screen and (min-width:500px) and (max-width: 976px) {

  .contentFold {
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #057092;
    transform: translate(-50%, -50%);
    text-align: left;
    z-index: 1;
    color: #fff;
    padding: 16px;
    width: 100%;
  }


  .card_response {
    padding: 40px;
  }

  .blackHeading {
    font-size: 25px
  }

  .blueHeading {
    font-size: 24px;
  }

  .padset {
    padding: 30px;

  }

  .sponsorBorder {
    border-right: 1px solid transparent;
    padding-right: 22px !important;
  }

  .navelems {
    right: 0;
    padding-right: 16px;
    padding-left: 16px;
  }

  .fold1bg {
    background-size: cover;
    background-color: #057092
  }

  .bgImage {
    position: relative;
    width: 100%;
    height: 720px;
  }


  .sponsorTextAlign {
    text-align: center;
  }

}


@media screen and (min-width: 997px) {
  .navelemsmain{
    position: relative;
    right: 90px;
  }
  .navelems {
    padding: 0 59px;
    height: var(--isb-main-navbar-height);
  }
}
@media screen and (min-width: 1700px) {
  .card_response {
    padding: 40px;
  }

  .blackHeading {
    font-size: 25px
  }

  .blueHeading {
    font-size: 24px;
  }

  .padset {
    padding: 30px;
  }

  .sponsorTextAlign {
    text-align: center;
  }

}

.bottomLink {
  font-size: 14px;
}

 
.dropbtn, .dropdownItem, .dropbtnmobile {
  font-size: 16px;
  font-weight: 500;
  color: black;

}
.navrighttitle{
  font-size: 24px;
  font-weight: 600;
  line-height: 29px;
  color: var(--isb-edge-blue-font-color);
  position: fixed;
  right: 3%;

}
.perspective_title{
  font-family: var(--isb-edge-font-family-inter);
  margin-left: auto;
  font-size: 14px;
  font-weight: 600;
  color: var(--isb-edge-blue-font-color);
}
.dropbtn {
  background-color: transparent;
  border: none;
}

/* The container <div> - needed to position the dropdown content */
.dropdown {
  position: relative;
  display: inline-block;
  right: 5;
}

/* Dropdown Content (Hidden by Default) */


/* Links inside the dropdown */


/* Change color of dropdown links on hover */


/* Show the dropdown menu on hover */


/* Change the background color of the dropdown button when the dropdown content is shown */


.back_to_top {
  position: fixed;
  bottom: 67px;
  right: 0;
}

.cookie_banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  background-color: #057092;
  border-top: 1px solid #ddd;
  text-align: center;
  z-index: 10;
}

.cookie_banner a {
  font-weight: 600;
  color: white;
}

.cookieButton {
  padding: 3px;
  font-size: 14px;
  background-color: transparent !important;
  border: 1px solid #fff !important;
  color: #fff !important;
  border-radius: 2px !important;
  margin: auto;
  fill: white !important;
}



.bluebackground1 {
  background-color: #fff;
  ;
}

.card_main {
  background: #26ABCD;
  background-size: cover;
}



.rank {
  font-family: Arial;
  font-size: 64px;
  font-weight: 700;
  line-height: 74px;
  letter-spacing: 0em;
  text-align: left;
  font-family: Arial;
  font-size: 42px;
  font-weight: 700;
  line-height: 41px;
  letter-spacing: 0em;
  text-align: left;
  color: #000000;
  ;

}

.rank_description p {
  font-size: 15px !important;

}

.main_white_head {
  color: #FFFFFF;
  font-weight: bold;
  font-size: 24px;
}


.highlightcards_title {
  color: #000000 !important;
  font-weight: 700 !important;
  font-size: 14px !important;
}


.main_blue_head {
  color: #057092;
  font-weight: bold;
  font-size: 24px;
}
.finance_link{
  max-width:1000px;
  justify-content:center;
}
.login_button{
padding: 5px 10px;
border-radius: 5px;
background: #057092;
border: none;
color: white;
font-weight: 500;

}

.dropdownmobile {
  color: black !important;
  text-decoration: none;
  background-color: transparent;
  align-items: baseline;
  font-weight: 600;
  vertical-align: center;
  border-color: transparent;
  font-size: 12px;
}

.dropdownmobile::after {
  display: none !important;
}

.dropdownmobile:hover {
  color: black !important;
  text-decoration: none;
  background-color: transparent;
  align-items: baseline;
  font-weight: 600;
  vertical-align: center;
  border-color: transparent;
}

.dropdownmobile::on-click {
  color: black !important;
  text-decoration: none;
  background-color: transparent;
  align-items: baseline;
  font-weight: 600;
  vertical-align: center;
  border-color: transparent;
  margin: 0;
}
/* The container <div> - needed to position the dropdown content */
.dropdownmobile {
  position: relative;
  display: inline-block;
  right: 5;
}

/* Dropdown Content (Hidden by Default) */
.dropdowncontentmobile {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 250px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  right: 3;
}

/* Links inside the dropdown */
.dropdowncontentmobile a {
  color: #057092;
  padding: 9px 16px;
  text-decoration: none;
  display: block;
}

/* Change color of dropdown links on hover */
.dropdowncontentmobile a:hover {
  background-color: white;
}

/* Show the dropdown menu on hover */
.dropdownmobile:hover .dropdowncontentmobile {
  display: block;
}

/* Change the background color of the dropdown button when the dropdown content is shown */
.dropdownmobile:hover .dropbtnmobile {
  background-color: transparent;
}
.navelementStyl{
  color:var(--isb-edge-blue-font-color) ;
  text-decoration: underline;
  text-underline-offset: 4px;
  text-decoration-thickness: 4px;
  text-decoration-color: var(--isb-edge-green-color);

}
