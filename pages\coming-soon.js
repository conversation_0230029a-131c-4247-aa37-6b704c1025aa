import classes from "./coming-soon.module.css";
import React, { useState } from 'react';
import Router from "next/router";
import BottomFold from "../components/bottom_fold_one";


export default function LandingPage(props) {
  const learningTracks = props.apiData.data;
  const landingPage = props.landingPage.data.attributes;

  const [agree, setAgree] = useState(true);
 
 
  const submitContact = async (event) => {
    event.preventDefault();
    const json = {
      data: {
        name: `${event.target.name.value}`,
        email: `${event.target.email.value}`,
        phone_number: `${event.target.number.value}`,
        is_selected: agree,
      },
    };
    const response = await fetch(
      `${props.apiUrl}/api/lead-forms`,
      {
        body: JSON.stringify(json),
        headers: {
          "Content-Type": "application/json",
        },
        method: "POST",
      }
    );
    if (response.status == 200) {
      Router.push("/cs-tq");
    }
  };

  return (
    <>
      <div className={`${classes.fold1bg}`}>
        <div className={`row py-lg-5 p-0 ${classes.equalPadding} mx-auto `}>
        <div className={`${classes.secstart} ${classes.mobileFoldbg}  col-lg-8 col-md-12 py-lg-0 py-md-4 py-sm-4 py-3 px-0 d-flex flex-column justify-content-center`}>
            <div className="px-lg-0 px-md-3 px-sm-3 px-3">
              <h1 className={`text-white pt-lg-0 pt-lg-4 pt-0 ${classes.title}`}>
                <b>{landingPage.title}</b>
              </h1>
              <p className={`my-lg-4 my-0 text-white pe-lg-3 ${classes.paragraph}`}>
                {landingPage.description}
              </p>
            </div>
            <div></div>
          </div>
        <div className="col-lg-4 col-md-12 p-4 bg-white">
            <div>
              <p className={`p-0 m-0 ${classes.blueBoldText}`}>
               {landingPage.lead_form_title}
              </p>
              <p style={{ fontSize: "15px" }} className="py-2 m-0">
               {landingPage.lead_form_description}
              </p>
              <form onSubmit={submitContact} className="my-0">
                <div className="form-group col-12">
               
                  <input
                    className="form-control"
                    type="text"
                    id="name"
                    name="name"
                    placeholder="Name"
                    required
                  />
                </div>
                <div className="form-group col-12 py-3">
                   
                  <input
                    className="form-control"
                    type="text"
                    id="email"
                    name="email"
                    required
                    placeholder="Email"
                    pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                    title="Please enter valid email address"
                  />
                </div>
                <div className="form-group col-12">
                  
                  <input
                    className="form-control"
                    type="text"
                    id="number"
                    name="number"
                    placeholder="Phone Number"
                    required
                    pattern="[6789][0-9]{9}"
                    title="Please enter valid phone number"
                    onKeyPress={(event) => {
                      if (!/[0-9]/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                  />
                </div>
                  <div className={`mt-3 ${classes.disablePointer}`}>
                  <label
                  style={{fontSize:"13px"}}
                  for="agree">
                    {landingPage.lead_form_terms_content}
                  </label>
                  </div>
                  
                <div className="text-center">
                  <button disabled={!agree} className={`${classes.viewbtn} mt-3`} type="submit">
                  {landingPage.lead_form_btn_title}
                  </button>
                </div>
              </form>
            </div>
          </div>
          
          
        </div>
      </div>
      <div className="bg-white">
        <div className={`${classes.equalPadding} mx-auto`}>
           
        </div>
        <div className={`${classes.execBlock} py-3`}>
          <div className={`${classes.equalPadding} mx-auto`}>
            <p className={`text-center ${classes.whiteHeading} pt-3 m-0`}>
              {landingPage.isb_exec_title}
            </p>
            <div className="row py-3 px-lg-0 px-3">
              {landingPage.exec_education.map((item, i) => {
                return (
                  <div
                    key={i}
                    className="col-lg-4 col-md-6 col-sm-12 col-12 m-0"
                  >
                    <div className="bg-white mt-2">
                      <p
                        className={`${classes.blackHeading} text-center m-0 py-2`}
                      >
                        {item.title_head}
                      </p>
                    </div>
                    <p className={`m-0 p-0 text-center text-white`}>
                      {item.keyword}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
         
      </div>
      <BottomFold data = {props.bottomFoldData}></BottomFold>
    </>
  );
}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;

  const [learningTracks, landingPageResponse,bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/learning-tracks?populate[learning_track_short_image][populate]=*`
    ).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/coming-soon?populate[exec_education][populate]=*`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      apiData: learningTracks,
      landingPage: landingPageResponse,
      apiUrl: APIUrl,
      bottomFoldData:bottomFoldData
    },
    revalidate:60
  };
}
