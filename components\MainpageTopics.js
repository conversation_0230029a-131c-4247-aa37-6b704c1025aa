// components/MainpageTopics.js
import React, { useRef, useState, useEffect } from 'react';
import styles from './MainpageTopics.module.css';
import Image from 'next/image';
import Link from 'next/link';
import Slider from 'react-slick';
import "slick-carousel/slick/slick.css"; 
import "slick-carousel/slick/slick-theme.css";
import { useRouter } from 'next/router';
import viewallarrow from "../assets/viewall_arrow.svg"
import chevronRight from "../assets/Chevron Right.svg"

export default function MainpageTopics({topics, apiUrl}) {
  const router = useRouter();
  const sliderRef = useRef(null);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [isMobile, setIsMobile] = useState(false);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState(false);
  
  // Check for mobile view on client-side only
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    // Initial check
    checkMobile();
    
    // Listen for resize events
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  // Ensure images are pre-loaded before slider initialization
  useEffect(() => {
    if (topics && topics.data && topics.data.length > 0) {
      const imageUrls = topics.data
        .filter(topic => topic.attributes?.topic_image?.data?.attributes?.url)
        .map(topic => `${apiUrl}${topic.attributes.topic_image.data.attributes.url}`);
      
      // Preload images
      const preloadImages = imageUrls.map(url => {
        return new Promise((resolve) => {
          const img = new window.Image();
          img.src = url;
          img.onload = () => resolve(url);
          img.onerror = () => resolve(url); // Continue even if image fails to load
        });
      });
      
      Promise.all(preloadImages).then(() => {
        setImagesLoaded(true);
        // Force slider to update after images are loaded
        if (sliderRef.current) {
          setTimeout(() => {
            sliderRef.current.slickGoTo(0);
          }, 100);
        }
      });
    }
  }, [topics, apiUrl]);
  // Slider settings
  const settings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 1,
    arrows: isMobile, // Use default arrows only in mobile view
    swipeToSlide: true,
    prevArrow: <button className={`${styles.slickPrevArrow} ${isBeginning ? styles.arrowDisabled : ''}`}></button>,
    nextArrow: <button className={`${styles.slickNextArrow} ${isEnd ? styles.arrowDisabled : ''}`}></button>,
    beforeChange: (current, next) => {
      // Check if we're at the beginning or end
      setIsBeginning(next === 0);
      
      // Check if we're at the end
      // We need to calculate this based on total slides and slides to show
      if (topics && topics.data) {
        const totalSlides = topics.data.length;
        const slidesToShow = sliderRef.current?.props?.slidesToShow || 5;
        const lastSlideIndex = Math.max(0, totalSlides - slidesToShow);
        setIsEnd(next >= lastSlideIndex);
      }
    },
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      }
    ]
  };

  // Navigation functions
  const goToNext = () => {
    sliderRef.current.slickNext();
  };

  const goToPrev = () => {
    sliderRef.current.slickPrev();
  };

  // Handle card hover - only for desktop/tablet
  const handleCardMouseEnter = (topicId, event) => {
    if (!isMobile) {
      const cardElement = event.currentTarget;
      const rect = cardElement.getBoundingClientRect();
      
      // Calculate position for dropdown
      const top = rect.bottom + window.scrollY;
      const left = rect.left + (rect.width / 2) + window.scrollX;
      
      setDropdownPosition({ top, left });
      setActiveDropdown(topicId);
    }
  };
  
  // Handle card click for navigation using Next.js router for instant navigation
  const handleCardClick = (topicName) => {
    // Navigate to alltopics page with topic filter using Next.js router
    router.push({
      pathname: '/perspectives/alltopics',
      query: { topic: topicName.replace(/\s+/g, '_'), type: 'All' }
    });
  };

 
  // If topics data isn't available yet, show a loading indicator or placeholder
  if (!topics || !topics.data) {
    return <div className={styles.loadingContainer}>Loading topics...</div>;
  }

  return (
    <div className={`${isMobile ? styles.mobileContainer : ''} ${isMobile ? styles.containerOverrideHidden : ''}`} style={{width:`var(--isb-edge-width-1200)`, margin:"0 auto", overflow: isMobile ? "visible" : "hidden" }}>
      <div className={styles.sliderContainer}>
        <div className={styles.sliderWrapper}>
          {/* Custom Navigation Arrows - only for desktop */}
          {!isMobile && (
            <button 
              className={`${styles.navButton} ${styles.prevButton}`} 
              onClick={goToPrev} 
              aria-label="Previous"
              style={{ display: isBeginning ? 'none' : 'flex' }}
            >
              <div className={styles.rotatedChevron}>
                <Image src={chevronRight} alt="Previous" width={15} height={15} />
              </div>
            </button>
          )}
      
          <div className={styles.sliderOverflowFix}>
            <Slider ref={sliderRef} {...settings} className={styles.slider}>
              {topics && topics.data?.map((topic, index) => {
                const topicName = topic.attributes?.topic_name || 'Untitled Topic';
                const topicId = topic.id;
                const topicImage = topic.attributes?.topic_image?.data?.attributes;
                const topics_slider_bool = topic.attributes?.topics_slider;
      
                return (
                  <div key={topicId || index} className={`${styles.slideContainer}`}>
                    <div
                      className={styles.card}
                      onMouseEnter={(e) => handleCardMouseEnter(topicId || index, e)}
                      onClick={() => handleCardClick(topicName)}
                    >
                      <div className={styles.cardImageContainer}>
                        {topicImage && (
                          <Image
                            src={`${apiUrl}${topicImage?.url}`}
                            alt={topicImage?.alternativeText || topicName}
                            width={250}
                            height={120}
                            className={styles.cardImage}
                            priority={index < 2} // Prioritize loading first two images
                            loading={index < 2 ? "eager" : "lazy"}
                          />
                        )}
                      </div>
                      <div className={styles.cardContent}>
                        <Link 
                          href={{
                            pathname: '/perspectives/alltopics',
                            query: { topic: topicName.replace(/\s+/g, '_'), type: 'All' }
                          }} 
                          style={{textDecoration: 'none', color: 'inherit', pointerEvents: isMobile ? 'none' : 'auto'}} 
                          onClick={(e) => isMobile && e.preventDefault()}
                          scroll={false}
                        >
                          <h3 className={styles.topicCardTitle}>{topicName}</h3>
                        </Link>
                        <div>
                          {topic.attributes?.dropdown_content?.slice(0, 2).map((content, index) => {
                            return (
                              <React.Fragment key={`content-item-${index}`}>
                                <Link 
                                  className='text-decoration-none' 
                                  href={`/perspectives/${content.post_type}/${content.post_id}`}
                                  onClick={(e) => {
                                    e.stopPropagation(); // Prevent event from bubbling up to card onClick
                                  }}
                                >
                                  <p className={`${styles.dropdownContent} ps-0`}>{content.post_title}</p>
                                </Link>
                                {index === 0 && topic.attributes?.dropdown_content.length > 1 && (
                                  <div className={styles.dropdownDivider}></div>
                                )}
                              </React.Fragment>
                            )
                          })}
                        </div>
                          {topic.attributes?.dropdown_content.length>0 && 
                            <Link 
                              className='text-decoration-none' 
                              href={{
                                pathname: '/perspectives/alltopics',
                                query: { topic: topicName.replace(/\s+/g, '_'), type: 'All' }
                              }} 
                              scroll={false}
                              onClick={(e) => {
                                e.stopPropagation(); // Prevent event from bubbling up to card onClick
                              }}
                            >
                              <p className='d-flex align-items-center gap-2 justify-content-end' style={{ textAlign:"right", fontSize:"12px", padding:"10px 0", marginRight:"0px"}}>View All <Image src={viewallarrow} alt="view all" width={20} height={20}/></p>
                            </Link>
                          }
                      </div>
                    </div>
                  </div>
                );
              })}
            </Slider>
          </div>
      
          {/* Custom Navigation Arrows - only for desktop */}
          {!isMobile && (
            <button 
              className={`${styles.navButton} ${styles.nextButton}`} 
              onClick={goToNext} 
              aria-label="Next"
              style={{ display: isEnd ? 'none' : 'flex' }}
            >
              <Image src={chevronRight} alt="Next" width={15} height={15} />
            </button>
          )}
        </div>
        {/* Separate Dropdown Layer */}
        {topics && topics.data?.map((topic, index) => {
          const topicId = topic.id;
          const isActive = activeDropdown === (topicId || index);
          const relatedItems = topic.attributes?.related_items || [];
      
          if (!isActive) return null;
      
          const dropdownStyle = {
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
          };
      
          return (
            <div
              key={`dropdown-${topicId || index}`}
              className={styles.detachedDropdown}
              style={dropdownStyle}
            >
              <ul className={styles.cardList}>
                {
                  relatedItems.slice(0, 5).map((item, itemIndex) => (
                    <li key={itemIndex}>
                      <Link href={`/perspectives/${item.type}/${item.slug}`}>
                        {item.title}
                      </Link>
                    </li>
                  ))
                }
              </ul>
            </div>
          );
        })}
      </div>
    </div>
  );
}
