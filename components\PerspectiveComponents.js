import Image from 'next/image'
import styles from '../pages/perspectives/article/article.module.css'
import Link from 'next/link'
import { useRouter } from "next/router";

export function ShareArticle({ styles, shareArticle, apiUrl, title }) {
    return (
        <div className={styles.shareArticle}>
            <span className={styles.shareText}>{title}</span>
            <div className={styles.socialIcons}>
                {
                    shareArticle.length > 0 && shareArticle.map((item, index) => (
                        <a key={index} onClick={() => window.open(`${item.icon_url}`, '_blank')} className={styles.shareIcon}>
                            <Image
                                src={`${apiUrl}${item.share_icon.data.attributes.url}`}
                                alt={item.share_text} width={40} height={40} />
                        </a>
                    ))
                }
            </div>
        </div>
    )
}

export function TableOfContents({ styles, articles_body, activeSection, title }) {
    const handleScrollToSection = (e, sectionId) => {
        e.preventDefault();
        const section = document.getElementById(sectionId);
        
        if (section) {
            const headerOffset = 100; 
            const elementPosition = section.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
            
            window.scrollTo({
                top: offsetPosition,
                behavior: "smooth"
            });
        }
    };
    
    return (
        <nav className={styles.tableOfContents}>
            <h4>{title}</h4>
            <ul>
                {articles_body.map((section, index) => (
                    <li key={index} className={activeSection === `section-${index}` ? styles.active : ''}>
                        <a href={`#section-${index}`} onClick={(e) => handleScrollToSection(e, `section-${index}`)}>
                            {section.title}
                        </a>
                    </li>
                ))}
            </ul>
        </nav>
    )
}

export function ProgeammesForYou({ track_details, apiUrl, currentQuery }) {
    return (
        <div className="d-flex gap-3 flex-column">
            {track_details && track_details.map((data, index) => {
                return (
                    <div key={index} className={styles.programmeCard}>
                        <div className={styles.programmeImage}>
                            <Image
                                src={`${apiUrl}${data.attributes.learning_track_short_image.data.attributes.url}`}
                                alt="Management Essentials"
                                width="0"
                                height="0"
                                sizes="100vw"
                                className={styles.image}
                            />
                        </div>
                        <div className={styles.programmeContent}>
                            <h5>{data.attributes.learning_track_name}</h5>
                            <p>{data.attributes.learning_track_short_description}</p>
                            <Link
                                rel="canonical"
                                href={`/${data.attributes.learning_track_id?.replace(/^\//, '')}${currentQuery ? `?${currentQuery}` : ""}`}
                                className={` text-decoration-none text-white align-self-start `}
                            >
                                <button className={styles.exploreBtn}>

                                    Explore more
                                </button>
                            </Link>
                        </div>
                    </div>
                )
            })}
        </div>
    )
}
export function RecommendedForYou({ combinedArray, apiUrl, currentQuery, router , title, readTimePermin, date}) {
    return (
        <div>
            {combinedArray.length > 0 &&
                <div className={styles.relatedArticles}>
                    <h3 className={styles.relatedHead}>{title}</h3>
                    <div className="d-flex gap-4 justify-content-md-start justify-content-center row ms-0">
                        {combinedArray.map((article, index) => (
                            <div key={index} className={`${styles.relatedCard} col-md-4 col-12 px-0 `}
                                onClick={() => router.push(`${article.attributes.post_id}${currentQuery ? `?${currentQuery}` : ""}`)}
                            >
                                {(article.attributes.home_page_image?.data?.attributes?.url) ? (
                                    <div className={styles.programmeImage}>
                                        <Image
                                            src={`${apiUrl}${article.attributes.home_page_image.data.attributes.url}`}
                                            alt={article.attributes.post_name}
                                            fill
                                            sizes='100vw'
                                            className={styles.image}
                                        />
                                        <div className={styles.iconWrapper}>
                                            <Image
                                                src={`${apiUrl}${article.attributes.category_icon.data.attributes.url}`}
                                                alt="Bookmark"
                                                width={40}
                                                height={40}
                                            />
                                        </div>
                                    </div>
                                ) : null}

                                <div className={`${styles.articleContent} d-flex flex-column flex-grow-1`}>
                                    <div>
                                        <p className={styles.category}>{article.attributes.subtext}</p>
                                        <h4 className={styles.title}>{article.attributes.post_name}</h4>
                                        <p className={styles.subtext}>{article.attributes.short_summary}</p>
                                    </div>
                                    <div className={`${styles.metaInfo} mt-auto`}>
                                        <span>{article.attributes.author_name}</span>
                                        <span>{date}{readTimePermin >0 && ` | ${readTimePermin} Min`}</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>}
        </div>
    )
}







export const TopicsComponentForSlug = ({ Topics, Post_type, topiclick }) => {
  const router = useRouter();

  const handleTopicClick = (topicName, topiclick) => {
    if(topiclick){
        return null
    }
    // Format topic name to match alltopics.js expectations
    // Replace spaces with + for URL-friendly format
    const formattedTopic = topicName.replace(/\s+/g, '_');
    
    // Capitalize the first letter of each word in the content type
    const formattedType = Post_type ? 
      Post_type.charAt(0).toUpperCase() + Post_type.slice(1) : 
      'All';
    
    // Prepare the query parameters, starting with topic and type
    const queryParams = { 
      topic: formattedTopic,
    //   type: formattedType
      type: 'All'
    };
    
    // Collect any UTM parameters from the current URL
    const currentUrl = new URL(window.location.href);
    const urlParams = new URLSearchParams(currentUrl.search);
    
    // Add all UTM parameters to our query object
    urlParams.forEach((value, key) => {
      if (key.startsWith('utm_')) {
        queryParams[key] = value;
      }
    });
    
    // Use the router.push method with options to prevent re-rendering issues
    router.push(
      {
        pathname: '/perspectives/alltopics',
        query: queryParams
      },
      undefined,
      { shallow: true }
    );
  };

  return (
    <p>
      {Topics &&
        Topics.map((topic, index) => (
          <span
            key={index}
            onClick={() => handleTopicClick(topic.attributes.topic_name || topic[0].attributes.topic_name, topiclick)}
            style={{ cursor: !topiclick && "pointer", fontSize: "14px" }}
          >
            {topic.attributes.topic_name?.toUpperCase() || ''}
            { index + 1 < Topics.length &&  " + " }
          </span>
        ))}
    </p>
  );
};
