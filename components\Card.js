
import React from 'react';
import styles from './CardContainer.module.css';


const Card = ({ content, backgroundImage, lpv2flag }) => {
  const cardStyle = {
    backgroundImage: ` linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url(${backgroundImage})`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    borderRadius: lpv2flag ? "0px":'12px',
  };




  return (
    
        <div  className={`${styles.card}`} style={cardStyle} >
      
       <div className={`${styles.card_desc} text-white font-weight-bold `}
                    dangerouslySetInnerHTML={{
                    __html: content,
                                    }}
        ></div>
       </div>
                             
 
  );
};

export default Card;
