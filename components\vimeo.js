// import { useEffect, useRef } from 'react';

// const VimeoVideo = (props) => {
//   const playerRef = useRef(null);

//   useEffect(() => {
//     const script = document.createElement('script');
//     script.src = 'https://player.vimeo.com/api/player.js';
//     script.async = true;
//     script.defer = true;

//     script.onload = initializePlayer;

//     document.body.appendChild(script);

//     return () => {
//       document.body.removeChild(script);
//     };
//   }, []);

//   const initializePlayer = () => {
//     const options = {
//       url: props.data,
//       autoplay: false,
//     };

//     const player = new Vimeo.Player(playerRef.current, options);
//     // Perform any additional player configuration or event handling here
//   };

//   return (
//     <div className='col-12' ref={playerRef}>
//     </div>
//   );
// };

// export default VimeoVideo;


const VimeoPlayer = (props) => {
  return (
    <iframe
      className="col-lg-6 col-md-12 p-0"
      src={props.data}
      width="640"
      height="360"
      frameBorder="0"
      allowFullScreen
      allow="autoplay"
    />
  );
};
export default VimeoPlayer;
