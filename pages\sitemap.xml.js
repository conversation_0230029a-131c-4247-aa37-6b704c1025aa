const EXTERNAL_DATA_URL = process.env.API_BASE_URL;

function generateSiteMap(posts,host) {
  return `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
   <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
     <!--We manually set the two URLs we know already-->
     <url>
       <loc>https://${host}</loc>
       <lastmod>2023-05-23</lastmod>
		   <changefreq>weekly</changefreq>
		   <priority>1</priority>
     </url>
     
     <url>
		    <loc>https://${host}/about-page</loc>
		    <lastmod>2023-05-23</lastmod>
		    <changefreq>monthly</changefreq>
		    <priority>0.8</priority>
	   </url>

     <url>
		    <loc>https://${host}/isb-online-learner</loc>
		    <lastmod>2023-05-23</lastmod>
		    <changefreq>monthly</changefreq>
		    <priority>0.8</priority>
	   </url>

     <url>
		    <loc>https://${host}/immersive-learning</loc>
		    <lastmod>2023-05-23</lastmod>
		    <changefreq>monthly</changefreq>
		    <priority>0.8</priority>
	   </url>

     <url>
		    <loc>https://${host}/exploring-the-benefits</loc>
		    <lastmod>2023-05-23</lastmod>
		    <changefreq>monthly</changefreq>
		    <priority>0.8</priority>
	   </url>

     <url>
		    <loc>https://${host}/sitemap</loc>
		    <lastmod>2023-05-23</lastmod>
		    <changefreq>monthly</changefreq>
		    <priority>0.8</priority>
	   </url>

     <url>
       <loc>https://${host}/learning-tracks</loc>
       <lastmod>2023-05-23</lastmod>
		   <changefreq>weekly</changefreq>
		   <priority>0.8</priority>
     </url>
     <url>
       <loc>https://${host}/certificates-and-badges</loc>
       <lastmod>2023-05-23</lastmod>
		   <changefreq>weekly</changefreq>
		   <priority>0.9</priority>
     </url>
     <url>
       <loc>https://${host}/lxp-page</loc>
       <lastmod>2023-05-23</lastmod>
		   <changefreq>weekly</changefreq>
		   <priority>0.8</priority>
     </url>
     <url>
       <loc>https://${host}/faq-page</loc>
       <lastmod>2023-05-23</lastmod>
		   <changefreq>weekly</changefreq>
		   <priority>0.8</priority>
     </url>
     
     ${posts.data
       .map(( item,i ) => {
         return `
       <url>
         <loc>${`https://${host}/${item.attributes.learning_track_id}`}</loc>
         <lastmod>2023-05-23</lastmod>
         <changefreq>weekly</changefreq>
         <priority>0.9</priority>
       </url>
       ${item.attributes.courses.data
        .map(( course,j ) => {
          return `
        <url>
          <loc>${`https://${host}/${item.attributes.learning_track_id}/${course.attributes.courseid}`}</loc>
          <lastmod>2023-05-23</lastmod>
          <changefreq>weekly</changefreq>
          <priority>0.9</priority>
        </url>
      `;
        })
        .join('')}
     `;
       })
       .join('')}
   </urlset>
 `;
}

function SiteMap() {
  // getServerSideProps will do the heavy lifting
}

export async function getServerSideProps({ res,req }) {

  const APIUrl = process.env.API_BASE_URL;
  let host = req.headers.host
  // We make an API call to gather the URLs for our site
  const request = await fetch(`${APIUrl}/api/learning-tracks?populate=*`);
  const posts = await request.json();
  // We generate the XML sitemap with the posts data
  const sitemap = generateSiteMap(posts,host);

  res.setHeader('Content-Type', 'text/xml');
  // we send the XML to the browser
  res.write(sitemap);
  res.end();

  return {
    props: {},
  };
}

export default SiteMap;