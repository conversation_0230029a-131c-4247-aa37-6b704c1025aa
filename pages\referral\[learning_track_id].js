import React, { useEffect, useState } from 'react';
import useCollapse from "react-collapsed";
import { useRouter } from 'next/router';
import Image from 'next/image';
import Link from 'next/link';
import Modal from 'react-bootstrap/Modal';
import Button from 'react-bootstrap/Button';
import Form from 'react-bootstrap/Form';
import { NextSeo } from 'next-seo';
import appLogo from '../../assets/ISB_Online Logo.png';
import classes from './referral.module.css';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import "react-phone-input-2/lib/style.css";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
// Icons for collapse component



const ReferralPage = (props) => {
    const router = useRouter();
    const { learning_track_id } = router.query;
    const [learningTrackData, setLearningTrackData] = useState(null);
    const [loading, setLoading] = useState(true);

    // Form state
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        country: 'India'
    });

    // Form validation state
    const [validated, setValidated] = useState(false);

    // Modal state
    const [showModal, setShowModal] = useState(false);
    const [generatedLink, setGeneratedLink] = useState('');
    const [emailList, setEmailList] = useState('');
    const [linkCopied, setLinkCopied] = useState(false);
    const [invitationSent, setInvitationSent] = useState(false);

    // Sample data for syllabus section
    const [landingPageResponse, setLandingPageResponse] = useState({
        programme_course_title: "Senior Management Programme",
        syllabus_note: "*The programme syllabus is subject to change based on faculty discretion.",
        courses: [
            {
                "id": 1,
                "attributes": {
                  "course_title": "Leadership and Strategic Thinking",
                  "landingpage_course_description": "<p>Define and articulate your personal brand.</p><p>Create relatable and impactful narratives.</p><p>Present your brand authentically in interviews, social media, and professional interactions.</p><p>Explore personal branding journeys of global icons, applying their strategies to enhance your own brand.</p>"
                }
              },
              
            
            {
                "id": 2,
                "attributes": {
                  "course_title": "Financial Management for Executives",
                  "landingpage_course_description": "<p>Learn advanced financial concepts and decision-making frameworks for organizational growth.</p>"
                }
              },
            {
                "id": 3,
                "attributes": {
                  "course_title": "Digital Transformation and Innovation",
                  "landingpage_course_description": "<p>Understand how to lead digital transformation initiatives and foster innovation in your organization.</p>"
                }
              }
        ]
    });

    // Handle form input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value
        });
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();
        const form = e.currentTarget;

        if (form.checkValidity() === false) {
            e.stopPropagation();
            setValidated(true);
            return;
        }

        // Generate link with form data
        const params = new URLSearchParams();
        params.append('ref', formData.name);
        params.append('email', formData.email);
        params.append('phone', formData.phone);

        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || window.location.origin;
        const generatedUrl = `${baseUrl}/${learning_track_id}?${params.toString()}`;

        setGeneratedLink(generatedUrl);
        setShowModal(true);
        setValidated(true);
    };

    // Handle copy link
    const handleCopyLink = () => {
        navigator.clipboard.writeText(generatedLink);
        setLinkCopied(true);
        setTimeout(() => setLinkCopied(false), 3000);
    };

    // Handle send invitation
    const handleSendInvitation = () => {
        // Here you would implement the email sending functionality
        // For now, we'll just simulate it
        setInvitationSent(true);
        setTimeout(() => setInvitationSent(false), 3000);
    };

    // Fetch learning track data based on ID
    useEffect(() => {
        const fetchLearningTrackData = async () => {
            if (learning_track_id) {
                try {
                    setLoading(true);
                    const url = process.env.NEXT_PUBLIC_API_BASE_URL;
                    const res = await fetch(`${url}/api/learning-tracks?filters[learning_track_id][$eq]=${learning_track_id}&populate=deep,4`);
                    const data = await res.json();

                    if (data?.data && data.data.length > 0) {
                        setLearningTrackData(data.data[0].attributes);
                    }
                    setLoading(false);
                } catch (error) {
                    console.error('Error fetching learning track data:', error);
                    setLoading(false);
                }
            }
        };

        fetchLearningTrackData();
    }, [learning_track_id]);

    return (
        <>
            <NextSeo
                title={learningTrackData?.seo?.metaTitle || `Refer a Friend | ISB Executive Education`}
                description={learningTrackData?.seo?.metaDescription || `Refer a friend to ISB Executive Education programs and earn rewards.`}
                canonical={`${process.env.NEXT_PUBLIC_BASE_URL}/referral/${learning_track_id}`}
                openGraph={{
                    url: `${process.env.NEXT_PUBLIC_BASE_URL}/referral/${learning_track_id}`,
                    title: learningTrackData?.seo?.metaTitle || `Refer a Friend | ISB Executive Education`,
                    description: learningTrackData?.seo?.metaDescription || `Refer a friend to ISB Executive Education programs and earn rewards.`,
                    images: [
                        {
                            url: learningTrackData?.seo?.metaImage?.data?.attributes?.url || '',
                            width: 800,
                            height: 600,
                            alt: 'ISB Executive Education',
                        },
                    ],
                }}
            />

            <section style={{ backgroundColor: '#0047AB', padding: '60px 0', color: 'white' }}>
                <div className="container" style={{ maxWidth: '1200px', margin: '0 auto' }}>
                    <div style={{ maxWidth: '1300px', margin: '0 auto' }}>
                        <h1 style={{ fontSize: '2rem', fontWeight: 'bold', textAlign: 'center', marginBottom: '1.5rem' }}>
                            Invite Your Colleagues to the IIM Kozhikode Experience
                        </h1>
                        <p style={{ fontSize: '1.2rem', textAlign: 'center', marginBottom: '2rem', color: 'white' }}>
                            Receive up to ₹22,000 off the programme fee for each person you refer to Senior Management Programme
                        </p>
                        <div style={{ maxWidth: '1100px', margin: '0 auto' }}>

                            <Form noValidate validated={validated} onSubmit={handleSubmit} className="mt-4">
                                <div className="row justify-content-center">
                                    <div className="col-md-10">

                                        {/* First Row: Name & Email */}
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <Form.Group controlId="formName">
                                                    {/* <Form.Label>Name</Form.Label> */}
                                                    <Form.Control
                                                        type="text"
                                                        placeholder="Name"
                                                        name="name"
                                                        value={formData.name}
                                                        onChange={handleInputChange}
                                                        required
                                                        className="py-2"
                                                    />
                                                </Form.Group>
                                            </div>

                                            <div className="col-md-6">
                                                <Form.Group controlId="formEmail">
                                                    {/* <Form.Label>Email</Form.Label> */}
                                                    <Form.Control
                                                        type="email"
                                                        placeholder="Email"
                                                        name="email"
                                                        value={formData.email}
                                                        onChange={handleInputChange}
                                                        required
                                                        className="py-2"
                                                    />
                                                </Form.Group>
                                            </div>
                                        </div>

                                        {/* Second Row: Country & Phone */}
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <Form.Group controlId="formCountry">
                                                    {/* <Form.Label>Country/Region</Form.Label> */}
                                                    <Form.Select
                                                        name="country"
                                                        value={formData.country}
                                                        onChange={handleInputChange}
                                                        required
                                                        className="py-2"
                                                    >
                                                        <option value="India">India</option>
                                                        <option value="United States">United States</option>
                                                        <option value="United Kingdom">United Kingdom</option>
                                                        <option value="Singapore">Singapore</option>
                                                        <option value="Other">Other</option>
                                                    </Form.Select>
                                                </Form.Group>
                                            </div>

                                            <div className="col-md-6">
                                                <Form.Group controlId="formPhone">
                                                    {/* <Form.Label>Phone</Form.Label> */}
                                                    <Form.Control
                                                        type="tel"
                                                        placeholder="+91 7755522222"
                                                        name="phone"
                                                        value={formData.phone}
                                                        onChange={handleInputChange}
                                                        required
                                                        className="py-2"
                                                    />
                                                </Form.Group>
                                            </div>
                                        </div>

                                        {/* Generate Button */}
                                        <div className="text-center mt-4">
                                            <Button
                                                type="submit"
                                                style={{
                                                    backgroundColor: '#ffffff',
                                                    color: '#0047AB',
                                                    border: 'none',
                                                    maxWidth: '300px',
                                                    margin: '0 auto',
                                                    display: 'block'
                                                }}
                                                size="lg"
                                            >
                                                Generate Unique Link
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </Form>
                        </div>

                    </div>
                </div>
            </section>

            <section style={{ padding: '20px 0', backgroundColor: '#f8f9fa' }}>
                <div className="container" style={{ maxWidth: '1100px', margin: '0 auto' }}>
                    <h2 className="text-center mb-5">How does it work?</h2>
                    <div className="row">
                        <div className="col-md-4 mb-4">
                            <div style={{ textAlign: 'center', padding: '20px' }}>
                                <div style={{
                                    width: '80px',
                                    height: '80px',
                                    margin: '0 auto 20px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: '#e6f0ff',
                                    borderRadius: '50%',
                                    color: '#0047AB'
                                }}>
                                    <i className="fas fa-link fa-2x"></i>
                                </div>
                                <h3 style={{ fontWeight: 'bold', marginBottom: '15px' }}>Invite your colleagues</h3>
                                <p>Share your personalized referral link or send an email to invite colleagues directly using our referral system.</p>
                            </div>
                        </div>
                        <div className="col-md-4 mb-4">
                            <div style={{ textAlign: 'center', padding: '20px' }}>
                                <div style={{
                                    width: '80px',
                                    height: '80px',
                                    margin: '0 auto 20px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: '#e6f0ff',
                                    borderRadius: '50%',
                                    color: '#0047AB'
                                }}>
                                    <i className="fas fa-user-plus fa-2x"></i>
                                </div>
                                <h3 style={{ fontWeight: 'bold', marginBottom: '15px' }}>Benefit for your colleagues</h3>
                                <p>When your colleague enrolls in any programme using your referral code, they receive up to ₹22,000 off the programme fee.</p>
                            </div>
                        </div>
                        <div className="col-md-4 mb-4">
                            <div style={{ textAlign: 'center', padding: '20px' }}>
                                <div style={{
                                    width: '80px',
                                    height: '80px',
                                    margin: '0 auto 20px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: '#e6f0ff',
                                    borderRadius: '50%',
                                    color: '#0047AB'
                                }}>
                                    <i className="fas fa-gift fa-2x"></i>
                                </div>
                                <h3 style={{ fontWeight: 'bold', marginBottom: '15px' }}>Benefit for you</h3>
                                <p>For every colleague you refer, you will receive a benefit of up to ₹22,000 off the programme fee.</p>
                            </div>
                        </div>
                    </div>
                    <div className="text-center mt-4">
                        <p className="small">Review the terms of the referral programme</p>
                    </div>
                </div>
            </section>


           

              <div className="px-2 bg-white">
                    <div
                      className={`col-lg-12 col-md-12 col-12 mx-auto p-2 ${classes.equalPadding}`}>
                      <h2 className={`text-center ${classes.main_blue_head} py-4 m-0`}>
                        {landingPageResponse.programme_course_title}
                      </h2>
            
                      {landingPageResponse.courses.map(
                        (item, i) => {
                          return <Collapse key={item.id} index={i} {...item}  />;
                        }
                      )}
            
                      {/* <p className={`px-2 px-lg-0  ${classes.bdc_note_mobile}`} style={{fontStyle:"italic",fontFamily: "sans-serif"}}>{landingPageResponse.syllabus_note}</p> */}
                    </div>
                    {/* <div className={`d-flex justify-content-center bg-white pb-3 ${learnTrackData.story_fold?.length ===0 ? "pb-5":"pb-3"}`}>
                    <DownloadBrocureBtn scrollToTop={scrollToTop} thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse}/>
                    </div> */}
                  </div>


                   <section style={{ backgroundColor: '#0047AB', padding: '60px 0', color: 'white' }}>
                <div className="container" style={{ maxWidth: '1200px', margin: '0 auto' }}>
                    <div style={{ maxWidth: '1300px', margin: '0 auto' }}>
                        <h1 style={{ fontSize: '2rem', fontWeight: 'bold', textAlign: 'center', marginBottom: '1.5rem' }}>
                            Invite Your Colleagues to the IIM Kozhikode Experience
                        </h1>
                        <p style={{ fontSize: '1.2rem', textAlign: 'center', marginBottom: '2rem', color: 'white' }}>
                            Receive up to ₹22,000 off the programme fee for each person you refer to Senior Management Programme
                        </p>
                        <div style={{ maxWidth: '1100px', margin: '0 auto' }}>

                            <Form noValidate validated={validated} onSubmit={handleSubmit} className="mt-4">
                                <div className="row justify-content-center">
                                    <div className="col-md-10">

                                     
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <Form.Group controlId="formName">
                                                  
                                                    <Form.Control
                                                        type="text"
                                                        placeholder="Name"
                                                        name="name"
                                                        value={formData.name}
                                                        onChange={handleInputChange}
                                                        required
                                                        className="py-2"
                                                    />
                                                </Form.Group>
                                            </div>

                                            <div className="col-md-6">
                                                <Form.Group controlId="formEmail">
                                                   
                                                    <Form.Control
                                                        type="email"
                                                        placeholder="Email"
                                                        name="email"
                                                        value={formData.email}
                                                        onChange={handleInputChange}
                                                        required
                                                        className="py-2"
                                                    />
                                                </Form.Group>
                                            </div>
                                        </div>

                                       
                                        <div className="row mb-3">
                                            <div className="col-md-6">
                                                <Form.Group controlId="formCountry">
                                                  
                                                    <Form.Select
                                                        name="country"
                                                        value={formData.country}
                                                        onChange={handleInputChange}
                                                        required
                                                        className="py-2"
                                                    >
                                                        <option value="India">India</option>
                                                        <option value="United States">United States</option>
                                                        <option value="United Kingdom">United Kingdom</option>
                                                        <option value="Singapore">Singapore</option>
                                                        <option value="Other">Other</option>
                                                    </Form.Select>
                                                </Form.Group>
                                            </div>

                                            <div className="col-md-6">
                                                <Form.Group controlId="formPhone">
                                                   
                                                    <Form.Control
                                                        type="tel"
                                                        placeholder="+91 7755522222"
                                                        name="phone"
                                                        value={formData.phone}
                                                        onChange={handleInputChange}
                                                        required
                                                        className="py-2"
                                                    />
                                                </Form.Group>
                                            </div>
                                        </div>

                                      
                                        <div className="text-center mt-4">
                                            <Button
                                                type="submit"
                                                style={{
                                                    backgroundColor: '#ffffff',
                                                    color: '#0047AB',
                                                    border: 'none',
                                                    maxWidth: '300px',
                                                    margin: '0 auto',
                                                    display: 'block'
                                                }}
                                                size="lg"
                                            >
                                                Generate Unique Link
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </Form>
                        </div>

                    </div>
                </div>
            </section>



      

            {/* Link Generation Modal */}
            <Modal
                show={showModal}
                onHide={() => setShowModal(false)}
                centered
                style={{ color: '#333' }}
            >
                <Modal.Header closeButton>
                    <Modal.Title>Invite your colleagues</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div>
                        <h5>Share your unique link</h5>
                        <p>Copy your personal referral link below to share with colleagues.</p>
                        <div className="input-group mb-3">
                            <Form.Control
                                type="text"
                                value={generatedLink}
                                readOnly
                            />
                            <Button
                                variant="primary"
                                onClick={handleCopyLink}
                                style={{
                                    backgroundColor: '#0047AB',
                                    border: 'none'
                                }}
                            >
                                {linkCopied ? 'Copied!' : 'Copy Link'}
                            </Button>
                        </div>
                    </div>

                    <div style={{ marginTop: '30px', borderTop: '1px solid #dee2e6', paddingTop: '20px' }}>
                        <h5>Share via email</h5>
                        <p>Enter multiple comma-separated emails to share with colleagues.</p>
                        <div className="input-group mb-3">
                            <Form.Control
                                type="text"
                                placeholder="To:"
                                value={emailList}
                                onChange={(e) => setEmailList(e.target.value)}
                            />
                            <Button
                                variant="primary"
                                onClick={handleSendInvitation}
                                style={{
                                    backgroundColor: '#0047AB',
                                    border: 'none'
                                }}
                                disabled={!emailList}
                            >
                                {invitationSent ? 'Sent!' : 'Send Invitation'}
                            </Button>
                        </div>
                    </div>
                </Modal.Body>
            </Modal>
        </>
    );
};







  const C = (
    <FontAwesomeIcon
      icon={faMinus}
      className="fa-solid fa-minus"
      style={{ color: "#7C8495" }}
    />
  );
  const E = (
    <FontAwesomeIcon
      icon={faPlus}
      className="fa-solid fa-plus"
      style={{ color: "#7C8495" }}
    />
  );


  const Collapse = ({ index, ...data }) => {
    
    const { getCollapseProps, getToggleProps, isExpanded } = useCollapse();

    return (
      <>
        <div>
          <h5
            className={`${classes.faqQuestion} d-flex py-2 justify-content-between text-black m-0 mb-2`}
            {...getToggleProps()}
          >
            {`Course ${index+1}: ${data.attributes.course_title}`}

            {isExpanded ? C : E}
          </h5>
          <div
            {...getCollapseProps()}
            className={classes.collapseWrapper}
          >
            <div className={`${classes.faqAnswer} ${classes.collapseContent} ${isExpanded ? classes.expanded : ''} p-0 m-0 text-white ${classes.liStyle}`}
             dangerouslySetInnerHTML={{
                 __html: data.attributes.landingpage_course_description
                 }}
                ></div>
          </div>
        </div>
        <div className={`mb-3 p-0 ${classes.foottitle}`}></div>
      </>
    );
  };






export async function getServerSideProps(context) {
    const { learning_track_id } = context.params;

    return {
        props: {
            meetupId: learning_track_id,
        },
    };
}

export default ReferralPage;
