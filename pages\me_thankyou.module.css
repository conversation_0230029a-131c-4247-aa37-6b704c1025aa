.blueHeading{
    font-weight: 700;
    font-size: 24px;
    line-height: 54.47px;
    color: #057092;
  }
  .linearGradient{
    background: linear-gradient(90deg, #735FC8 0%, #0096E6 48.05%, #2DAF8C 100.27%);
}
.equalPadding{
    max-width: 1000px;
  }
  .whiteHeading{
    font-weight: 700;
    font-size: 26px;
    line-height: 35.47px;
    color: #fff;
  }
  .fold1bg {
    background-image: url('../assets/me_thank_bg.svg');
    background-size: cover;
    height: 500px;
    background-repeat: no-repeat;

  }
  .showBtn
  {
      border:none;
      padding: 8px 36px 8px 36px;
      font-weight: 600;
      flex-wrap: wrap;
      margin-right: 6px;
      margin-left: 6px;
      border-radius: 0px!important;
      color: #FFFFFF;
      color: #057092;
      background:white;
      width: 275px;
      height: 45px;
  }
.imagePadding{
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
    text-align: center;
 
}
.imagePad{
  padding-top: 140px;
  padding-bottom: 120px;
}
.text{
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 700;
        font-size: 25px;
        text-align: center;
        color: white;
        padding: 10px;
        justify-content: center;
    }

.sub_text{
font-family: Open Sans;
font-size: 15px;
font-weight: 600;
text-align: center;
color: white;
}
.boxshadow{
        border: none;
        border-radius: 0%;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12); 
}