import ThankYouPage from "../../../lpv2/[learning_track_id]/thankyou";

export default function thankyouPage(props) {
  return (
    <div>
     
      <ThankYouPage 
      apiData={props.apiDatav2} 
      stickyBanner={props.stickyBanner} 
      trackFold={props.trackFold} 
      landingPageResponse={props.landingPageResponse} 
      Url={props.apiUrl} 
      baseurl={props.baseurl}
      meetupId={props.meetupId}
      apiUrl={props.apiUrl}
      />
     
    </div>
  );
}

export const getStaticPaths = async (context) => {
  const { req, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;
  const res = await fetch(`${APIUrl}/api/landingpages?populate=deep,3`);
  const response = await res.json();
  const paths = response.data.map((course) => {
    return {
      params: {
        learning_track_id: course.attributes.learning_track_id.toString(),
    
      },
    };
  });
  return {
    paths,
    fallback: false,
  };
};

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const meetupId = context.params.learning_track_id;

  const APIUrl = process.env.API_BASE_URL;
  const Baseurl = process.env.NEXT_PUBLIC_BASE_URL;
  const [tracklandingpage, tqData, bottomFoldData, stickyBanner, tracklandingpagev2, trackFold] = await Promise.all([
    fetch(
      `${APIUrl}/api/landingpages?filters[learning_track_id][$eq]=${meetupId}&populate=deep,3`
    ).then((r) => r.json()),

    fetch(`${APIUrl}/api/me-thankyou?populate=deep,3`).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),



    // v2 api calls


    fetch(`${APIUrl}/api/sticky-banners?populate=deep,4`).then((r) => r.json()),
    fetch(`${APIUrl}/api/landing-pagev2s?filters[learning_track_id][$eq]=${meetupId}&populate=deep,5`).then((r) => r.json()),
    fetch(`${APIUrl}/api/new-track-page-fold?populate[apply_to_lt_title][populate]=*`).then((r) => r.json()),


  ]);

  return {
    props: {
      apiData: tracklandingpage,
      tqData: tqData,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData,
      learning_track_id:meetupId,

      // v2 data
      baseurl: Baseurl,
      stickyBanner: stickyBanner,
      apiDatav2: tracklandingpagev2,
      trackFold: trackFold,
    },
    revalidate: 120,
  };
}
