import Link from "next/link";
import Image from "next/image";
import classes from "./index.module.css";
import TealBackground from "../assets/TealBackground.webp";
import { useRef } from "react";
import { useRouter } from "next/router";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import BottomFold from "../components/bottom_fold_one";
import React, { useState } from "react";
import Head from "next/head";
import { NextSeo } from "next-seo";
export default function Home(props) {

  const router = useRouter();
  const { query } = router;

  const sliderRef = useRef(null);
  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
  };

  const landingPageResponse = props.homePageContent.data.attributes.home_achievements
  const navdData = props.navdData.data[0].attributes;
  const featuredcourses = props.featuredcourses.data;
  const pageData = props.homePageContent.data.attributes;
  const image = props.homePageContent.data.attributes.home_fold_one_media;
  const newsFoldImage = props.homePageContent.data.attributes.news_fold_image;
  const nwlImage = props.homePageContent.data.attributes.nwl_content;
  const insights = props.homePageContent.data.attributes.insights;
  const qpms = query.utm_source != undefined ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}` : ``
  const [selectedItems, setSelectedItems] = useState(props.homePageContent.data.attributes.home_highlights.slice(0, 3));
  const baseURL = process.env.NEXT_PUBLIC_BASE_URL;
  const learningTracks = pageData.learning_tracks.data;
  const metaTags = props.homePageContent.data.attributes.meta_tags;
  function generateWebsiteSchema(baseURL) {
    // Website schema
    // const websiteSchema = {
    //   "@context": "https://schema.org",
    //   "@type": "WebSite",
    //   "name": "ISB Online",
    //   "url": baseURL,
    //   "potentialAction": {
    //     "@type": "SearchAction",
    //     "target": `${baseURL}/search?q={search_term_string}`,
    //     "query-input": "required name=search_term_string"
    //   }
    // };
   
    const navigationSchema = {
      "@context": "https://schema.org",
      "@type": "ItemList",
      "itemListElement": [
        {
          "@type": "SiteNavigationElement",
          "position": 1,
          "name": "Home",
          "url": baseURL
        },
        {
          "@type": "SiteNavigationElement",
          "position": 2,
          "name": "About",
          "url": `${baseURL}/about-page`
        },
        // Ensure learningTracks is an array before mapping
        ...(Array.isArray(learningTracks)
          ? learningTracks.map((item, index) => ({
              "@type": "SiteNavigationElement",
              "position": index + 3, // Start at 3 after Home & About
              "name": item.attributes?.learning_track_name || "",
              "url": `${baseURL}${item.attributes?.learning_track_id ? '/' + item.attributes.learning_track_id : ''}`
            }))
          : [])
      ]
    };
  
    return [navigationSchema];
  }
  
  // Use a consistent image URL that Twitter can access
  return (
    <>
    <Head>
      {generateWebsiteSchema(baseURL).map((schema, index) => (
        <script
          key={`schema-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema)
          }}
        />
      ))}
    </Head>
     <Head>
      <meta name="twitter:card" content="summary" />
      <meta property="twitter:title" content={metaTags.title} />
      <meta name="twitter:image" content={process.env.NEXT_PUBLIC_API_BASE_URL + metaTags.image.data?.attributes?.url} />
      <meta property="twitter:description" content={metaTags.description} />
    </Head>
    <NextSeo
      title={metaTags.title}
      description={metaTags.description}
      twitter={{
        handle: '@ISBOnline',
        cardType: 'summary',
        site: '@ISBOnline',
        image: `${process.env.NEXT_PUBLIC_BASE_URL}/images/twitter-card.png`,
      }}
      openGraph={{
        title: metaTags.title,
        description: metaTags.description,
        images: [
          {
            url: process.env.NEXT_PUBLIC_API_BASE_URL + metaTags.image.data?.attributes?.url,
            width: 800,
            height: 210,
            alt: 'ISB Online Logo',
          },
        ],
      }}
    />
      <div className={classes.bgImage}>
        <Image
          src={TealBackground}
          alt="tealBackfround"
          fill
          style={{
            objectFit: 'cover',
            objectPosition: 'center',
          }}
          priority={true}
        />
        <div className={`${classes.contentFold} ${classes.equalPadding} row py-lg-5 flex-row-reverse p-0 mx-auto`}>
          <div className="col-lg-6 col-md-12 p-0">
            {pageData.home_fold_one_btn_url != null ? <iframe className="col-lg-12 col-md-12 p-0" width="640" height="360" src={`https://player.vimeo.com/video/816447427?h=55124c22aa&amp;badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowFullScreen style="position:absolute;top:0;left:0;width:100%;height:100%;" title="Leadership Essentials"`} allowFullScreen={true} allow="autoplay; fullscreen; picture-in-picture"></iframe> :
              <Image
                width="0"
                height="0"
                sizes="100vw"
                className={`img-fluid w-100 h-auto`}
                src={props.apiUrl + image.data.attributes.formats.small.url}
                priority={true}
                alt={image.data.attributes.alternativeText}
              />
            }
          </div>
          <div
            className={`${classes.secstart} col-lg-6 col-md-12 py-lg-0 py-md-4 py-sm-4 py-4 px-3 d-flex flex-column justify-content-between`}>
            <div>
              <h1 className={classes.heading}>
                <b>{pageData.home_fold_one_title}</b>
              </h1>
              <h2 className={`my-4 text-white ${classes.description}`}>
                {pageData.home_fold_one_description}
              </h2>
            </div>
            <div>
              <Link rel="canonical" href={`#LTs`} scroll={false}>
                <button
                  type="button"
                  className={`${classes.fold1btn} btn btn-primary`}>
                  {pageData.home_fold_one_btn_title}{" "}
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_3275_9282)">
                      <path
                        d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_3275_9282">
                        <rect width="24" height="24" fill="#057092" />
                      </clipPath>
                    </defs>
                  </svg>
                </button>
              </Link>
              <div className="p-0 m-0" id="LTs"></div>
            </div>
          </div>
        </div>

      </div>
     
      <div>
        <div className={`bg-white p-0 mx-auto`}>
          <div className={`col-lg-6 col-md-10 mx-auto`}>
            <h2 className={`text-center ${classes.blackHeading} m-0 pt-lg-3 pt-5`}>
            Featured Programmes
            </h2>
            <p className={`text-center my-0 px-lg-0 px-md-3 px-sm-3 px-3`}>
              {pageData.learning_tracks_description}
            </p>
          </div>
          <div className={`row ${classes.equalPadding} mx-auto py-lg-4 py-md-4 py-sm-0 py-4 px-lg-0 px-md-3 px-sm-3 px-3 `}>
            {pageData.learning_tracks.data.map((item, i) => {
              const programUrl = navdData.programs_tab_title.programs.find((program) => program.url.includes(item.attributes.learning_track_id));
              return (
                <div
                  key={item.id}
                  className={`${i === 0 || i === 2 ? "px-lg-2" : "px-lg-2"
                    } px-1 ${pageData.learning_tracks.data.length <= 3 ?"col-lg-4":"col-lg-3"} mb-0 d-flex align-items-stretch col-md-6 col-sm-12 my-lg-0 my-md-3 my-3 pb-3`}
                >
                 { <div className={`card ${classes.boxshadow} `}>
                {(item.attributes.is_featured_program) && <div className={classes.ribbon_style}>New</div>}
                    <Image
                      width="0"
                      height="0"
                      sizes="100vw"
                      priority={true}
                      src={
                        props.apiUrl +
                        item.attributes.learning_track_short_image.data
                          .attributes.formats.small.url
                      }
                      className={`w-100 h-auto card-img-top ${classes.image_border}`}
                      alt={item.attributes.learning_track_short_image.data
                        .attributes.alternativeText}
                    />


                    <div
                      className={`card-body d-flex flex-column ${classes.buttonPos}`}
                    >
                      <h3 className={`card-title ${classes.learnTrackTitle}`}>
                        {item.attributes.learning_track_name}
                      </h3>
                      <p className={`pb-5 card-text`}>
                        {item.attributes.learning_track_short_description}
                      </p>
                      <div className={`mb-3 ${classes.buttonPosition}`}>
                        <Link
                          rel="canonical"
                          href={`${programUrl?.url || '#'}${qpms}`}
                          className={`btn btn-primary mt-auto align-self-start ${classes.viewbtn}`}
                        >
                          {pageData.learning_track_btn_title} <i className="fa fa-long-arrow-right"></i>
                        </Link>
                      </div>
                    </div>
                  </div>}
                </div>
              );
            })}
          </div>
        </div>

 


{/* ----------------------Futured courses slice-------------------------*/}



      {featuredcourses.length>0 &&  <div className={`bg-white p-0 mx-auto`}>
          <div className={`col-lg-6 col-md-10 mx-auto`}>
            <h2 className={`text-center ${classes.blackHeading} m-0 pt-lg-3 pt-5`}>
            {pageData.featured_courses_title}
            </h2>
            
          </div>
          <div className={`row ${classes.equalPadding} justify-content-center mx-auto py-lg-4 py-md-4 py-sm-0 py-4 px-lg-0 px-md-3 px-sm-3 px-3 `}>
            {featuredcourses.map((item, i) => {
              return (
                <div
                  key={item.id}
                  className={`px-lg-2 px-1 ${featuredcourses.length <=3 ? "col-lg-4":"col-lg-3"} mb-0 d-flex align-items-stretch col-md-6 col-sm-12 my-lg-0 my-md-3 my-3 `}
                >
                  {<div className={`card ${classes.boxshadow} `}>
                 {item.attributes.is_featured_course && <div className={classes.ribbon_style}>New</div>}
                    <Image
                      width="0"
                      height="0"
                      sizes="100vw"
                      priority={true}
                      src={
                        props.apiUrl +
                        item.attributes.course_short_image.data
                          .attributes.formats.small.url
                      }
                      className={`w-100 h-auto card-img-top ${classes.image_border}`}
                      alt={item.attributes.course_short_image.data
                        .attributes.alternativeText}
                    />


                    <div
                      className={`card-body d-flex flex-column ${classes.buttonPos}`}
                    >
                      <h3 className={`card-title ${classes.learnTrackTitle}`}>
                        {item.attributes.course_title}
                      </h3> 
                      <p className={`pb-5 card-text`}>
                        {item.attributes.course_short_description}
                      </p>
                      <div className={`mb-3 ${classes.buttonPosition}`}>
                        <Link
                          rel="canonical"
                          href={`/course/${item.attributes.courseid + qpms}`}
                          className={`btn btn-primary mt-auto align-self-start ${classes.viewbtn}`}
                        >
                          {pageData.learning_track_btn_title} <i className="fa fa-long-arrow-right"></i>
                        </Link>
                      </div>
                    </div>
                  </div>}
                </div>
              );
            })} 
          </div>
        </div>}
       
        <div className={`${classes.bluebackground} p-0`}>
          <div
            className={`col-lg-12 col-md-12 col-12 mx-auto ${classes.equalPadding}`}
          >
            <h2 className={`text-center ${classes.blueHeading} py-4 m-0`}>
              {pageData.nwl_title}
            </h2>
            <div className={`row bg-white align-items-center p-3 mx-lg-5 mx-md-2 mx-3`}>
              <div className="col-lg-6 col-sm-12 p-0">
                <Image
                  width="0"
                  height="0"
                  sizes="100vw"
                  className="img-fluid w-100 h-auto"
                  src={props.apiUrl + nwlImage.data.attributes.url}
                  alt={nwlImage.data.attributes.alternativeText}
                />
              </div>
              <div className="col-lg-6 col-md-12 px-lg-3 px-md-0 px-0">
                <h3 className={classes.nwlTitle}>
                  {pageData.nwl_description_title}
                </h3>
                <p
                  className={`text-align-left`}
                >
                  {pageData.nwl_description}.
                </p>
                <Link
                  rel="canonical"
                  href={`/lxp-page${qpms}`}
                  className={`${classes.newwaylearnbtn} btn btn-primary`}
                >
                  {pageData.nwl_button_title}{""}
                </Link>
              </div>
            </div>
            <div className="py-4"></div>
          </div>
        </div>
      </div>


      <div className={`${classes.bluebackground1} p-0`}>
        <div
          className={`col-lg-12 col-md-12 col-12 mx-auto ${classes.equalPadding}`}
        >
          <h2 className={`text-center ${classes.blueHeading} py-4 m-0`}>
            {/* {pageData.nwl_title} */}
          </h2>

          <div className={`${classes.card_main} px-4 px-lg-0 p-0 card `}>
            <div className={` d-flex justify-content-center mt-3 `}>
              <h2 className={`${classes.main_white_head}  `}>

                {pageData.Advantage_title}
              </h2>
            </div>

            <div className="row d-flex justify-content-evenly">
              {landingPageResponse.map((ach, i) => {
                return (

                  <div key={i} className="col col-xs-3 col-sm-3 col-6">
                    <div className={`d-flex justify-content-center mt-3  `}>
                      <h3 className={` ${classes.rank} p-0 text-white`}>
                        {ach.rank_title}
                      </h3>
                    </div>
                    <div className={`text-center ${classes.rank_description}`}>
                      {/* px-lg-5 */}
                      <p className=" py-2 text-white ">
                        {ach.rank_description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* <div className="py-4"></div> */}
        </div>


        <h2 className={`text-center ${classes.main_blue_head} py-4 m-0`}>
          {/* Programme Highlights */}
          {pageData.Highlights_title}
        </h2>
        <div >
          <div className="row justify-content-center mx-2 px-lg-5 " style={{ gap: "30px" }} >
            {selectedItems.map((card, index) => (
              <div
                key={index}
                className={`p-0 col-lg-3 col-md-4 col-12 card border-0`}
                style={{ boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)", width: "180px" }}
              >
                <div className="card-body text-center">
                  <Image
                    src={props.apiUrl + card.image.data.attributes.url}
                    alt="Cardvg"
                    width="42"
                    height="41"
                    sizes="100vw"
                  />
                  <h2 className={`card-title text-black ${classes.highlightcards_title} pt-2 mt-2`}>
                    {card.title}

                  </h2>
                </div>
              </div>
            ))}
          </div>

        </div>
        {selectedItems.length > 0 ?
          <div className="py-4"></div> : <></>
        }
      </div>

      <div className={classes.newsFold}>
        <h2 className={`text-center ${classes.blueHeading} pt-4 m-0`}>
          {pageData.news_fold_title}
        </h2>
        <Slider
          {...settings}
          ref={sliderRef}
          className={`p-0 ${classes.equalPadding} mx-auto`}
        >
          <div className={`bg-white ${classes.equalPadding} p-3 shadow-sm`}>
            <div className="row">
              <Image
                width="0"
                height="0"
                sizes="100vw"
                className="img-fluid col-lg-4 col-md-12"
                src={props.apiUrl + newsFoldImage.data.attributes.url}
                alt={newsFoldImage.data.attributes.alternativeText} />
              <div className="col-lg-8 col-12 d-flex flex-column justify-content-center">
                <p className="pt-lg-0 pt-3">
                  {pageData.news_fold_content}
                </p>
                <a
                  target="_blank"
                  rel="noopener noreferrer"
                  href={`${pageData.news_fold_url}`}
                  className={`${classes.newwaylearnbtn} btn btn-primary`}>
                  {pageData.news_fold_btn_title}
                </a>
              </div>
            </div>
          </div>
        </Slider>
        <div className="py-4">

        </div>
      </div>

      <div className="bg-white">
        <div className={`${classes.equalPadding} mx-auto`}>
          <h2 className={`text-center ${classes.blueHeading} pt-4 m-0`}>
            {pageData.insights_fold_title}
          </h2>
          <div className={`row pt-2 pb-3 px-3 ${classes.insightStyle}`}>
            {
              insights.map((item) => {
                return (
                  <a key={item.id} href={item.insight_url + qpms} className="col-md-4 col-lg-4">
                    <Image
                      width="0"
                      height="0"
                      sizes="100vw"
                      alt={item.insight_image.data.attributes.alternativeText} src={props.apiUrl + item.insight_image.data.attributes.url} className={`img-fluid ${classes.insightCards}`} />
                    <p className={`pt-3 px-3 text-center ${classes.insightCardsText}`}>{item.insight_title}</p>
                  </a>)
              })
            }
           
          </div>

        </div>
      </div>
      <BottomFold data={props.bottomFoldData}></BottomFold>
    </>

  );
}
 

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;
  const [homeData, bottomFoldData, featuredcourses, navdData] = await Promise.all([
    fetch(
      `${APIUrl}/api/main-page?populate[learning_tracks][populate]=*&populate[home_fold_one_media][populate]=*&populate[nwl_content][populate]=*&populate[insights][populate]=*&populate[meta_tags][populate]=*&populate[news_fold_image][populate]=*&populate[home_highlights][populate]=*&populate[home_achievements][populate]=*&populate[featured_courses][populate]=*&populate[course_media][populate]`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
    fetch(`${APIUrl}/api/featuredcourses?populate=*`).then((r) => r.json()),
    fetch(`${APIUrl}/api/navbars?populate[programs_tab_title][populate]=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      homePageContent: homeData,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData,
      featuredcourses:featuredcourses,
      navdData:navdData
    },
    revalidate: 240
  };
}
