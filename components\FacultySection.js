import React from 'react'
import styles from '../pages/perspectives/article/article.module.css'
import Image from 'next/image'

const FacultySection = ({ professor_section, apiUrl, shareArticle }) => {
  return (
    <div>
      <div className={styles.picksList} style={{paddingTop:shareArticle? "0px" : ""}}>
        <div className={styles.pickCard}>
          <Image
            src={`${apiUrl}${professor_section.prof_image.data.attributes.url}`}
            alt="professor"
            width={95}
            height={90}
            className={styles.pickImage}
          />
          <div className={styles.pickContent}>
            <h5>{professor_section.prof_first_name} {professor_section.prof_last_name}</h5>
            <p>{professor_section.prof_description}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FacultySection