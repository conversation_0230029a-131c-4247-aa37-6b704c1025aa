import Image from "next/image";
import classes from "./referal-page.module.css";
import React, { useState } from "react";
import referralImage from "../assets/referal_landing.svg";
import Router from "next/router";
import BottomFold from "../components/bottom_fold_one";


export default function ReferralLandingPage(props) {
  const landingPage = props.landingPage.data.attributes;

  const [inputFields, setInputFields] = useState([
    { name: "", phone_number: "" },
    { name: "", phone_number: "" },
  ]);

  const addFields = () => {
    let newfield = { name: "", phone_number: "" };

    setInputFields([...inputFields, newfield]);
  };
  const handleFormChange = (index, event) => {
    let data = [...inputFields];
    data[index][event.target.name] = event.target.value;
    setInputFields(data);
  };

  const submitContact = async (event) => {
    event.preventDefault();
    const json = {
      data: {
        name: `${event.target.user_name.value}`,
        phone_number: `${event.target.number.value}`,
        friends: inputFields,
      },
    };

  
    const response = await fetch(
      `${props.apiUrl}api/referals`,
      {
        body: JSON.stringify(json),
        headers: {
          "Content-Type": "application/json",
        },
        method: "POST",
      }
    );
    if (response.status == 200) {
      Router.push("/thankyou-ref");
    }
  };

  const removeFields = (index) => {
    let data = [...inputFields];
    data.splice(index, 1);
    setInputFields(data);
  };

  return (
    <>
      <div className={`${classes.fold1bg}`}>
        <div
          className={`row flex-row-reverse py-lg-5 p-0 ${classes.equalPadding} mx-auto `}
        >
          <Image
            cl
            src={referralImage}
            className="col-lg-4 img-fluid col-md-12 p-0 m-0"
            alt="foldImage"
          />
          <div
            className={`${classes.secstart} ${classes.mobileFoldbg}  col-lg-8 col-md-12 py-lg-0 py-md-4 py-sm-4 py-3 px-0 d-flex flex-column justify-content-center`}
          >
            <div className="px-lg-0 px-md-3 px-sm-3 px-3">
              <h2
                className={`text-white pt-lg-0 pt-lg-4 pt-0 ${classes.title}`}
              >
                <b>{landingPage.title}</b>
              </h2>
              <h5 className="text-white ">{landingPage.sub_title}</h5>
              <p
                className={`my-lg-4 my-0 text-white pe-lg-3 ${classes.paragraph}`}
              >
                {landingPage.description}
              </p>
            </div>
            <div></div>
          </div>
        </div>
      </div>

      <div className="bg-white px-lg-0 px-3 m-0">
        <div className={`${classes.equalPadding} mx-auto py-lg-3 py-2`}>
          <h5 className="m-0 pb-2">{landingPage.form_title}</h5>
          <div>
            <form onSubmit={submitContact} className="my-0">
              <div className={`${classes.blueBg} row pt-2 pb-3 m-0`}>
                <p style={{ fontSize: "15px" }} className="m-0 pb-2 text-dark">
                  Enter your details
                </p>
                <div className="form-group col-lg-4">
                  <label
                    className={`control-label ${classes.formLabelText}`}
                    for="user_name"
                  >
                    Name
                  </label>
                  <input
                    className="form-control"
                    type="text"
                    id="user_name"
                    name="user_name"
                    placeholder="Name"
                    required
                  />
                </div>
                <div className="form-group col-lg-4">
                  <label
                    className={`control-label ${classes.formLabelText}`}
                    for="number"
                  >
                    Phone Number
                  </label>
                  <input
                    className="form-control"
                    type="text"
                    id="number"
                    name="number"
                    placeholder="Phone Number"
                    required
                    pattern="[6789][0-9]{9}"
                    title="Please enter valid phone number"
                    onKeyPress={(event) => {
                      if (!/[0-9]/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                  />
                </div>
              </div>
              <div className={`${classes.blueBg} row pt-2 pb-3 m-0 my-3`}>
                <p style={{ fontSize: "15px" }} className="m-0 py-2 text-dark">
                  Enter Friend’s details 
                </p>
                {inputFields.map((input, index) => {
                  return (
                    <div className="row m-0 p-0" key={index}>
                      <div className="row py-2">
                        <div className="col-lg-4 col-11">
                          <label
                            className={`control-label ${classes.formLabelText}`}
                            for="name"
                          >
                            Name
                          </label>
                          <input
                            className="form-control"
                            name="name"
                            placeholder="Name"
                            required
                            value={input.name}
                            onChange={(event) => handleFormChange(index, event)}
                          />
                        </div>
                        <div className="col-lg-4 col-11">
                          <label
                            className={`control-label ${classes.formLabelText} mt-lg-0 mt-2`}
                            for="phone_number"
                          >
                            {" "}
                            Phone Number{" "}
                          </label>
                          <input
                            name="phone_number"
                            className="form-control"
                            placeholder="Phone Number"
                            value={input.phone_number}
                            required
                            pattern="[6789][0-9]{9}"
                            title="Please enter valid phone number"
                            onKeyPress={(event) => {
                              if (!/[0-9]/.test(event.key)) {
                                event.preventDefault();
                              }
                            }}
                            onChange={(event) => handleFormChange(index, event)}
                          />
                        </div>
                        {
                          index!=0?<button onClick={() => removeFields(index)} className="btn text-center col-1 p-0 pt-4 m-0">
                          <p className={`text-start ${classes.addMore} m-0`}>Clear</p>
                        </button>:<></>
                        }
                      </div>
                    </div>
                  );
                })}
                <button
                  className={`btn text-start py-0 ${classes.addMore}`}
                  onClick={addFields}
                >{`Add More >`}</button>
              </div>
              <div className="text-start">
                <button
                  className={`${classes.viewbtn} mt-3 mb-3 px-5`}
                  type="submit"
                >
                  {landingPage.btn_title}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
      <BottomFold data = {props.bottomFoldData}></BottomFold>
    </>
  );
}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;

  const [landingPageResponse,bottomFoldData] = await Promise.all([
    fetch(`${APIUrl}/api/referal-landing-page?populate=*`).then((r) =>
      r.json()
    ),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      landingPage: landingPageResponse,
      apiUrl: APIUrl,
      bottomFoldData:bottomFoldData
    },
    revalidate:60
  };
}
