import Link from "next/link";
import classes from "./sitemap.module.css";

export default function SiteMap(props){
    return (
    <div className={`${classes.equalPadding} ${classes.liStyle} mx-auto py-4`}>
        <li><Link rel="canonical" href={`/`}>Home</Link></li>
        <li><Link rel="canonical" href={`/about-page`}>About</Link></li>
        <li><Link rel="canonical" target="_blank" href={`https://execed.isb.edu/en/solutions-for-institutions.html`}>Academic Solutions</Link></li>
        {/* <li><Link rel="canonical" href={`/learning-tracks`}>Learning Tracks</Link></li> */}
        <li><Link rel="canonical" href={`/certificates-and-badges`}>Certificates and Badges</Link></li>
        <li><Link rel="canonical" href={`/lxp-page`}>LXP Page</Link></li>
        <li><Link rel="canonical" href={`/faq-page`}>FAQ Page</Link></li>
        {props.courseData.data.map((item, i) => {
          return (
            <div
              key={`${item.attributes.courseid}`}>
              <div>
                <li>Individual Courses</li>
                <ul className="px-5">

                  <li  >
                    <Link rel="canonical" href={`/course/${item.attributes.courseid}`}>{item.attributes.course_title}</Link>
                  </li>

                </ul>
              </div>
            </div>
          );
        })}
        {props.tracksData.data.map((item,i)=>{
            return (
                <div
                  key={`${item.attributes.learning_track_id}`}>
                    <div>
                      <li><Link rel="canonical" href={`/${item.attributes.learning_track_id}`}>{item.attributes.learning_track_name}</Link> </li>
                      {item.attributes.courses.data.length>0 && <ul className="px-5">
                        {item.attributes.courses.data.map((course,j)=>{
                            return (
                                <li key={j}>
                        <Link rel="canonical" href={`/${item.attributes.learning_track_id}/${course.attributes.courseid}`}>{course.attributes.course_title}</Link>
                        </li>)
                        })}
                      </ul>}
                    </div>
                </div>
              );
        })}
      
    </div>
    )
}

export async function getStaticProps(context) {
    const { req, query, res, asPath, pathname, params } = context;
    const APIUrl = process.env.API_BASE_URL;
  
    const [response,courseData] = await Promise.all([
      fetch(
        `${APIUrl}/api/learning-tracks?populate=*`
      ).then((r) => r.json()),
      fetch(
        `${APIUrl}/api/featuredcourses?populate[course_title][populate]=*& populate[courseid][populate]=*`
      ).then((r) => r.json()),
    ]);
    return {
      props: {
        tracksData: response,
        courseData:courseData,
        apiUrl: APIUrl,
      },
    };
  }