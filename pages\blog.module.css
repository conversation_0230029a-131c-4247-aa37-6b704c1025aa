
  
  .heading{
    font-size: 17px;
    vertical-align: center;
    line-height: normal;
    font-weight: 600;
    color: white;
  }

  .description{
    font-size: 2rem;
    vertical-align: center;
    line-height: normal;
    font-weight: 700;
    color: white;
  }
  
  .fold1bg{
     background-image: url('../assets/TealBackground.webp');
    background-size: cover;
    background-color:#057092;
  }
  
  
  
  
 
    






.showBtn
  {
      /* background-color: white !important; */
      border:none;
      padding: 8px 36px 8px 36px;
      font-weight: 500;
      flex-wrap: wrap;
      margin-right: 6px;
      margin-left: 6px;
      border-radius: 0px!important;
      /* width: 347px;
      height: 46px; */
      color: #FFFFFF;
      background: #057092;
  }

.body{
    background-color:white;
}


.equalPadding{
  max-width: 1000px;
}

.hidden{
  content-visibility: hidden;
}
.auto{
  content-visibility: auto;
}