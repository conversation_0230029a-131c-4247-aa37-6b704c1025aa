
.linearGradient{
    background: linear-gradient(90deg, #735FC8 0%, #0096E6 48.05%, #2DAF8C 100.27%);
}

.equalPadding{
    max-width: 1000px;
  }

  .blackHeading{
    font-weight: 700;
    font-size: 26px;
    line-height: 35.47px;
    color: #000;
  }
  .loader {
    /* margin-top: 10px; */
    color: #66266B; /* Adjust the color as needed */
    font-size: 16px;
    font-weight: bold;
    position: absolute;
}

  .whiteHeading{
    font-weight: 700;
    font-size: 26px;
    line-height: 35.47px;
    color: #fff;
  }

  .fold1bg{
    background-image: url('../assets/tq_page.svg');
    background-size: cover;
    box-shadow: inset 0 0 0 1000px rgba(0,0,0,.7);
    min-height: 300px;
  }

  .showBtn
  {
      background-color: white !important;
      border:none;
      padding: 10px 36px 10px 36px;
      font-weight: 600;
      flex-wrap: wrap;
      margin-right: 8px;
      margin-left: 8px;
      border-radius: 0px!important;
      color: #057092;
  }

.imagePadding{
    padding-top: 225px;
    text-align: center;
}

.imagePad{
  padding-top: 140px;
  padding-bottom: 120px;
}
.boxshadow{
    border: none;
    border-radius: 0%;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12); 
  }
  
  .buttonPosition{
      bottom: 0px;
      position: absolute;
  }
  
  .viewbtn {
  background-color: #057092 !important;
  padding: 6px 20px;
  border:none;
  border-radius: 0px!important;
  color: #fff;
  }
        
  .viewbtn:hover{
   background-color: #057092 !important;
   padding: 6px 20px;
   font-weight: 600;
   border:none;
   border-radius: 0px!important;
  }    

  .image_border{
    border-radius: 0%;
     min-height: 138px;
    }
        
    .learnTrackTitle{
       color: black;
       font-size: 16px;
       font-weight: 700;
    }

    @media screen and (min-width:0px) and (max-width: 499px) {
  
        .fold1bg {
            background-image: url('../assets/tqImage.jpg');
            background-size: cover;
            box-shadow: inset 0 0 0 1000px rgba(0,0,0,.7);
            background-repeat: no-repeat;
        
          }

          .imagePadding{
            padding-top: 45px;
        }
    
    }
    
    @media screen and (min-width:500px) and (max-width: 976px){
        .fold1bg {
            background-image: url('../assets/tqImage.jpg');
            background-size: cover;
            box-shadow: inset 0 0 0 1000px rgba(0,0,0,.7);
            background-repeat: no-repeat;
        
          }
          .imagePadding{
            padding-top: 125px;
        }
    }    