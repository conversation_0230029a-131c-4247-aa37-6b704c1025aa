{"name": "isb_next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.2.1", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@fortawesome/react-fontawesome": "^0.2.0", "@next/bundle-analyzer": "^13.2.4", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/full-screen": "^3.12.0", "axios": "^1.6.8", "bootstrap": "^5.2.2", "bootstrap-horizon": "^1.0.1", "bootstrap-icons": "^1.9.1", "caniuse-lite": "^1.0.30001641", "crypto-js": "^4.2.0", "eslint": "8.26.0", "eslint-config-next": "13.0.1", "js-cookie": "^3.0.1", "moment": "^2.29.4", "next": "13.0.1", "next-compose-plugins": "^2.2.1", "next-images": "^1.8.4", "next-seo": "^5.15.0", "pdfjs-dist": "^3.4.120", "react": "18.2.0", "react-bootstrap": "^2.7.2", "react-collapsed": "^3.5.0", "react-dom": "18.2.0", "react-h5-audio-player": "^3.10.0-rc.1", "react-lazyload": "^3.2.0", "react-phone-input-2": "^2.15.1", "react-player": "^2.14.0", "react-responsive-carousel": "^3.2.23", "react-slick": "^0.29.0", "slick-carousel": "^1.8.1", "swr": "^2.0.0"}, "devDependencies": {"sass": "^1.57.1"}}