import Image from "next/image";
import classes from "./learning-tracks.module.css";
import Link from "next/link";
import BottomFold from "../components/bottom_fold_one";
import { useRouter } from "next/router";
import { useEffect } from "react";




function AllLearnTracks(props) {
  const router = useRouter();
  const { query } = router;
  const learningTracks = props.tracksData.data;
  const qpms = query.utm_source != undefined ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}` : ``


  useEffect(() => {
    if (router.pathname === "/learning-tracks") {
      const qpms = new URLSearchParams(router.query).toString();
      router.replace(qpms ? `/?${qpms}` : "/");
    }
  }, [router]);

  return null;

  return (
    <>

      <div className={`${classes.fold1bg}`}>
        <div className={`${classes.hideCrumb} mx-auto pt-lg-5 px-3 px-lg-0`}>
          <div className={`d-flex ${classes.breadCrumb} px-1`}>
            <Link style={{ textDecoration: 'none' }} href={`/${qpms}`}><p className="text-white m-0">ISB Online</p></Link>
            <svg
              style={{ fill: "#ffffff" }}
              width="24"
              height="18"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_3275_9282)">
                <path
                  d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z"
                />
              </g>
              <defs>
                <clipPath id="clip0_3275_9282">
                  <rect width="24" height="24" fill="#057092" />
                </clipPath>
              </defs>
            </svg>
            <p className={`text-white m-0 ${classes.breadCrumblast}`}>Learning Tracks</p>
          </div>
        </div>
        <div className={`pb-lg-5 py-md-4 px-lg-1 px-md-3 px-sm-3 px-4  py-4 ${classes.equalPadding} mx-auto`} >
          <div className="text-white"><h1><b>{props.learnTrackContent.data.attributes.title}</b></h1></div>
          <p className={`py-0 ${classes.p_text_lg}`}>
            {props.learnTrackContent.data.attributes.description}
          </p>
        </div>
        {/* <div className={classes.extraSpace}></div> */}
      </div>
      <div className={`${classes.equalPadding} mx-auto px-lg-0 px-md-4 px-sm-4 px-4 pb-4`}>
        <div>
          <h2 className={`${classes.mainHeading} pt-lg-5 pt-md-3 pt-sm-3 pt-3`}>
            {props.learnTrackContent.data.attributes.learn_track_title}
          </h2>
          <div className={`row m-0`}>
            {learningTracks.map((item, i) => {
              return (
                <div
                  key={`${item.attributes.learning_track_id}`}
                  className={`col-lg-3 d-flex align-items-stretch col-md-6 col-sm-12 px-2 pt-3`}>
                  <Link rel="canonical" style={{ textDecoration: "none" }} href={`/${item.attributes.learning_track_id}${qpms}`}>
                    <div className={`card ${classes.boxshadow}`}>
                      <Image
                        width="0"
                        height="0"
                        sizes="100vw"
                        src={
                          props.apiUrl +
                          item.attributes.learning_track_short_image.data
                            .attributes.url
                        }
                        className={`card-img-top ${classes.image_border} w-100 h-auto`}
                        alt={item.attributes.learning_track_short_image.data
                          .attributes.alternativeText}
                      />
                      <div>
                        <h3
                          className={`card-title text-center py-3 ${classes.learnTrackTitle}`}
                        >
                          {item.attributes.learning_track_name}
                        </h3>
                      </div>
                    </div>
                  </Link>
                </div>
              );
            })}
          </div>
          <hr className={`mt-5 mb-0`}></hr>
          <div>
            {learningTracks.map((Track, i) => {
              return (
                <>
                  <h2 id={i} key={Track.attributes.learning_track_id} className={`${classes.mainHeading} pt-4`}>{Track.attributes.learning_track_name}</h2>
                  {/* <h3   className={` ${classes.learnTrackTitle_comprises_title}`} > This Learning Track comprises the following courses: </h3> */}
                  <h3 className={` ${classes.learnTrackTitle_comprises_title}`} >  {props.learnTrackContent.data.attributes.comprises_courses_tag} </h3>
                  <div className="row m-0">
                    {Track.attributes.courses.data.map((course, i) => {
                      return (
                        <div
                          key={course.id}
                          className={`col-lg-3 d-flex align-items-stretch col-md-6 col-sm-12 px-2 pt-3`}
                        >
                          <div className={`card ${classes.boxshadow}`}>
                            <Image
                              width="0"
                              height="0"
                              sizes="100vw"
                              src={`${props.apiUrl}${course.attributes.course_short_image.data.attributes.url}`}
                              className={`w-100 h-auto card-img-top ${classes.image_border}`}
                              alt={course.attributes.course_short_image.data.attributes.alternativeText}
                            />
                            <div
                              className={`card-body d-flex flex-column ${classes.buttonPos} mb-3`}
                            >
                              <h3
                                className={`card-title ${classes.learnTrackTitle}`}
                              >
                                {course.attributes.course_title}
                              </h3>
                              <p
                                className={`pb-3 card-text ${classes.paragraphSize}`}
                              >
                                {course.attributes.course_short_description}
                              </p>
                              <div className={`mb-0 ${classes.buttonPosition}`}>
                                <Link
                                  rel="canonical"
                                  href={`${Track.attributes.learning_track_id.toString()}/${course.attributes.courseid}${qpms}`}
                                  className={`btn btn-primary mt-auto align-self-start ${classes.viewbtn}`}
                                >
                                  Learn More
                                  <i className="fa fa-long-arrow-right"></i>
                                </Link>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <hr className={`${i === 2 ? "d-none" : ""
                    } mt-5 mb-0`}></hr>
                </>
              );
            })}
          </div>
        </div>
      </div>
      <BottomFold data={props.bottomFoldData}></BottomFold>
    </>
  );
}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;
  const [response, learnTrackContent, bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/learning-tracks?populate[learning_track_short_image][populate]=*&populate[courses][populate][0]=course_short_image`
    ).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/learning-tracks-page`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      tracksData: response,
      apiUrl: APIUrl,
      learnTrackContent: learnTrackContent,
      bottomFoldData: bottomFoldData
    },
    revalidate: 240
  };
}

export default AllLearnTracks;
