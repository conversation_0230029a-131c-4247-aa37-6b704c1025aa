.liStyle p {
    color: white !important;
    font-weight: 300;
  }
  .liStyle li {
    color: white !important;
    font-weight: 300;
  }
  
  
  .para{
      color: black !important;
      text-decoration: none;
      background-color: transparent;
      align-items: baseline;
      font-weight: 600;
      vertical-align: center;
      margin: 0;
  }
  
  .dropdown{
    color: black !important;
    text-decoration: none;
    background-color: transparent;
    align-items: baseline;
    font-weight: 600;
    vertical-align: center;
    border-color: transparent;
  }
  
  .dropdown::after{
    display: none !important; 
  }
  
  .dropdown:hover{
    color: black !important;
    text-decoration: none;
    background-color: transparent;
    align-items: baseline;
    font-weight: 600;
    vertical-align: center;
    border-color: transparent;
  }
  
  .dropdown::on-click{
    color: black !important;
    text-decoration: none;
    background-color: transparent;
    align-items: baseline;
    font-weight: 600;
    vertical-align: center;
    border-color: transparent;
    margin: 0;
  }
  
  .faqQuestion{
    font-size: 17px;
    font-weight: 500;
    border: none;
    background-color: transparent;
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }
  .faqAnswer{
    text-align: left;
    color: white !important;
  }
  
  .horizontalLine{
    border-top:3px solid white;
    }
  
  .bgImage{
    position: relative;
    width: 100%;
    height: 400px; 
  }
  
  .bgContent{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1;
    color: #fff;
    padding: 16px;
    width: 100%;
  }
  
  .contentFold {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: left;
    z-index: 1;
    color: #fff;
    padding: 16px;
    width: 100%; 
  }
  
  .fold1bg{
    background-image: url('../assets/TealBackground.webp');
    background-size: cover;
    background-color: #057092
  }
  
  .newsFold{
    background-image: url('../assets/new_background.webp');
    background-size: cover
  }
  
  .insightStyle a{
    text-decoration: none;
  }
  
  .insightCards{
    min-height: 180px;
    max-height: 180px;
    width: 100%;
  }
  
  .insightCardsText a{
    font-size: 16px;
    font-weight: 700;
    color:#7C8495;
  }
  
  .fontwhite{
  color:white;
  list-style: 21px !important
  }
  
  .buttonPos{
    position: relative;
  }
  
  .buttonPosition{
    bottom: 0px;
    position: absolute;
  }
  
  .blackHeading{
    font-weight: 700;
    font-size: 26px;
    line-height: 54.47px;
    color: #000;
  }
  
  .blueHeading{
    font-weight: 700;
    font-size: 24px;
    line-height: 54.47px;
    color: #057092;
  }
  
  .nwlTitle{
    font-weight: 600;
    font-size: 20px;
    line-height: 34.47px;
    color: #057092;
  }
  
  .card_body_cal{
    min-height:120px
  }
  
  .learnTrackTitle{
    color: black;
    font-size: 1rem;
    font-weight: 700;
  }
  
  .homeImage{
    width: 100vw;
  }
  
  .bluebackground{
    background-color: #D7E0E8;
  }
  .btnprimary{
    background-color: #057092;
    border-radius: 0px;
  }
  
  .moduleTitle{
    color: #057092 !important;
    text-decoration: none;
    align-items: baseline;
    vertical-align: center;
    font-size: 20px;
    margin: 0;
  }
  
  .lessonContent{
    color: #7C8495 !important;
    text-decoration: none;
    align-items: baseline;
    padding: 2px;
    margin: 0px;
    vertical-align: center;
  }
  
  .header {
      width: 100%;
      height: 5rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #FFFFFF;
      padding: 0 10%;
      position: sticky;
    }
      
    .navelems{
      right: 0;
      padding-right: 52px;
      padding-left: 52px;
    }
  
    .content{
      padding: 25PX;
      background-color: #F9F9F9;
      margin-top: 10px;
    }
    
    .fold1{
      padding: 0;
      background-color: rgb(255, 255, 255);
    }
    .custm_padding{
      padding: 0px;
    }
   .bottomFold{
      padding: 0;
      background-color: white;
      margin-top: 48px;
      margin-left: 48px;
      margin-right: 28px;
    }
  
    .profFold{
      padding: 0;
      background-color: white;
      margin-right: 48px;
    }
  
  
    .sec-start
  {
      padding: 20px 0px;
  }
  
  .custm_btn{
    background-color: #057092;
  }
  
  .custm_font{
    color: #000;
  }
  
  .fold1btn
  {
    min-height: 38px;
      background-color: #fff!important;
      border: 2px solid #fff!important;
      color: #057092!important;
      font-weight: 600;
      border-radius: 0px!important;
    fill:  #057092!important;
  }
  
  .fold1btn:hover{
    background-color: transparent!important;
      border: 3px solid #fff!important;
      color: #fff!important;
      font-weight: 600;
      border-radius: 0px!important;
    fill: white !important;
  }
  
  
  .viewbtn
  {
      background-color: #057092 !important;
      padding: 6px 20px;
      border:none;
      border-radius: 0px!important;
  }
  
  .viewbtn:hover
  {
        background-color: #057092 !important;
      padding: 6px 20px;
      font-weight: 600;
      border:none;
      border-radius: 0px!important;
  }
  
  .sec-start p
  {
   font-size: 26px;
   padding-top: 12px;
  }
  .learning-tracks
  {
      padding: 20px 0px;
  }
  .learning-track-title
  {
      font-size: 18px;
      text-align: center;
  }
  
  .image_border{
  border-radius: 0%;
  min-height: 138px;
  }
  
  .boxshadow{
    border: none;
    border-radius: 0%;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12); 
  }
  
  .learningtracktext
  {
        font-size: 14px;
      text-align: center;
      color: #7C8495;
  }
  .learning-track-btn,.learning-track-btn:hover
  {
      background-color: #057092!important;
      padding: 6px 20px;
      width: 152px;
      height: 38px;
      border-radius: 0px!important;
  }
  .learning-tracks .card
  {
    height: 355px;
  }
  .partnershipcoursetitle
  {
      font-size: 18px;
      text-align: left;
      font-weight: 700;
  }
  .partnershipCourseText
  {
      font-size: 14px;
      text-align: left;
      color: #7C8495;
     
  }
  .hrstext
  {
      margin-bottom: 0px;
      font-size: 14px;
  }
  .taxestext
  {
    color: #000;
      font-weight: 650;
      font-size: 16px;
      margin-bottom:0px
  }
  .newwaylearn
  {
      background-color: #D7E0E8;
      padding: 40px 0px;
  }
  .new-way-learn-title
  {
      color: #057092;
      font-size: 20px;
      padding-bottom: 20px;
  }
  .waylearning
  {
      margin-left: 80px;
    margin-right: 80px;
    margin-bottom: 100px;
    margin-top: 20px;
  }
  .new-way-learn h5
  {
      color: #057092;
  }
  .new-way-learn p
  {
      color: #7C8495;
  }
  
  .container { 
    position: relative;
  }
  @media screen and (max-width:768px){
    .sponsorFont{
      color: var(--isb-blue-color);
      font-weight: 700 !important;
      font-size: 24px !important;
      line-height: 24px ;
      
    }
    .sponsorBorder{
border-bottom: 1px solid var(--isb-blue-color);

    }
  }
  @media screen and (min-width: 480px) {
    .verticalcenter {
      margin: 0;
      position: absolute;
      top: 50%;
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
    }  
  }
  
  
  
  
  
  @media screen and (max-width: 480px) {
    .verticalcenter {
      margin: 10px;
      position: static;
      top: 50%;
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
    } 
    
    
  .newwaylearn{
    padding:0px
  }
  
  }
  
  .new-way-learn-col-1
  {
      background-color: #fff;
  }
  .new-way-learn-col-2
  {
      background-color: #fff;
      padding: 72px 26px;
  }
  .newwaylearnbtn,.newwaylearnbtn-:hover
  {
      background-color: #fff!important;
      border: 1px solid #057092!important;
      color: #057092!important;
      text-transform: uppercase;
      font-weight: 600;
      border-radius: 0px!important;
    max-width: 180px;
  }
  .footer
  {
      background-color: #057092;
  }
  .left-footer p
  {
      color: #fff;
      font-weight: 100;
      font-size: 17px;
  }
  .allfaqs
  {
      font-size: 16px;
      color: #fff;
    font-weight: 400;
    
  }
  
  .allfaqHeading{
    font-size: 16px;
      color: #fff !important;
    font-weight: 600;
    text-decoration: none;
  }

  .contactUsCard{
    font-size: 16px;
    color: #fff !important;
  text-decoration: none;
  text-align: right;
  }
  
  .announcementsTitle{
    font-size: 14px;
      color: #fff;
    font-weight: 400;
  }
  
  .foottitle{
  border-bottom:1px solid white;
  color:white;
  padding-bottom:8px;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  }
  
  
  .sponsorBorder{
    border-right:1px solid gray;
    padding-right: 22px !important;
    }
  
  .sponsorFont{
    font-size: 17px;
    font-weight: 600;
  }
  
  .sponsorTextAlign{
    text-align: left;
  }
  
  .footerFinal{
      border-bottom:1px solid black;
  }
  
  .collapsible{
    flex-direction:row;
    justify-content: space-between;
    display:flex;
  }
  .left-footer hr
  {
    color: #fff!important;
    background-color: #fff!important;
  }
  .footer-plus
  {
   margin-top: 15px;
   color: #fff;
  }
  .footer-first-plus
  {
      margin-top:10px;
      color: #fff;
  }
  .footer ul {
      list-style-type: none;
      color: #fff;
      font-size: 14px;
      font-weight: 300;
      padding-left: 0px;
      margin-top: 20px;
  }
  .footer ul li
  {
    padding-bottom: 5px;
  }
  
  .social-icons .fa
  {
      background-color: #fff;
      color: #057092;
      width: 23px;
      height: 23px;
      padding-left: 5px;
      padding-top: 4px;
      border-radius: 50%;
      margin-top: 3px;
  }
  .footerrow
  {
      padding-top: 25px;
    padding-bottom: 25px;
    padding-left: 12px;
    padding-right: 12px;
    background: #057092;
  }
  
  .footerrowWhite
  {
      padding-top: 25px;
    padding-bottom: 25px;
    padding-left: 12px;
    padding-right: 12px;
    background: #FFFFFF;
  }
  
  #testimonial {
      padding: 30px 20px 20px;
      color: #fff;
      background-color: #d7e0e8;
  }
  #testimonial h2 {
      font-style: italic;
      color: #fff;
      font-size: 26px;
      text-align: center;
  }
  #testimonial .client-img {
      width: 200px;
      height: 200px;
      margin: 0px auto;
      border-radius: 100%;
      position: absolute;
      left: 0px;
  }
  #testimonial .carousel-content {
      padding: 20px 0px 20px 100px;
      width: 78%;
      margin: 0 auto;
      position: relative;
      background-color: #fff;
  }
  #testimonial h3 {
      font-size: 17px;
      color: #fff;
      margin-bottom: 30px;
      font-style: italic;
      text-align: right;
  }
  .carousal-btn,.carousal-btn:hover
  {
      background-color: #fff!important;
      border: 1px solid #057092!important;
      color: #057092!important;
      font-weight: 600;
      border-radius: 0px!important;
      margin-left: 16%;
  }
  #testimonial .testimonial-text {
      font-size: 15px;
      color: #7C8495;
      margin-left: 16%;
      font-weight: 500;
      width: 61%;
  }
  #testimonial .testimonial-date {
      font-size: 12px;
      color: #7C8495;
      margin-left: 16%;
  }
  #testimonial .client-img img {
      width: 100%;
      padding-left: 15px;
  }
  #testimonial .carousel-control-prev,
  #testimonial .carousel-control-next {
      font-size: 36px;
  }
  .main-carousal
  {
      background-color: #ECECEC;
  }
  .main-carousal-item img
  {
    width: 85%;
    margin: 0% 4% 1% 5%;
  }
  .courses-btn,.courses-btn:hover
  {
      background-color: #057092!important;
      border: 1px solid #057092!important;
      color: #fff!important;
      font-weight: 600;
      border-radius: 0px!important;
      margin-top: 10px;
      margin-bottom: 30px;
  }
  .card_response{
  padding-top:40px
  }
  .padset{
    padding:2rem
  }
  
  .equalPadding{
    max-width: 1000px;
  }
  
  @media screen and (min-width:0px) and (max-width: 499px) {
    .card_response {
      padding:40px;
    } 
    .blackHeading{
      font-size:24px
    }
    .blueHeading{
     font-size:24px;
     line-height: 32px;
    }
   .padset{
    padding:30px;
  
   }
   .sponsorBorder{
    border-right:1px solid transparent;
    padding-right: 0px !important;
  
    }
    .navelems{
      right: 0;
      padding-right: 16px;
      padding-left: 16px;
    }
  
    .fold1bg{
        background-size: cover;
        background-color: #057092
      }
  
      .bgImage{
        position: relative;
        width: 100%;
        height: 560px; 
      }
      .sponsorTextAlign{
        text-align: center;
      }
      
      
  
  }
  
  @media screen and (min-width:500px) and (max-width: 976px) {
    .card_response {
      padding:40px;
    } 
    .blackHeading{
      font-size:25px
    }
    .blueHeading{
     font-size:24px;
    }
   .padset{
    padding:30px;
  
   }
   .sponsorBorder{
    border-right:1px solid transparent;
    padding-right: 22px !important;
    }
  
    .navelems{
      right: 0;
      padding-right: 16px;
      padding-left: 16px;
    }
  
    .fold1bg{
        background-size: cover;
        background-color: #057092
      }
  
      .bgImage{
        position: relative;
        width: 100%;
        height: 720px; 
      }
  
    
      .sponsorTextAlign{
        text-align: center;
      }  
  
  }
  
  
  @media screen and (min-width: 1700px){
    .card_response {
      padding:40px;
    } 
    .blackHeading{
      font-size:25px
    }
    .blueHeading{
     font-size:24px;
    }
   .padset{
    padding:30px;
   }
   .sponsorTextAlign{
    text-align: center;
  }
  
  }
  
  .bottomLink{
    font-size: 14px;
  }
  
  .dropbtn {
    background-color: transparent;
    color: black;
    font-size: 16px;
    border: none;
  }
  
  .dropdown {
    position: relative;
    display: inline-block;
    right: 5;
  }
  
  .dropdowncontent {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 250px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    right: 3;
  }
  
  .dropdowncontent a {
    color: #057092;
    padding: 9px 16px;
    text-decoration: none;
    display: block;
  }
  
  /* Change color of dropdown links on hover */
  .dropdowncontent a:hover {background-color: white;}
  
  /* Show the dropdown menu on hover */
  .dropdown:hover .dropdowncontent {display: block;}
  
  /* Change the background color of the dropdown button when the dropdown content is shown */
  .dropdown:hover .dropbtn {background-color: transparent;}
  
  
  
  
  .moreInfobtn {
    background-color: transparent;
    color: black;
    font-size: 16px;
    border: none;
  }
  
  /* The container <div> - needed to position the dropdown content */
  .moreInfo {
    position: relative;
    display: inline-block;
  }
  
  /* Dropdown Content (Hidden by Default) */
  .MoreInfoContent {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 250px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
  }
  
  /* Links inside the dropdown */
  .MoreInfoContent a {
    color: #057092;
    padding: 9px 16px;
    text-decoration: none;
    display: block;
  }
  
  /* Change color of dropdown links on hover */
  .MoreInfoContent a:hover {background-color: white;}
  
  /* Show the dropdown menu on hover */
  .moreInfo:hover .MoreInfoContent {display: block;}
  
  /* Change the background color of the dropdown button when the dropdown content is shown */
  .moreInfo:hover .moreInfobtn {background-color: transparent;}
  
  .back_to_top {
    position: fixed;
    bottom: 67px;
    right: 0;
  }
  .programList{
    margin-top: 1rem;
    margin-bottom: 0rem;
    /* display: flex; */
    /* flex-direction: column; */
    text-align: left;
  }
  .programList a{
text-decoration: none;
color: #057092;
  }
  .programListHeader{
    font-weight: 600;

  }
 .programList p{
  color: #5D6F7A;
 }
  .programList :last-child p{
    margin-bottom: 5px;
  }
  .socialIcons{
    display: flex;
    gap: 10px;
  }
 