import PhoneInput from 'react-phone-input-2';
import classes from '../pages/lpv2/[learning_track_id]/index.module.css'
import { YearsofExperience } from './ObjectData';
export const LeadForm =({trackpage,  myclass, buttonDisabled,checkbox<PERSON>and<PERSON>, formErrors, handleChange1, handleChange, formData, submitContact, landingPageResponse})=>{
  
    return(
      <div id="form" className={`${myclass? myclass : "col-lg-4 col-md-12 p-4"}  bg-white `} style={{borderRadius:"12px"}}>
              <div>
               {!trackpage&& <p className={`p-0 m-0 mb-2 ${classes.blueBoldText}`}>
                  {landingPageResponse.signup_form.signup_title}
                </p>}
             
                <form onSubmit={submitContact} className="my-0">
                  <div className="form-group col-12 py-1">
                    <label
                      className={`control-label ${classes.formLabelText} fw-bold`}
                      htmlFor="name"
                    >
                      {landingPageResponse.signup_form.Name_lable}
                      <span className={`${classes.formLabelTextcolor}`}>*</span>
                    </label>
  
                    <input
                      className={`form-control ${classes.forminput} border-0 `}
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      lang='en'
                      data-lpignore="true"
                      autoComplete='off'
                      // spellCheck='true'
                      onKeyPress={(event) => {
                        if (!/^[A-Za-z. ]$/.test(event.key)) {
                          event.preventDefault();
                        }
                      }}
                    />
                      {formErrors.name && <div  className={`${classes.error_tooltip} mb-5`}>{formErrors.name}</div>}
                  </div>
  
                  <div className="form-group col-12 py-2 ">
                    <label
                      className={`control-label ${classes.formLabelText} fw-bold`}
                      htmlFor="email"
                    >
                      {landingPageResponse.signup_form.Email_lable}
                      <span className={`${classes.formLabelTextcolor}`}>*</span>
                    </label>
                    <input
                      className={`form-control ${classes.forminput} border-0`}
                      type="text"
                      id="email"
                      name="email"
                      lang='en'
                      data-lpignore="true"
                      autoComplete='off'
                      // spellCheck='true'
                      value={formData.email}
                      onChange={handleChange}
                    />
                    {formErrors.email && <div  className={`${classes.error_tooltip}`}>{formErrors.email}</div>}


                    
                  </div>
                  <PhoneInputField landingPageResponse={landingPageResponse} formData={formData} handleChange1={handleChange1} formErrors={formErrors} />

                  <div className="form-group col-12 py-2">
                    <label
                      className={`control-label ${classes.formLabelText} fw-bold`}
                      htmlFor="role"
                    >
                      {landingPageResponse.signup_form.dropdown_lable}
  
                      <span className={`${classes.formLabelTextcolor}`}>*</span>
                    </label>
                    <select
                      className={`form-select ${classes.formselect} border-0`}
                      id="role"
                      name="role"
                      value={formData.role}
                      onChange={handleChange}
                    >
                       <option value="Select">Select</option>
                        <option value="MySelf">My Self</option>
                        <option value="My Team">My Team</option>
                        <option value="My Organisation [L&D Responsibility]">
                          My organisation (L & D Responsibility)
                        </option>
                    </select>
                    {formErrors.role && <div className={`${classes.error_tooltip}`}>{formErrors.role}</div>}
                    
                  </div>
  
             <div>
              <div className="form-group col-12 pt-1">
                <label
                  className={`control-label ${classes.formLabelText} fw-bold`}
                  htmlFor="years_of_experience"
                >
                  Years of Experience
                  <span className={`${classes.formLabelTextcolor}`}>*</span>
                </label>
  
                <select
                  className={`form-select ${classes.formselect} border-0`}
                  id="years_of_experience"
                  name="years_of_experience"
                  value={formData.years_of_experience}
                  onChange={handleChange}
                >
                  <option value=''>Select</option>
                  {YearsofExperience.map((item, index) => {
                    return <option key={index} value={item.value} >{item.text}</option>;
                  })}
                </select>
                {formErrors.years_of_experience && <div className={`${classes.error_tooltip} mb-5`}>{formErrors.years_of_experience}</div>}
              </div>
                    <div className="form-group col-12 pt-3 pb-1">
                      <label
                        className={`control-label ${classes.formLabelText} fw-bold`}
                        htmlFor="location"
                      >
                        Location
                        <span className={`${classes.formLabelTextcolor}`}>*</span>
                      </label>
                 
                      <input
                        className={`form-control ${classes.forminput} border-0 `}
                        type="text"
                        id="location"
                        lang='en'
                      data-lpignore="true"
                      autoComplete='off'
                      // spellCheck='true'
                        name="location"
                        value={ formData.location}
                        onChange={handleChange}
                      />
                    {formErrors.location && <div className={`${classes.error_tooltip} mb-5`}>{formErrors.location}</div>}
                    </div>
                 
               </div>  
  
                  <div className={`mt-1 pt-1 ${classes.disablePointer}`}>
                    <label style={{ fontSize: "13px" }} htmlFor="agree">
                      <input
                        defaultChecked={true}
                        className={classes.checkBox}
                        type="checkbox"
                        id="agree"
                        onClick={checkboxHandler}
                        onChange={checkboxHandler}
                      />
                      &ensp;
                      {landingPageResponse.signup_form.checkbox_text}
                    </label>
                  </div>
  
                  <div className="text-center">
                    <button
                      // disabled={!agree}
                      disabled={buttonDisabled}
                      className={`${ classes.land_btn} ${trackpage? "mt-lg-2 mt-xxl-3 mb-xxl-3":"mt-3"} mt-2 text-white `}
                      type="submit"
                    >
                      {landingPageResponse.signup_form.brochure_btn}
                    </button>
                  </div>
                </form>
              </div>
            </div>
    )
  }


export const PhoneInputField = ({ landingPageResponse, formData, handleChange1, formErrors }) => {
  return (
    <div className="form-group col-12 py-2" >
      <label
        className={`control-label ${classes.formLabelText} fw-bold`}
        htmlFor="number"
      >
        {landingPageResponse.signup_form.Mobile_lable}
        <span className={`${classes.formLabelTextcolor}`}>*</span>
      </label>
      <div className="bg_phone border-0">

        <PhoneInput
          country={"in"}
          inputClass={`form-control ${classes.forminput} border-0`}
          placeholder={""}
          defaultCountry={"in"}
          name="number"
          value={formData.number}
          onChange={handleChange1}
          inputProps={{
            id: "number",
            onKeyPress: (event) => {
              if (!/[0-9]/.test(event.key)) {
                event.preventDefault();
              }
            },

          }}
          countryCodeEditable={false}
        />

        {formErrors.number && <div className={`${classes.error_tooltip}`}>{formErrors.number}</div>}

      </div>
    </div>
  )
}