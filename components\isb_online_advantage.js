import classes from "./isb_online_advantage.module.css";
import Image from "next/image";


export default function IsbOnlineAdvantage(props) {


  return (
    <>
      <div className={`bg-white col-lg-12 p-4 mt-5 shadow-sm`}>
        <div className={`col ${classes.group_icon_ul}`}>
          <h2 className={`${classes.sideHeads} mb-0`}>
            {props.title}
          </h2>
          <p className={`py-3`}>
            {props.description}
          </p>
          <ul className={classes.group_icons_list}>
            {
              props.advantages.map((advantage) => {
                return (
                  <ol key={advantage.id} className="row">
                    <Image
                      priority={true}
                      width={47.5}
                      height={47.5}
                      alt={advantage.icon.data.attributes.alternativeText}
                      className="col-lg-1 col-2 p-1 m-0 img-fluid" src={`${props.baseurl}${advantage.icon.data.attributes.url}`}></Image>
                    <span className="col-lg-11 col-10">
                      <div
                        dangerouslySetInnerHTML={{
                          __html: advantage.description,
                        }}
                      ></div>
                    </span>

                  </ol>)
              })
            }
           
          </ul>
        </div>
        

      </div>
    </>
  )
}
