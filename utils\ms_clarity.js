import React, { useEffect } from 'react';

const Clarity = () => {
    useEffect(() => {
      // Load Clarity script
      const script = document.createElement('script');
      script.src = '/utils/ms_clarity.js'; // Path to clarity.js
      script.async = true;
      document.body.appendChild(script);
  
      // Initialize Clarity
      if (window.clarity) {
        window.clarity('auto', 'f4x9q9qi2h'); // Replace with your project ID
      }
    }, []);
  
    return null; // Clarity component doesn't render anything
  };
  
  export default Clarity;