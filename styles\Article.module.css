.article {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.container {
  display: grid;
  grid-template-columns: 1fr 250px;
  gap: 3rem;
  position: relative;
}

.tableOfContents {
  position: sticky;
  top: 6rem;
  height: fit-content;
  padding: 1.5rem;
  background: #f8f8f8;
  margin-top: 2rem;
}

.tableOfContents h4 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.tableOfContents ul {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-bottom: 2rem;
}

.tableOfContents li {
  margin-bottom: 1rem;
}

.tableOfContents a {
  color: #666;
  text-decoration: none;
  font-size: 1rem;
  transition: color 0.2s ease;
}

.tableOfContents a:hover {
  color: #1a1a1a;
}

.tableOfContents li.active a {
  color: #1a1a1a;
  font-weight: 500;
}

.mainContent {
  min-width: 0;
}

.header {
  position: relative;
  margin-bottom: 4rem;
}

.headerContent {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: 2rem 0;
}

.headerContent h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  color: #1a1a1a;
}

.meta {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  color: #666;
  font-size: 0.9rem;
}

.headerImage {
  position: relative;
  width: 100%;
  height: 500px;
  margin-top: 2rem;
  border-radius: 12px;
  overflow: hidden;
}

.image {
  object-fit: contain;
}

.content {
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.1rem;
  line-height: 1.8;
  color: #333;
}

.section {
  margin-bottom: 3rem;
}

.section h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #1a1a1a;
}

.section p {
  margin-bottom: 1.5rem;
}

.contentImage {
  margin: 2rem 0;
  width: 100%;
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 400px;
  background-color: #f8f8f8;
  overflow: hidden;
}

.imageWrapper img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

.imageLink {
  display: block;
  position: relative;
  cursor: pointer;
}

.imageLink:hover .imageLinkOverlay {
  opacity: 1;
}

.imageLinkOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.imageLinkOverlay span {
  color: white;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 1rem 2rem;
  border: 2px solid white;
  border-radius: 4px;
}

.contentImage figcaption {
  margin-top: 1rem;
  text-align: center;
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

.relatedArticles {
  margin-top: 6rem;
  padding-top: 3rem;
  border-top: 1px solid #eee;
}

.relatedArticles h3 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: #1a1a1a;
}

.relatedGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.relatedCard {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.relatedCard:hover {
  transform: translateY(-4px);
}

.relatedCard.noImage {
  background-color: #f8f8f8;
  padding: 2rem;
  border-radius: 8px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.relatedCard.noImage h4 {
  margin: 0;
}

.relatedImage {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.relatedCard h4 {
  font-size: 1.2rem;
  font-weight: 500;
  color: #1a1a1a;
  line-height: 1.4;
}

.progressWrapper {
  width: 100%;
  height: 4px;
  background: #eee;
  border-radius: 2px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: #1a1a1a;
  transition: width 0.2s ease;
}

@media (max-width: 768px) {
  .article {
    padding: 1rem;
  }

  .headerContent h1 {
    font-size: 2rem;
  }

  .headerImage {
    height: 300px;
  }

  .content {
    font-size: 1rem;
  }

  .section h2 {
    font-size: 1.5rem;
  }

  .relatedGrid {
    grid-template-columns: 1fr;
  }

  .imageWrapper {
    height: 300px;
  }
  
  .imageLinkOverlay {
    opacity: 1;
    background: rgba(0, 0, 0, 0.5);
  }
  
  .imageLinkOverlay span {
    font-size: 1rem;
    padding: 0.8rem 1.5rem;
  }
}

@media (max-width: 1024px) {
  .container {
    grid-template-columns: 1fr;
  }

  .tableOfContents {
    display: none;
  }
}
