import React, {  useEffect, useState } from 'react'
import dynamic from "next/dynamic";
import playIcon from "../assets/play_icon.png";
import { LeadFormModal } from "../components/LeadFormModal";
import Link from "next/link";
import Image from 'next/image';
import { useRouter } from "next/router";
import { BrochureFileNameFormatter, MIN_NAME_LENGTH } from '../utils';
const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });
 


const ProgramPageHeroSection = ({
  landingPageResponse,
  getModalStat,
  meetupId,
  qpms, 
  baseURL, 
  classes,
  learnTrackData,apiUrl,
}) => {
    const router = useRouter();
    const [showIframe, setShowIframe] = useState(false);
    const [isImageHidden, setIsImageHidden] = useState(false);
    const [autoplay, setAutoplay] = useState(false);
    const [leadModal, setLeadModal] = useState(false)
  const [currentLocation, setCurrentLocation] = useState({ city:'', state: '', country: '' });

    const getCurrentLocation = (loc)=>{
      setCurrentLocation(loc)
    }
    
 const {query} = router;


  const handleBrochureModal=()=>{
    document.documentElement.style.overflow="hidden"
      document.documentElement.style.paddingRight="17px"
    setLeadModal(true)
    getModalStat(true)
  }


  const handleCloseModal =()=>{
  
    setLeadModal(false)
    getModalStat(false)
    setFormErrors({})
    setTimeout(() => {
      document.documentElement.style.overflow="auto"
      document.documentElement.style.paddingRight="unset"
    }, 100);
    // router.push({
    //         pathname: `/v2/${meetupId}`,
    //       });
  }

  const handleImageClick1 = () => {
    if(learnTrackData.learning_track_video_url){
      setShowIframe(true);
      }else{
        return null
      }
  };
  const handleImageClick = () => {
    if(learnTrackData.learning_track_video_url){
      setIsImageHidden(true);
      setAutoplay(true);
    }else{
      return null;
    }
  };

  const handleDownload = async () => {
    try {
      const response = await fetch(
        apiUrl + landingPageResponse.brochure.data.attributes.url
      );
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      const formattedName = BrochureFileNameFormatter(meetupId);
      link.download = `${formattedName +".pdf"}`;
      link.click();
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };

  const isValidMobileNumber = (number) => {
    let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    return /^[0-9]{0,15}$/.test(cleanedNumber);
  };

  
const [buttonDisabled, setButtonDisabled] = useState(false);
const [countryCodeEntry,setCountryCodeEntry] = useState()

const validEmailExtensions = [
  ".com", ".org", ".net", ".edu", ".gov",
  ".co", ".us", ".ae", ".in"
];

const isValidEmail = (email) => {
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  if (!emailRegex.test(email)) return false;
  
  const domain = email.split("@")[1];
  const extension = domain.slice(domain.lastIndexOf(".") + 1); // Extract extension without the dot

  return validEmailExtensions.includes("." + extension.toLowerCase()); // Include dot for comparison
};


const [formErrors, setFormErrors] = useState({});
const [coutrycode_exclude,setCoutrycode_exclude] = useState()

const [formData, setFormData] = useState({
  name: '',
  email: '',
  number: '',
  role: 'Select',
  years_of_experience:'',
  agree: true,
  location:"",

});
useEffect(() => {
  if (currentLocation.state || currentLocation.country || currentLocation.city) {
    setFormData((prevData) => ({
      ...prevData,
      location: `${currentLocation.city}, ${currentLocation.state}, ${currentLocation.country}`
    }));
  }
}, [currentLocation]);
const handleChange = (e) => {
  const { name, value } = e.target;

  let updatedValue = value;

  // Apply text-only validation for the "location" field
  if (name === "location") {
    updatedValue = value.replace(/[^a-zA-Z\s]/g, '');
  }

  setFormData((prevData) => ({
    ...prevData,
    [name]: updatedValue,
  }));
  setFormErrors((prevErrors) => ({
    ...prevErrors,
    [name]: '',
  }));
};
const checkboxHandler = () => {
    
};

const handleChange1=(e)=>{

if(Number(e.slice(0,2)) ===91){
setCountryCodeEntry(e.slice(0,2))
let sliced_mobile = e.slice(2,12)
    setCoutrycode_exclude(sliced_mobile)

}else if(Number(e.slice(0,3)) ===971){
setCountryCodeEntry(e.slice(0,3))
let sliced_mobile = e.slice(3,13)
setCoutrycode_exclude(sliced_mobile)

}else if(Number(e.slice(0,2)) ===65 || Number(e.slice(0,2)) ===61){
setCountryCodeEntry(e.slice(0,2))
let sliced_mobile = e.slice(2,13)
setCoutrycode_exclude(sliced_mobile)

}

else{
setCountryCodeEntry(e)
}
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      "number": '',
    }));
  
  setFormData((prevData) => ({
    ...prevData,
    "number": e,
  }));
  setFormErrors((prevErrors) => ({
    ...prevErrors,
    "number": '',
  }));
}
const [completePath, setCompletePath] = useState('');
const [queryParams, setQueryParams] = useState('');
 
useEffect(() => {
  if (typeof window !== 'undefined') {
    const currentPath = window.location.pathname.split('/').slice(1).join('/');  
    const qParams = window.location.search.slice(1); 
    let combinedUrl = `${baseURL}/${currentPath}`;
    if (qParams) {
      combinedUrl += `?${qParams}`;
    }
    setCompletePath(combinedUrl);
  }
}, []);
useEffect(() => {
  if (typeof window !== 'undefined') {
    const qParams = window.location.search.slice(1); 
    setQueryParams(`${qParams}`);
  }
}, []);

  const submitContact = async (event) => {
    event.preventDefault();
    const errors = {};
    setButtonDisabled(true);
  
    if (!formData.name) {
      errors.name = 'Name is required';
    }
    if( formData.name.length<MIN_NAME_LENGTH){
      errors.name = 'Name should be atleast 3 characters';
    }
    if (!formData.years_of_experience) {
      errors.years_of_experience = 'Years of experience is required';
    }
    if (!formData.location) {
      errors.location = 'Location is required';
    }
    if (!formData.location && !currentLocation.state) {
      errors.location = 'Location is required';
    }
  
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!isValidEmail(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
  
    if (!formData.number) {
      errors.number = 'Mobile number is required';
    } 
   if(countryCodeEntry){
     if (countryCodeEntry.slice(0,2) ==91 && (coutrycode_exclude).length<10) {
      errors.number = 'Please enter valid mobile number'; 
    }else if((countryCodeEntry.slice(0,2) ==65 || countryCodeEntry.slice(0,2) ==61) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter valid mobile number'; 
    }
    else if((countryCodeEntry.slice(0,3) ==971) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter a valid mobile number '; 
    }
  }
    else if (!isValidMobileNumber(event.target.number.value)) {
      errors.number = 'Please enter a valid mobile number';
    }
  
    if (formData.role === 'Select') {
      errors.role = 'Please select a role';
    }
  
  
  
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setButtonDisabled(false);
      return;
    }
  
        let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    const getCountryCode = (phoneNumber) =>
      phoneNumber.match(/^\+(\d{1,3})/)?.[1];
    const countryCode = getCountryCode(event.target.number.value);
  
   
    // const expirationDate = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);
   
    const json = {
      first_name: formData.name,
      email: formData.email.toLowerCase(),
      country_code: countryCode,
      mobile: cleanedNumber,
      years_of_experience: formData.years_of_experience,
      location: formData.location,
      role: formData.role,
      url: completePath,
      lead_form_submitted: true,
      program_id:
        process.env.NEXT_PUBLIC_BASE_URL === 'https://online.isb.edu'
          ? landingPageResponse.prod_program_id
          : landingPageResponse.staging_program_id,
      // tags: `utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`
    };
  
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_LANDINGPAGE_SFDC_URL}/backend/free_lesson/user_create`,
      {
        body: JSON.stringify(json),
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
      }
    );

    const data = await response.json();
    const userId = data.user_id;

    if (response.status === 200) {
      const now = new Date();
      const expirationDate = new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000); // 60days from now
      document.cookie = `leadform_name=${formData.name.trim()}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_email=${formData.email?.trim()}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_mobile=${json.mobile}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_country_code=${json.country_code}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_ProgramId=${json.program_id}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_role=${formData.role}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_id=${userId}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_location=${formData.location}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_years_of_experience=${formData.years_of_experience}; Expires=${expirationDate.toUTCString()}; path=/;`;

      const cookies = document.cookie.split(';')

      handleDownload();
      handleCloseModal()
      setFormData({
        name: '',
        email: '',
        number: '',
        role: 'Select',
        years_of_experience: '',
        agree: true,
        state_country: "",
      })
      setButtonDisabled(false)

      setTimeout(() => {
        router.push(
          {
            pathname: `${`/${meetupId}/StorefrontThankYou`}`,
            query: {...query, showModal: true }, 
          },
          `${`/${meetupId}/StorefrontThankYou${qpms ? `?${queryParams}` : ''}`}`, { shallow: true }
        );
      }, 10);
    }
    else{
      setButtonDisabled(false);
    }
  };


  return (
    <div className={`${classes.fold1bg}`}>
    <div className={`${classes.hideCrumb} mx-auto pt-lg-5 px-3 px-lg-0`}>
      <div className={`d-flex ${classes.breadCrumb} px-0`}>
        <Link style={{ textDecoration: "none" }} href="/">
          <p className="text-white m-0">ISB Online</p>
        </Link>
        <svg
          style={{ fill: "#ffffff" }}
          width="24"
          height="18"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_3275_9282)">
            <path d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z" />
          </g>
          <defs>
            <clipPath id="clip0_3275_9282">
              <rect width="24" height="24" fill="#057092" />
            </clipPath>
          </defs>
        </svg>
       
        <p className={`text-white m-0 ${classes.breadCrumblast}`}>
          {learnTrackData.learning_track_name}
        </p>
      </div>
    </div>
    <div
      className={`row flex-row-reverse col-lg-12 m-0 mx-auto ${classes.equalPadding} py-0 pb-lg-5 py-md-0`}
    >
      <>
        {

          (
            !showIframe ? (
              <div
                className={`col-lg-6 col-md-12 p-0 ${classes.imageWrapper} d-lg-none`}
              >
                <Image
                  onClick={handleImageClick1}
                  alt=""
                  width={0}
                  height={0}
                  priority={true}
                  sizes="100vw"
                  style={{ width: "100%", height: "auto" }}
                  src={`${apiUrl}${learnTrackData.learning_track_image.data.attributes.formats.large.url}`}
                ></Image>

                <div className={classes.playIcon}>
                  {learnTrackData.learning_track_video_url &&<Image alt="" priority={true} onClick={handleImageClick1} height={62} width={62} src={playIcon}></Image>}
                </div>
              </div>
            ) : (
              <iframe
                className={`col-lg-6 col-md-12 p-0 d-lg-none ${classes.mobileiframeWrapper}`}
                src={learnTrackData.learning_track_video_url}
                width="642px"
                height="216px"
                allowFullScreen={true}
                allow="autoplay; fullscreen; picture-in-picture"
              />
            )
          )
        }
        {/* //second div */}
        {
          <div
            className={`col-lg-6 col-md-12 p-0 ${classes.second_div} `}
          >
             
            <div className={`${classes.video_box} `}>
              <div className={`${classes.video_overlays} `}>
                {!isImageHidden && (
                  <>
                    {learnTrackData.learning_track_image.data.attributes && learnTrackData.learning_track_image.data.attributes.formats.large.url &&
                    <div className={classes.playIcon}>
                      {learnTrackData.learning_track_video_url && <Image alt="" priority={true} onClick={handleImageClick} height={62} width={62} src={playIcon}></Image>}
                    </div>}
                    <Image
                    alt=""
                      onClick={handleImageClick}
                      width={0}
                      height={0}
                      priority={true}
                      sizes="100vw"
                      style={{ width: "525px", height: "100%", }}
                      src={`${apiUrl}${learnTrackData.learning_track_image.data.attributes.formats.large.url}`}
                    />
                  </>
                )}
              </div>
             {learnTrackData.learning_track_video_url && <div>
                <ReactPlayer
                  url={learnTrackData.learning_track_video_url}
                  className='react-player'
                  playing={autoplay}
                  width='525px'
                  height='100%'
                  controls={true}
                />
              </div>}
            </div>
          </div>
        }
      </>


      <div className="col-lg-6 px-lg-0 px-3 py-lg-0 py-4 col-md-12 align-items-center d-flex justify-content-center pe-lg-4">
        <div className={classes.downloadBrochure}>
          <h1 className="text-white">
            <b>{learnTrackData.learning_track_name}</b>
          </h1>
          <h5 className="text-white">{learnTrackData.Headline_text}</h5>
          <h2 className={`my-3 ${classes.description}`}>
            {learnTrackData.track_description}
          </h2>
    
          <LeadFormModal
            getLoc={getCurrentLocation}
            handleCloseModal={handleCloseModal}
            landingPageResponse={landingPageResponse}
            submitContact={submitContact}
            formData={formData}
            formErrors={formErrors}
            handleChange={handleChange}
            handleChange1={handleChange1}
            buttonDisabled={buttonDisabled}
            leadModal={leadModal}
            apiUrl={apiUrl}
            checkboxHandler={checkboxHandler}/>
          <button className={`${classes.fold1btn} fw-bolder px-5 pointer`} onClick={handleBrochureModal}>Download Brochure</button>
        </div>
       
      </div>
    </div>
  </div>
  )
}

export default ProgramPageHeroSection