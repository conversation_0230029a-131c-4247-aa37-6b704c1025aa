import classes from "./404.module.css";
import Link from "next/link";
import errorImage from "../assets/404.png";
import Image from "next/image";
import BottomFold from "../components/bottom_fold_one";
import Head from "next/head";

export default function Custom404(props) {
  return (
    <>
       <Head>
        <title>404 - Page Not Found</title>
      </Head>
      <main className={`pt-5 m-0 mx-auto ${classes.equalPadding}`}>
        <div className="pt-5 pb-4">
        <Image priority={true} src={errorImage} height={250} width={250} alt="error_image"></Image>    
        <p className={classes.title}>The page you were looking for doesn&apos;t exist.</p>
        <p className={classes.description}>
          You may have mistyped the address or the page may <br/> have moved. Click
          below to navigate to the ISB Online home.
        </p>
        </div>
        <div className="pb-5 text-center">
                  <Link rel="canonical" href="/">
                  <button
                    style={{
                      backgroundColor: "#057092",
                      color: "white",
                      border: "none",
                      width:"180px",
                      padding: "12px",
                    }}
                  >
                    Go to Home
                    <svg
                      width="30"
                      height="12"
                      viewBox="0 0 15 13"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8.6225 12.8462L7.54201 11.7797L11.9903 7.33147H0.469696V5.78791H11.9903L7.54201 1.35369L8.6225 0.273194L14.909 6.55969L8.6225 12.8462Z"
                        fill="white"
                      />
                    </svg>
                  </button>
                  </Link>
                </div>
      </main>
      <BottomFold data = {props.bottomFoldData}></BottomFold>
    </>
  );
}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;
  const [tqData,bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/thank-you-page?populate[courses][populate][0]=course_short_image`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      tqData: tqData,
      apiUrl: APIUrl,
      bottomFoldData:bottomFoldData
    },
    revalidate:240
  };
}
