import React, { useState } from 'react';
import styles from './LeadForm.module.css';
import Image from 'next/image';


const LeadForm = ({ leadFormData, apiUrl }) => {

  const leadData = leadFormData.data.attributes.lead_form;
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    interests: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState({ success: false, message: '' });

  // const interests = [
  //   'Human Resources', 'Analytics', 'Public Policy', 'Economy', 'Government',
  //   'Finance & Accounting', 'Technology', 'Strategy & Organisation', 'Leadership',
  //   'Entrepreneurship', 'Sales', 'Marketing', 'Healthcare', 'Digital Transformation', 'ESG'
  // ];

  const interests = leadFormData.data.attributes.topics.data.map((interest) => {
    return interest.attributes.topic_name;
  });



  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleInterestChange = (interest) => {
    setFormData(prev => {
      const newInterests = prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest];
      return {
        ...prev,
        interests: newInterests
      };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus({ success: false, message: '' });

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/leads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          interested: formData.interests
        })
      });

      if (!response.ok) {

        throw new Error('Failed to submit form');
      }

      const data = await response.json();
      setSubmitStatus({
        success: true,
        message: 'Thank you for your interest! We will get back to you soon.'
      });

      // Reset form after successful submission
      setFormData({ name: '', email: '', interests: [] });
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus({
        success: false,
        message: 'Failed to submit form. Please try again later.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.leadFormContainer}>
      <div className={styles.formContent}>
        <div className={styles.headermain} >
          <h2>{leadData.title}</h2>
          <Image
          src={`${apiUrl}${leadData.icon.data.attributes.url}`}
          alt="ISB Campus"
          width={80}
          height={47}
          priority
        />
        </div>
        <div className={styles.header}>
        <p>{leadData.sub_title}</p>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.inputGroup}>
            <div className={styles.formField}>
              <label htmlFor="name" className="mb-2">{leadData.name_label}</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                // placeholder="Your Name*"
                required
                className={styles.input}
                disabled={isSubmitting}
              />
            </div>

            <div className={styles.formField}>
              <label htmlFor="email" className="mb-2">{leadData.email_label}</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                // placeholder="Email*"
                required
                className={styles.input}
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div className={styles.interestsSection}>
            <label style={{ color: "#192890", fontSize: "16px" }}>{leadData.interested_in_text}</label>
            <div className={styles.interestsGrid}>
              {interests.map((interest, index) => (
                <button
                  key={index}
                  type="button"
                  className={`${styles.interestButton} ${formData.interests.includes(interest) ? styles.active : ''
                    }`}
                  onClick={() => handleInterestChange(interest)}
                  disabled={isSubmitting}
                   
                >
                  {interest}
                </button>
              ))}
            </div>
          </div>

          {/* {submitStatus.message && (
            <div className={`${styles.submitMessage} ${submitStatus.success ? styles.success : styles.error}`}>
              {submitStatus.message}
            </div>
          )} */}

          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : leadData.submit_btn_title}
          </button>
        </form>
      </div>

      <div className={styles.illustration}>
        <Image
          src={`${apiUrl}${leadData.footer_img.data.attributes.url}`}
          alt="ISB Campus Illustration"
          width={800}
          height={120}
          className={styles.campusImage}
          priority
        />
      </div>
    </div>
  );
};

export default LeadForm;
