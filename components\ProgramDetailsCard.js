import React, {useEffect, useMemo, useState} from 'react'
import Moment from "moment";
import classes1 from "../pages/finance.module.css"
import { ThankYouSection } from './ThankYouSection';
import { StartYourApplicationBtn } from './StartYourApplicationBtn';
import { PaymentModal } from '../pages/lpv2/[learning_track_id]';
import { LeadForm, PhoneInputField } from './LeadForm';
import GroupEnrolment from './GroupEnrolment';
import { BrochureFileNameFormatter } from '../utils';
import DataTable from './DataTable';

export const ProgramDetailsCard = ({
    trackFoldData,
    landingPageResponse,
    classes,
    learnTrackData,
    baseURL,
    qpms,
    andQpms,
    mailId,
    fullUrl,
    isv2,
    meetupId,
    utm_source,
    utm_medium,
    utm_campaign,
    utm_term,
    utm_content,
    apiUrl,
    url,
    tags,
    financingData,
    financingLoading,
    financingError
}) => {
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showGroupEnrolment, setShowGroupEnrolment] = useState(false);
  const [thankYouModal, setThankYouModal] = useState(false);
  const [showFinancOptionsModal, setShowFinancOptionsModal] = useState(false);
  

const handleDownload = async () => {
  try {
    const response = await fetch(
      apiUrl + landingPageResponse.brochure.data.attributes.url
    );
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    const formattedName = BrochureFileNameFormatter(meetupId);
    link.download = `${formattedName +".pdf"}`;
    link.click();
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};
 

  const handleClosePaymentModal = (groupleadflag) => {
     
    document.getElementById("modal_content").scrollTop = 0;
    document.documentElement.style.overflow="unset"
    document.documentElement.style.paddingRight="unset"
    setShowPaymentModal(false)
    setShowFinancOptionsModal(false)
    setShowGroupEnrolment(false)
    if(groupleadflag===true){
      document.getElementById("modal_content").scrollTop = 0;
      document.documentElement.style.overflow="hidden"
      document.documentElement.style.paddingRight="17px"
      setThankYouModal(true)
    }else{
      setThankYouModal(false)

    }
    setFormErrors({})

  };
  const handleShowPaymentModal = (e) => {
    document.getElementById("modal_content").scrollTop = 0;
    document.documentElement.style.overflow="hidden"
    document.documentElement.style.paddingRight="17px"
    const id= e.target.id
    if(id==="payment_modal"){
      setShowPaymentModal(true);
    }else if(id==="financing_options"){
      setShowFinancOptionsModal(true)
    }else if(id==="group_enrolment"){
      setShowGroupEnrolment(true)
      // setThankYouModal(true)
    }
   
  }

  const isValidMobileNumber = (number) => {
    let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    return /^[0-9]{0,15}$/.test(cleanedNumber);
  };

  
const [buttonDisabled, setButtonDisabled] = useState(false);
const [countryCodeEntry,setCountryCodeEntry] = useState()

const validEmailExtensions = [
  ".com", ".org", ".net", ".edu", ".gov",
  ".co", ".us", ".ae", ".in"
];

const isValidEmail = (email) => {
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  if (!emailRegex.test(email)) return false;
  
  const domain = email.split("@")[1];
  const extension = domain.slice(domain.lastIndexOf(".") + 1); // Extract extension without the dot

  return validEmailExtensions.includes("." + extension.toLowerCase()); // Include dot for comparison
};


const [formErrors, setFormErrors] = useState({});
const [coutrycode_exclude,setCoutrycode_exclude] = useState()

 
 
 


  const [number, text] = learnTrackData.learning_track_hrs_week.match(/^([\d\s-]+)\s(.+)/).slice(1);

  const prgmCardTxtStyl ="shadow-sm bg-light ps-4 pe-2 py-4 rounded justify-content-center d-flex"
  return (
    <div  className={`col-lg-4 col-xs-12  shadow-sm px-4 pb-4 pt-4 mt-xs-3 bg-white mt-5 mt-lg-0 ${classes.sidebar}`}>
            <h4 className={`${classes.fontTwenty} text-center pb-3`}>
              {trackFoldData.application_head_title}
            </h4>
            
            <div className={prgmCardTxtStyl}>
              <div className='text-center'>
                {/* <h4 className={` ${classes.fontTwentyBlueHeading_new} mb-3 text-left`}>
                  {learnTrackData.Admission_details_title}
                </h4> */}
                <p className={`${classes.application_custm_font} mb-2`}>Starts on {Moment(learnTrackData.learning_track_available_date).format(
                  "MMM DD, yyyy"
                )}</p>
                {/* <p className={`${classes.application_custm_font} mb-2`}>{learnTrackData.learning_track_weeks} weeks,  {learnTrackData.learning_track_hrs_week}  </p> */}
                <p className={` ${classes.application_custm_font_new} `}> ₹{learnTrackData.learning_tracks_fee} + Taxes <br />
                </p>
              </div>
            </div>



            <div className='pt-2 pb-3'>
              <div className={`${classes.paymentPlanLink}`}>
              <a className={`${classes.financing_options_link} isb-text-color pointer`} id={"payment_modal"}  onClick={(e) => handleShowPaymentModal(e)}>{landingPageResponse.lt_data.view_paymet_plan}</a>
              </div>
              <div className={`${classes.paymentPlanLink}`}>
              <a className={`${classes.financing_options_link} isb-text-color pointer`} id={"financing_options"}  onClick={(e) => handleShowPaymentModal(e)}>{landingPageResponse.lt_data.enterprise_pricing}</a>
               </div>
              <div className={`${classes.paymentPlanLink}`}>
              {landingPageResponse.group_enrolment && <a className={`${classes.financing_options_link} isb-text-color pointer`} id={"group_enrolment"}  onClick={(e) => handleShowPaymentModal(e)}>{landingPageResponse.group_enrolment?.ge_link_title}</a>}
               </div>
            </div>

            {/* <div className={prgmCardTxtStyl}>
              <div className='text-center'>
                <h4 className={` ${classes.fontTwentyBlueHeading_new} mb-3`}>
                  {learnTrackData.Track_details_title}
                </h4>
                <div>
                  <p className={`${classes.application_custm_font} mb-2`}>
                    Duration - <span className={classes.application_custm_font_blue}>{learnTrackData.learning_track_weeks}  weeks</span>
                  </p>
                  <p className={`${classes.application_custm_font} mb-2`}>
                   Commitment - <span className={classes.application_custm_font_blue}> {learnTrackData.learning_track_hrs_week} </span>
                  </p>
                  <p className={`${classes.application_custm_font} mb-0`}>
                     <span className={classes.application_custm_font_blue}> {learnTrackData.no_of_courses} Courses + Assessments</span>
                  </p>
                </div>
              </div>
            </div> */}
            
            <div className='pb-4'>
        <div className={classes.stats}>
          <div className={classes.point}>
            <div className={classes.number}>{learnTrackData.learning_track_weeks}</div>
            <div className={classes.desc}>weeks</div>
          </div>
          <div className={classes.point}>
          <div className={classes.number}>{number}</div>
          <div className={classes.desc}>{text}</div>
          </div>
          <div className={classes.point}>
            <div className={classes.number}>{learnTrackData.no_of_courses}</div>
            <div className={classes.desc}>courses</div>
          </div>
          <div className={classes.point}>
            <div className={classes.number}>Self-paced</div>
            <div className={classes.desc}>and live virtual</div>
          </div>
        </div>
      </div>

            <StartYourApplicationBtn
            fullUrl={fullUrl}
            learnTrackData={learnTrackData}
            qpms={qpms}
            andQpms={andQpms}
            baseURL={baseURL}
            trackFoldData={trackFoldData}
            classes={classes}
            />

            <div className="">
              <PaymentModal landingPageResponse={landingPageResponse}  handleClosePaymentModal={handleClosePaymentModal} showPaymentModal={showPaymentModal} />
              <CustomModal buttonDisabled={buttonDisabled} 
              URL={url}
              baseURL={baseURL}
              tags={tags}
              groupEnrolment={showGroupEnrolment} 
              thankYouModal={thankYouModal}
              financingData={financingData}
              financingLoading={financingLoading}
              financingError={financingError}
              
              
              landingPageResponse={landingPageResponse} 
              showGroupEnrolment={showGroupEnrolment} 
              handleDownload={handleDownload} 
              footer_desc={trackFoldData.fin_optns_footer_note} 
              description={trackFoldData.finance_options_desc} 
              classes1={classes1} classes={classes} 
              contact_email={mailId} 
              handleClosePaymentModal={handleClosePaymentModal} 
              showFinancOptionsModal={showFinancOptionsModal}/>
            </div>
          </div>
  )
}


export const CustomModal = ({tags, URL,thankYouModal, buttonDisabled, groupEnrolment, submitContact, handleChange, formErrors, handleChange1, formData, 
  landingPageResponse,showGroupEnrolment,handleDownload, footer_desc, description, learnTrackData, trackFoldData, 
  baseURL, andQpms, qpms, apiUrl, sfthankyouflag, 
  classes1, classes, contact_email, showFinancOptionsModal, handleClosePaymentModal, financingData, financingLoading, financingError }) => {
  
  const modalFlag = showFinancOptionsModal ||showGroupEnrolment || thankYouModal;
  const modalBorderRadius = groupEnrolment || thankYouModal;

  return (
    <div id="myModal" className={`${classes.fin_modal} d-${modalFlag ? "flex" : "none"}`} >
      <div className={classes.modal_content} style={{borderRadius:modalBorderRadius?"8px":""}}>
        {(!sfthankyouflag && !showGroupEnrolment && !thankYouModal) ?
         <CustomModalBody 
         footer_desc={footer_desc}
         description={description} 
         classes1={classes1} 
         classes={classes} handleClosePaymentModal={handleClosePaymentModal} 
          contact_email={contact_email}
          financingData={financingData}
          financingLoading={financingLoading}
          financingError={financingError}
         /> 

          : showGroupEnrolment ? 
          <CustomModalBody 
          URL={URL}
          baseURL={baseURL}
          tags={tags}
          buttonDisabled={buttonDisabled}
          groupEnrolment={groupEnrolment}
          landingPageResponse={landingPageResponse}
          title={landingPageResponse.group_enrolment.title} 
          description={landingPageResponse.group_enrolment.description}
          handleClosePaymentModal={handleClosePaymentModal} classes1={classes1} classes={classes}/> :
          thankYouModal ?
          <CustomModalBody 
          handleDownload={handleDownload}
          thankYouModal={thankYouModal}
          landingPageResponse={landingPageResponse}
          description={landingPageResponse.group_enrolment.ge_thankyou_section.description}
          title={landingPageResponse.group_enrolment.ge_thankyou_section.title} 
          handleClosePaymentModal={handleClosePaymentModal}
          
         classes1={classes1} classes={classes}/> :
          
          
          <div className='bg-white'>
            <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
            <ThankYouSection
              handleDownload={handleDownload}
              qpms={qpms}
              andQpms={andQpms}
              baseURL={baseURL}
              trackFoldData={trackFoldData}
              classes={classes}
              learnTrackData={learnTrackData}
              apiUrl={apiUrl}
              sfthankyouflag={sfthankyouflag} />
          </div> }
      </div>
    </div>
  )
}

export const CustomModalBody=({tags, URL, baseURL, handleDownload, thankYouModal,   
  groupEnrolment, submitContact, formErrors,handleChange, handleChange1, formData, 
  landingPageResponse, title, 
  description, footer_desc, contact_email, 
  classes,classes1, handleClosePaymentModal, financingData, financingLoading, financingError})=>{

  const raw_description = description;

  const phoneRegex = /\+\d{1,4}-\d{2,5}-\d{5,7}/g;

  const styledDescription = raw_description?.replace(
    phoneRegex,
    (match) => `<span class="fs-6 ${classes.main_blue_head}">${match}</span>`
  );
  return(
    <div>
      {!groupEnrolment && !thankYouModal ? 
      <FinancingOptionsModal styledDescription={styledDescription} 
      handleClosePaymentModal={handleClosePaymentModal}  contact_email={contact_email}
      classes={classes} classes1={classes1} 
      landingPageResponse={landingPageResponse} title={title} description={description} 
      footer_desc={footer_desc}
      financingData={financingData} financingLoading={financingLoading} financingError={financingError}
      />: groupEnrolment ?
      <GroupEnrolmentModal
      URL={URL}
      baseURL={baseURL}
      tags={tags}
       styledDescription={styledDescription} 
      handleClosePaymentModal={handleClosePaymentModal} title={title} description={description}
      submitContact={submitContact}
        classes={classes} classes1={classes1}
        handleChange={handleChange}
        formData={formData}
        handleChange1={handleChange1}
        formErrors={formErrors}
        landingPageResponse={landingPageResponse}
      />:
      thankYouModal ? 
      <GroupEnrolmentThankyouModal title={title} landingPageResponse={landingPageResponse} handleDownload={handleDownload} classes={classes} styledDescription={styledDescription} handleClosePaymentModal={handleClosePaymentModal} />:""
      }
    </div>
  )
}

export const FinancingOptionsModal =({ footer_desc, classes, contact_email, handleClosePaymentModal, description, classes1, financingData, financingLoading, financingError})=>{
  return(
    <div className="financing_optns_div" >
            <div className="d-flex justify-content-between">
              <div style={{textAlign:"center"}}>
                <h2>{financingData?.finance_options_title}</h2>
              </div>
              <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
            </div>
            <div id="modal_content" className={classes.paymodal_body}>
              <div className={`${classes.contentext} container-md pt-4 pb-3 pb-md-4`}>
                {/* Display DataTable component */}
                <DataTable 
                  headers={financingData && financingData.header ? financingData.header : []}
                  data={financingData && financingData.data ? financingData.data : []}
                  loading={financingLoading}
                  error={financingError}
                  emptyMessage="No financing institutions found. Please check back later."
                  className={classes1.financeTable}
                  responsive={true}
                  description={financingData && financingData.description ? financingData.description : description}
                  footer={
                    <p className="mb-0">
                      {financingData && financingData.footer_text ? (
                        financingData.footer_text.includes('@') ? (
                          <>
                            {/* Extract the text before the email */}
                            {financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}
                            {/* Extract the full email address */}
                            <a href={`mailto:${financingData.footer_text.match(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}`} className='text-decoration-none'>
                              {financingData.footer_text.match(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[0]}
                            </a>
                            {/* Extract the text after the email */}
                            {financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/).length > 1 ? 
                              financingData.footer_text.split(/[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}/)[1] : ''}
                          </>
                        ) : financingData.footer_text
                      ) : `${footer_desc} `}
                      {!financingData || !financingData.footer_text || !financingData.footer_text.includes('@') ? 
                        <a href={`mailto:${contact_email}`} className='text-decoration-none'>{contact_email}</a> : null
                      }
                    </p>
                  }
                />
              </div>  
            </div>
          </div>
  )
}
export const GroupEnrolmentModal =({tags, baseURL,URL,handleChange1, handleChange,formData,buttonDisabled,formErrors,landingPageResponse,submitContact, handleClosePaymentModal, classes, styledDescription, title})=>{
  return(
    <div className="financing_optns_div" >
            <div className="d-flex justify-content-between">
              <div className={`${'d-flex justify-content-center w-100'}`} style={{textAlign:"center"}}>
                <h2 className={`${`${classes.blueBoldText} fs-3 mt-2`  } `}>{title}</h2>
              </div>
               <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
            </div>
            <div id="modal_content" className={classes.paymodal_body}>
             <GroupEnrolment handleClosePaymentModal={handleClosePaymentModal} tags={tags} URL={URL} baseURL={baseURL} styledDescription={styledDescription} maxwidth={"400px"}
             submitContact={submitContact}
             handleChange1={handleChange1}
              landingPageResponse={landingPageResponse} 
              formErrors={formErrors} buttonDisabled={buttonDisabled} 
              formData={formData} handleChange={handleChange} />
              
            </div>
          </div>
  )
}
export const GroupEnrolmentThankyouModal =({handleDownload,classes, landingPageResponse, handleClosePaymentModal, title, styledDescription})=>{
  return(
    <div className="financing_optns_div"  style={{maxWidth:"420px", textAlign:"center"}}>
            <div className="d-flex justify-content-between">
              <div className={`${'d-flex justify-content-center w-100'}`} style={{textAlign:"center"}}>
                <h2 className={`${`${classes.blueBoldText} fs-3 mt-2 lh-sm`} `}>{title}</h2>
              </div>
            </div>
            <div id="modal_content" className={classes.paymodal_body}>
              <div className='mt-4'>
              <div dangerouslySetInnerHTML={{ __html: styledDescription }}/>
                <div>
                <div className="text-center">
                    <button
                      className={`${ classes.land_btn} ${ "mt-lg-2 mt-xxl-3 mb-xxl-3" } mt-2 text-white `}
                      onClick={handleDownload}
                    >
                      {landingPageResponse.signup_form.brochure_btn}
                    </button>
                  </div>
                     <div  className={`${classes.blueBoldText} fs-6 text-center justify-content-center  d-flex`}>
                     <span className='pointer mt-2 mt-md-2 mt-lg-0' onClick={()=>handleClosePaymentModal(false)}>Close</span>
                     </div>
                </div>
              </div>
            </div>
          </div>
  )
}