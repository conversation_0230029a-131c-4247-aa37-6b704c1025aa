'use client'
import React, { useEffect, useState } from 'react';
import styles from '../pages/v2/[learning_track_id]/index.module.css';
import { Collapse } from 'react-bootstrap';
import VideoPopup from './VideoPopup';
import ImagePopup from './ImagePopup';
import { LeadFormModal } from './LeadFormModal';
import { useRouter } from "next/router";
import { BrochureFileNameFormatter, MIN_NAME_LENGTH } from '../utils';

const CardComponent = ({utm_source,utm_medium,utm_campaign,utm_term,utm_content,query, meetupId, isv2,title, pdfdata, apiUrl, programDetails, courseData, landingPageResponse, getModalStat,baseURL }) => {
  const qpms =
  utm_source !== undefined
    ? `utm_source=${utm_source}&utm_medium=${utm_medium || ''}&utm_campaign=${utm_campaign || ''}&utm_term=${utm_term || ''}&utm_content=${utm_content || ''}`
    : '';

const andQpms = qpms ? `?${qpms}` : '';

  const courseDetails = courseData.data;
  const [showDetails, setShowDetails] = useState(new Array(courseDetails.length).fill(false));
  const [leadModal, setLeadModal] = useState(false)
  const [formErrors, setFormErrors] = useState({});
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [coutrycode_exclude,setCoutrycode_exclude] = useState()
  const [currentLocation, setCurrentLocation] = useState({ city:'', state: '', country: '' });
  const [countryCodeEntry,setCountryCodeEntry] = useState()
  const router = useRouter();

const validEmailExtensions = [
  ".com", ".org", ".net", ".edu", ".gov",
  ".co", ".us", ".ae", ".in"
];
  const toggleShowMore = (index) => {
    setShowDetails((prevShowDetails) =>
      prevShowDetails.map((isOpen, i) => (i === index ? !isOpen : isOpen))
    );
  };
  const isValidEmail = (email) => {
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    if (!emailRegex.test(email)) return false;
    
    const domain = email.split("@")[1];
    const extension = domain.slice(domain.lastIndexOf(".") + 1); // Extract extension without the dot
  
    return validEmailExtensions.includes("." + extension.toLowerCase()); // Include dot for comparison
  };

  const isValidMobileNumber = (number) => {
    let cleanedNumber = number
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    return /^[0-9]{0,15}$/.test(cleanedNumber);
  };
 
  const handleDownload = async () => {
    try {
      const response = await fetch(
        apiUrl + landingPageResponse.brochure.data.attributes.url
      );
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      const formattedName = BrochureFileNameFormatter(meetupId);
      link.download = `${formattedName +".pdf"}`;
      link.click();
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    number: '',
    role: 'Select',
    years_of_experience:'',
    agree: true,
    location:"",
  
  });

  const getCurrentLocation = (loc)=>{
    setCurrentLocation(loc)
  }
  useEffect(() => {
    if (currentLocation.state || currentLocation.country || currentLocation.city) {
      setFormData((prevData) => ({
        ...prevData,
        location: `${currentLocation.city}, ${currentLocation.state}, ${currentLocation.country}`
      }));
    }
  }, [currentLocation]);
    const handleBrochureModal=()=>{
    document.documentElement.style.overflow="hidden"
      document.documentElement.style.paddingRight="17px"
    setLeadModal(true)
    getModalStat(true)
  }
  const handleCloseModal =()=>{
  
    setLeadModal(false)
    getModalStat(false)
    setFormErrors({})
    setTimeout(() => {
      document.documentElement.style.overflow="auto"
      document.documentElement.style.paddingRight="unset"
    }, 100);
    // router.push({
    //         pathname: `/v2/${meetupId}`,
    //       });
  }
  const handleChange = (e) => {
    const { name, value } = e.target;
  
    let updatedValue = value;
  
    // Apply text-only validation for the "location" field
    if (name === "location") {
      updatedValue = value.replace(/[^a-zA-Z\s]/g, '');
    }
  
    setFormData((prevData) => ({
      ...prevData,
      [name]: updatedValue,
    }));
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));
  };
  const checkboxHandler = () => {
      
  };
  
  const handleChange1=(e)=>{
  
  if(Number(e.slice(0,2)) ===91){
  setCountryCodeEntry(e.slice(0,2))
  let sliced_mobile = e.slice(2,12)
      setCoutrycode_exclude(sliced_mobile)
  
  }else if(Number(e.slice(0,3)) ===971){
  setCountryCodeEntry(e.slice(0,3))
  let sliced_mobile = e.slice(3,13)
  setCoutrycode_exclude(sliced_mobile)
  
  }else if(Number(e.slice(0,2)) ===65 || Number(e.slice(0,2)) ===61){
  setCountryCodeEntry(e.slice(0,2))
  let sliced_mobile = e.slice(2,13)
  setCoutrycode_exclude(sliced_mobile)
  
  }
  
  else{
  setCountryCodeEntry(e)
  }
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        "number": '',
      }));
    
    setFormData((prevData) => ({
      ...prevData,
      "number": e,
    }));
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      "number": '',
    }));
  }
  const [completePath, setCompletePath] = useState('');
  const [queryParams, setQueryParams] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname.split('/').slice(1).join('/');  
      const queryParams = window.location.search.slice(1); 
      let combinedUrl = `${baseURL}/${currentPath}`;
      if (queryParams) {
        combinedUrl += `?${queryParams}`;
      }
      setCompletePath(combinedUrl);
    }
  }, []);
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const qParams = window.location.search.slice(1); 
      setQueryParams(`?${qParams}`);
    }
  }, []);

  const submitContact = async (event) => {
    event.preventDefault();
    const errors = {};
    setButtonDisabled(true);
  
    if (!formData.name) {
      errors.name = 'Name is required';
    }
    if( formData.name.length<MIN_NAME_LENGTH){
      errors.name = 'Name should be atleast 3 characters';
    }
    if (!formData.years_of_experience) {
      errors.years_of_experience = 'Years of experience is required';
    }
    if (!formData.location && !currentLocation.state) {
      errors.location = 'Location is required';
    }
  
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!isValidEmail(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
  
    if (!formData.number) {
      errors.number = 'Mobile number is required';
    } 
   if(countryCodeEntry){
     if (countryCodeEntry.slice(0,2) ==91 && (coutrycode_exclude).length<10) {
      errors.number = 'Please enter valid mobile number'; 
    }else if((countryCodeEntry.slice(0,2) ==65 || countryCodeEntry.slice(0,2) ==61) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter valid mobile number'; 
    }
    else if((countryCodeEntry.slice(0,3) ==971) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter a valid mobile number '; 
    }
  }
    else if (!isValidMobileNumber(event.target.number.value)) {
      errors.number = 'Please enter a valid mobile number';
    }
  
    if (formData.role === 'Select') {
      errors.role = 'Please select a role';
    }
  
  
  
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setButtonDisabled(false);
      return;
    }
  
        let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    const getCountryCode = (phoneNumber) =>
      phoneNumber.match(/^\+(\d{1,3})/)?.[1];
    const countryCode = getCountryCode(event.target.number.value);
  
     
    // const expirationDate = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);
     

    const json = {
      first_name: formData.name,
      email: formData.email.toLowerCase(),
      country_code: countryCode,
      mobile: cleanedNumber,
      years_of_experience: formData.years_of_experience,
      location: formData.location,
      role: formData.role,
      url: completePath,
      lead_form_submitted: true,
      program_id:
        process.env.NEXT_PUBLIC_BASE_URL === 'https://online.isb.edu'
          ? landingPageResponse.prod_program_id
          : landingPageResponse.staging_program_id,
      // tags: `utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`,
    };
  
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_LANDINGPAGE_SFDC_URL}/backend/free_lesson/user_create`,
      {
        body: JSON.stringify(json),
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
      }
    );

    const data = await response.json();
    const userId = data.user_id;

    if (response.status === 200) {
      const now = new Date();
      const expirationDate = new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000); // 60days from now
      document.cookie = `leadform_name=${formData.name.trim()}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_email=${formData.email?.trim()}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_mobile=${json.mobile}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_country_code=${json.country_code}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_ProgramId=${json.program_id}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_role=${formData.role}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_id=${userId}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_location=${formData.location}; Expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_years_of_experience=${formData.years_of_experience}; Expires=${expirationDate.toUTCString()}; path=/;`;

      const cookies = document.cookie.split(';')

      handleDownload();
      handleCloseModal()
      setFormData({
        name: '',
        email: '',
        number: '',
        role: 'Select',
        years_of_experience: '',
        agree: true,
        state_country: "",
      })
      setButtonDisabled(false)

      setTimeout(() => {
        router.push(
          {
            pathname: `${`/${meetupId}/StorefrontThankYou`}`,
            query: {...query, showModal: true }, 
          },
          `${ `/${meetupId}/StorefrontThankYou${qpms ? `?${queryParams}` : ''}`}`, { shallow: true }
        );
      }, 10);
    }
    else{
      setButtonDisabled(false);
    }
  };
  return (
    <div className={styles.cardcompMain}>
      <h2 className={styles.sideHeads}>{title}</h2>
      <div className={`${styles.syllabus_main} mb-2 row d-flex justify-content-between`}>
        <div className={`${styles.syllabus_sub} col`}>
          {/* <h2>{programDetails.program_name}</h2> */}
          <p>{programDetails.no_of_courses} Courses, {programDetails.no_of_weeks + ' Weeks'}</p>
        </div>
        <div className="col-md-4 d-flex justify-content-center justify-content-md-end pointer">
           
            <p onClick={handleBrochureModal}>{programDetails.download_syllabus_title}</p>
           
        </div>
      </div>
      {courseDetails.map((course, index) => {
        const data = course?.attributes || {};
        const videosArray = data.video_urls ?data.video_urls.course_video_urls : [];
        const CourseImages = data.course_images ? data.course_images : [];
        const applicationData = data.application_data || {};
        const videoImageCombinationBoolean = (videosArray.length + CourseImages.length) ===2;
        return (
          <div key={index} className={styles.card}>
            <div className={`${styles.sectionsWrapper} mx-0 row`}>
              <div className={`col-12 col-lg-2 p-0 ${styles.blueThumbtab}`} >
                <div className={styles.leftSection}>
                  <p>Course {index + 1}</p>
                  <p className='mb-2 mb-md-1'> {(applicationData.duration && applicationData.content) && ( applicationData.content + ", " +applicationData.duration + ' ' +applicationData.duration_title   )}</p>
                  {/* <p>{applicationData.content}</p> */}
                  {/* <p>{applicationData.no_of_hrs}</p> */}
                  <h2 className={`d-lg-none d-block mb-1`}>{data.course_title}</h2>

                </div>
              </div>
              <div className={`${styles.rightSection} col px-3`}>
                <div className="top d-flex flex-column flex-md-row">
                  <div className={styles.rightLeftSection}>
                    <h2 className='d-lg-block d-none'>{data.course_title}</h2>
                    <p>{data.course_description}</p>
                  </div>
                 {(videosArray.length>0 || CourseImages.length>0) && <div className={styles.video_container_main}>
                    {(videosArray[0]?.url!=="" || CourseImages.length>0) && <h2 >Highlights</h2>}
                   
                      <div
                        className={`${videosArray.length<3 && CourseImages.length ==0 ||
                        (videoImageCombinationBoolean)? styles.rightRightSectionWidth :  styles.rightRightSection} `}
                      >
                    
                        <div className={`${styles.videoContainers} d-flex ${videosArray.length + CourseImages.length >1 ? "gap-3" :""}`}>
                          {(videosArray && videosArray.length>0 && videosArray[0]?.url!="") && data.video_urls?.course_video_urls.map((ele, index) => (
                            <div key={index} className='d-flex flex-column position-relative'>
                            <VideoPopup key={index} videolength={videosArray} videoSrc={ele.url} video_desc={ele.description} />
                              </div>
                          ))}
                          {CourseImages && CourseImages.length>0 &&  CourseImages.map((ele, index) => (
                            <div key={index} className='d-flex flex-column position-relative'>
                            <ImagePopup imageData={ele} apiUrl={apiUrl }/>
                            
                              </div>
                          ))}
                    
                        </div>
                      </div>
                    
                  </div>}

                </div>
                <div className="bottom">
                <div
                  className={styles.showhidelink}
                  onClick={() => toggleShowMore(index)}
                  aria-controls="example-collapse-text"
                  aria-expanded={showDetails[index]}
                >
                  <div className={styles.toggletextstlye}>{showDetails[index] ? 'Hide Details' : 'Show Details'}</div>
                </div>
                  <Collapse key={index} in={showDetails[index]}>
                    <div className='row'>
                      <div className={`${styles.modulesdiv} col`} id="example-collapse-text">
                      <h5 className={styles.module_head_title}>Concepts Covered</h5>
                        {data.syllabi.data.map((j) => (
                          <div key={j.id} className="">
                          <ul className={j.attributes.lessons.length===0 && 'mb-0'}>
                            {j.attributes.lessons.length>0 ? <h5>{j.attributes.module_name}</h5> : 
                            <li >{j.attributes.module_name}</li>
                            }
                            </ul>
                           {j.attributes.lessons.length>0 && <span>
                            {data.topics_title && <p>{data.topics_title}</p>}
                              <ul>
                                {j.attributes.lessons.map((i) => (
                                  <li key={i.id}>{i.lesson_name}</li>
                                ))}
                              </ul>
                            </span>}
                          </div>
                        ))}
                      </div>
                      <div className='col-md-6 col-12 pt-3 pt-md-0'>
                      {data?.exercise_comp &&<h4 className={styles.features_exercises_title}> {data.exercise_comp.title}</h4>}
                      {data?.exercise_comp && data.exercise_comp.exercise_element?.map((ele, index) => {
                        return(
                          <div key={index}>
                          <ul className={styles.regular_icon}>
                           <ol className='d-flex align-items-start gap-2 ps-0'>
                              <div className="icon">
                              <img loading="lazy" src={apiUrl + ele.icon.data.attributes.url} alt="" width="25" height="25"/>
                              </div>
                              <div className="list-text">{ele.description}</div>
                           </ol>
                           
                        </ul>
                          </div>

                        )
                      })}                      

                      </div>
                    </div>
                    
                  </Collapse>
            </div>
                </div>
              </div>
          </div>
        );
      })}
              <LeadFormModal
            getLoc={getCurrentLocation}
            handleCloseModal={handleCloseModal}
            landingPageResponse={landingPageResponse}
            submitContact={submitContact}
            formData={formData}
            formErrors={formErrors}
            handleChange={handleChange}
            handleChange1={handleChange1}
            buttonDisabled={buttonDisabled}
            leadModal={leadModal}
            apiUrl={apiUrl}
            checkboxHandler={checkboxHandler}/>
    </div>
  );
};
export default CardComponent;
