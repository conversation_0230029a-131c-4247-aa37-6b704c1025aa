import classes from "../../[learning_track_id]/[course]/index.module.css";
import Link from "next/link";
import { useRouter } from "next/router";
import Moment from "moment";
import Image from "next/image";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import React, { useState, useRef, useEffect } from "react";
import BottomFold from "../../../components/bottom_fold_one";
import playIcon from "../../../assets/play_icon.png";
import dynamic from "next/dynamic";
import { Collapse } from "../../../ReusableComponents/Collapse";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { fa0, faRotateRight } from "@fortawesome/free-solid-svg-icons";
import CryptoJS from 'crypto-js';


const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });

const arrowIcon = <svg width="15" height="13" viewBox="0 0 15 13" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M8.6225 12.8462L7.54201 11.7797L11.9903 7.33147H0.469696V5.78791H11.9903L7.54201 1.35369L8.6225 0.273194L14.909 6.55969L8.6225 12.8462Z" fill="white" />
</svg>


export default function CoursePage(props) {
  const { query } = useRouter();
  const qpms =
    query.utm_source != undefined
      ? `utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}`
      : ``;
  const queQpms = `?${qpms}`;
  const andQpms = `&${qpms}`;
  const ApplicationFold = (props) => {
    return (
      <div>
        <h4 className={classes.fontTwenty}>
          {coursefold.application_fold_title}
        </h4>


        <div className="shadow-sm bg-light px-2  py-3 rounded">
          <div>

            <div>
              {(courseData.course_url_prod || courseData.course_url_staging) && <div>
                <p className={`${classes.application_custm_font} mb-2`}>Starts on {Moment(courseData.course_available_date).format(
                  "MMM DD, yyyy"
                )}</p>
                <p className={` ${classes.application_custm_font_new} `}> ₹{courseData.course_fee} + Taxes <br />
                </p>
              </div>}
              <p className={`${classes.application_custm_font} mb-2`}>
                Duration - <span className={classes.application_custm_font_blue}>{applicationData.duration}  weeks</span>
              </p>
              <p className={`${classes.application_custm_font} mb-2`}>
                Commitment - <span className={classes.application_custm_font_blue}> {applicationData.no_of_hrs} </span>
              </p>
              <p className={`${classes.application_custm_font}`}>
                <span className={classes.application_custm_font_blue}> {applicationData.content} + Assessments</span>
              </p>
            </div>

          </div>
          <p className={` ${classes.application_custm_font_new} `}>{applicationData.metaverse} {applicationData.metaverse_title}</p>

        </div>

        <div className="py-2">
          <h6 className={`mt-3`}>
            {applicationData.apply_detail_title != null ? `${applicationData.apply_detail_title}` : ``}
          </h6>
        </div>
       

        <ApplyToCourse className={classes.applicationBtn}/>
        
      </div>
    );
  };

  // const ApplicationFoldWithLT = (props) => {
  //   return (
  //     <div>
  //       <h4 className={classes.fontTwenty}>Application</h4>
  //       <div className="d-flex flex-row justify-content-between">
  //         <p className={classes.custm_font}>Fee</p>
  //         <div className={classes.custm_font}>
  //           ₹{learnTrackData.learning_tracks_fee} + Taxes <br />
  //           <svg
  //             width="12"
  //             height="11"
  //             viewBox="0 0 11 11"
  //             fill="none"
  //             xmlns="http://www.w3.org/2000/svg"
  //           >
  //             <g clipPath="url(#clip0_3362_11920)">
  //               <path
  //                 d="M5.60694 9.88575C8.08872 9.88575 10.1006 7.87388 10.1006 5.3921C10.1006 2.91032 8.08872 0.898438 5.60694 0.898438C3.12516 0.898438 1.11328 2.91032 1.11328 5.3921C1.11328 7.87388 3.12516 9.88575 5.60694 9.88575Z"
  //                 stroke="#708FAB"
  //                 stroke-width="0.898732"
  //                 stroke-linecap="round"
  //                 stroke-linejoin="round"
  //               />
  //               <path
  //                 d="M5.60547 7.19004V5.39258"
  //                 stroke="#708FAB"
  //                 stroke-width="0.898732"
  //                 stroke-linecap="round"
  //                 stroke-linejoin="round"
  //               />
  //               <path
  //                 d="M5.60547 3.5957H5.61068"
  //                 stroke="#708FAB"
  //                 stroke-width="0.898732"
  //                 stroke-linecap="round"
  //                 stroke-linejoin="round"
  //               />
  //             </g>
  //             <defs>
  //               <clipPath id="clip0_3362_11920">
  //                 <rect
  //                   width="10.7848"
  //                   height="10.7848"
  //                   fill="white"
  //                   transform="translate(0.214844)"
  //                 />
  //               </clipPath>
  //             </defs>
  //           </svg>
  //           <span style={{ fontSize: "10px" }} className="text-muted">
  //             {" "}
  //             Financing options available
  //           </span>
  //           {/* </Link> */}
  //         </div>
  //       </div>

  //       <div className="d-flex flex-row justify-content-between">
  //         <p className={classes.custm_font}>Next Batch </p>
  //         <p className={classes.custm_font}>
  //         </p>
  //       </div>

  //       <div className="row text-center text_color">
  //         <h6 className="text-center text-danger">Limited Seats Available</h6>

  //         <div className="col shadow-sm bg-light p-3 m-2">
  //           <b className={classes.custm_font}>
  //             {learnTrackData.learning_track_weeks}
  //           </b>{" "}
  //           <br />
  //           <p className={classes.grey_text}>Weeks</p>
  //         </div>
  //         <div className="col shadow-sm bg-light p-3 m-2">
  //           <b className={classes.custm_font}>
  //             {learnTrackData.learning_track_hrs_week}
  //           </b>{" "}
  //           <br />
  //           <p className={classes.grey_text}>Hours per week</p>
  //         </div>
  //       </div>
  //       <div className="row text-center">
  //         <span className="col shadow-sm bg-light p-3 m-2">
  //           <b className={classes.custm_font}>
  //             {learnTrackData.no_of_courses} Courses
  //           </b>{" "}
  //           <br />
  //           <p className={classes.grey_text}>Plus assessments</p>
  //         </span>
  //         <span className="col shadow-sm bg-light p-3 m-2">
  //           <b className={classes.custm_font}>
  //             {learnTrackData.learning_track_metaverse}
  //           </b>{" "}
  //           <p className={classes.grey_text}>Interactive Social Learning</p>
  //         </span>
  //         <div>
  //           <h6 className={`mt-3`}>
  //             This course is part of the {learnTrackData.learning_track_name}{" "}
  //             Learning Track. Click below to apply to the track.
  //           </h6>
  //           <Link
  //             rel="canonical"
  //           >
  //             <p className={`${classes.applicationBtn} btn mt-2 mb-0`}>Apply</p>
  //           </Link>
  //         </div>
  //       </div>
  //     </div>
  //   );
  // };



  const interestedSectionSettings = {
    dots: true,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 4000,
    slidesToShow: 3,
    arrows: false,
    slidesToScroll: 3,
  };

  Moment.locale("en");
  const courseData = props.courseData.data[0].attributes;
  const courseImage =
    props.courseData.data[0].attributes.course_media.data.attributes.url;
  const coursefold = props.foldData.data.attributes;
  const courseSyllabus = props.courseData.data[0].attributes.syllabi.data;
  const courseProf =
    props.courseData.data[0].attributes.professor.data.attributes;
  const apiUrl = props.apiUrl;
  const data = props.courseCompletionData.data.attributes;
  const caselets = props.courseData.data[0].attributes.course_caselets.data;
  const interestedCourses = props.interestedCourses.data;

  const applicationData = props.courseData.data[0].attributes.application_data;
 
  const [showIframe, setShowIframe] = useState(false);


  const handleImageClick1 = () => {
    setShowIframe(true);
  };


  const [isImageHidden, setIsImageHidden] = useState(false);
  const [autoplay, setAutoplay] = useState(false);


  const handleImageClick = () => {
    setIsImageHidden(true);
    setAutoplay(true);
  };


  const [fullUrl, setFullUrl] = useState('');
  const [encryptedUrl, setEncryptedUrl] = useState('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const cookies = document.cookie
        .split(';')
        .map((cookie) => cookie.trim())
        .reduce((acc, cookie) => {
          const [key, value] = cookie.split('=');
          acc[key] = decodeURIComponent(value);
          return acc;
        }, {});

      const {
        leadform_name,
        leadform_email,
        leadform_mobile,
        leadform_ProgramId,
        leadform_role,
        leadform_country_code,
        leadform_id,
        
      } = cookies;

      const countryCode = localStorage.getItem('countryCode');

      const formData = {
        name: leadform_name,
        email: leadform_email,
        role: leadform_role,
        mobile: leadform_mobile,
        checkBox: true,
        countryCode: leadform_country_code || countryCode,
        programId: leadform_ProgramId,
        leadform_id : leadform_id
      };

      const secretKey = process.env.NEXT_PUBLIC_CRYPTO_KEY; // Use a secure secret key
      const encryptedParams = CryptoJS.AES.encrypt(
        JSON.stringify(formData),
        secretKey
      ).toString();

      const allValuesValid = Object.values(formData).every(
        (value) => value !== undefined && value !== 'undefined' && value !== null && value !== ''
      );

      if (allValuesValid) {
        // const params = new URLSearchParams(formData); 
       
        const fullUrlWithParams = `?params=${encodeURIComponent(encryptedParams)}`;
        setFullUrl(fullUrlWithParams);  
      } else {
        setFullUrl('');  
      }
  
    const decryptParams = (encryptedString) => {
      const bytes = CryptoJS.AES.decrypt(encryptedString, secretKey);
      const decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
      return decryptedData;
    };
    const decryptedData = decryptParams(encryptedParams);

  }
  }, []);


  const prodUrl = process.env.NEXT_PUBLIC_BASE_URL;

  const ApplyToCourse = (props) => {
    return (
      <div>
        {coursefold.course_apply_now_btn_title ?
          <div className={`${props.class? "text-center":""} mb-2`}>
            {prodUrl === `https://online.isb.edu` ?

              (courseData.course_url_prod != null ?
                <a href={`${courseData.course_url_prod}${qpms != `` ? andQpms : ``}${fullUrl}`} >
                  <button className={`${props.class ? `${classes.applicationBtn} btn mt-2 mb-0 py-2 px-3` : `${classes.fold1btn} px-5`}  `}>
                    {coursefold.course_apply_now_btn_title}
                  </button>
                </a>
                : "") :

              (courseData.course_url_staging != null ? <a
                href={`${courseData.course_url_staging}${qpms != `` ? andQpms : ``}${fullUrl}`}>
                 <button className={`${props.class ? `${classes.applicationBtn} btn mt-2 mb-0 py-2 px-3` : `${classes.fold1btn} px-5`}  `}>
                    {coursefold.course_apply_now_btn_title}
                  </button>
              </a> : "")}

          </div>
          : ""}
      </div>
    )
  }

const InterestedCourses =()=>{
  return(
    <div
        className={`justify-content-center ${classes.equalPadding} mx-auto mt-5`}
      >
        <h2 className="text-center text-dark">
          <b>You may also be interested in</b>
        </h2>
        <p className="text-center px-3">
         {courseData.course_interested_in_description}
        </p>
        
          <div className="row mb-3 px-3 justify-content-center">
            {interestedCourses.map((course) => {
              return (
                <div
                  key={course.id}
                  className={`px-2 col-lg-4 mb-0 d-flex align-items-stretch col-md-12 col-sm-12 mt-3`}
                >
                  <div
                    className={`card ${classes.boxshadow} ${classes.image_border}`}
                  >
                    <Image
                      width="0"
                      sizes="100vw"
                      height="0"
                      src={`${props.apiUrl}${course.attributes.course_short_image.data.attributes.formats.small.url}`}
                      className={`card-img-top ${classes.image_border} h-auto w-100`}
                      alt={
                        course.attributes.course_short_image.data.attributes
                          .alternativeText
                      }
                    />
                    <div
                      className={`card-body d-flex flex-column ${classes.buttonPos}`}
                    >
                      <h5 className={`card-title ${classes.learnTrackTitle}`}>
                        {course.attributes.course_title}
                      </h5>
                      <p
                        className={`pb-5 card-text ${classes.learning_track_short_description}`}
                      >
                        {course.attributes.course_short_description}
                      </p>
                      <div className={`pb-3 ${classes.buttonPosition}`}>
                        <Link
                          rel="canonical"
                          href={`/course/${course.attributes.courseid}${queQpms}`}
                          className={`btn btn-primary mt-auto align-self-start ${classes.viewbtn}`}
                        >
                          Learn More<i className="fa fa-long-arrow-right"></i>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        
      </div>
  )
}

  return (
    <>
      <div className={`${classes.fold1}`}>
        <div className={`${classes.hideCrumb} mx-auto pt-lg-5 px-3 px-lg-0`}>
          <div className={`d-flex ${classes.breadCrumb} px-3`}>
            <Link style={{ textDecoration: "none" }} href="/">
              <p className="text-white m-0">ISB Online</p>
            </Link>

            <svg
              style={{ fill: "#ffffff" }}
              width="24"
              height="18"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_3275_9282)">
                <path d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z" />
              </g>
              <defs>
                <clipPath id="clip0_3275_9282">
                  <rect width="24" height="24" fill="#057092" />
                </clipPath>
              </defs>
            </svg>
            <p className={`text-white m-0 ${classes.breadCrumblast}`}>
              {courseData.course_title}
            </p>
          </div>
        </div>
        <div
          className={`row flex-row-reverse ${classes.equalPadding} mx-auto py-lg-5 py-0`}
        >

          <>
            {
              (
                !showIframe ? (
                  <div
                    className={`col-lg-6 col-md-12 p-0 ${classes.imageWrapper} d-lg-none`}
                  >

                    <Image
                      onClick={handleImageClick1}
                      width={0}
                      height={0}
                      priority={true}
                      alt="Iframe_Image"
                      sizes="100vw"
                      style={{ width: "100%", height: "auto" }}
                      src={`${props.apiUrl}${courseData.course_media.data.attributes.formats.large.url}`}

                    ></Image>

                    <div className={classes.playIcon}>
                      <Image alt="Play" priority={true} onClick={handleImageClick1} height={62} width={62} src={playIcon}></Image>
                    </div>
                  </div>
                ) : (
                  <iframe
                    className="col-lg-6 col-md-12 p-0 d-lg-none"
                    src={courseData.course_video_link_url}
                    width="640"
                    height="360"
                    allowFullScreen={true}
                    allow="autoplay; fullscreen; picture-in-picture"
                  />
                )
              )
            }


            {
              <div className={`col-lg-6 col-md-12 p-0 ${classes.second_div} `} >
                <div className={`${classes.video_box} `}>
                  <div className={`${classes.video_overlays} `}>
                    {!isImageHidden && (
                      <>
                        <div className={classes.playIcon}>
                          <Image alt="Play" priority={true} onClick={handleImageClick} height={62} width={62} src={playIcon}></Image>
                        </div>
                        <Image
                          onClick={handleImageClick}
                          width={0}
                          height={0}
                          priority={true}
                          alt="Iframe_Image"
                          sizes="100vw"
                          style={{ width: "500px", height: "100%" }}
                          src={`${props.apiUrl}${courseData.course_media.data.attributes.formats.large.url}`}
                        />
                      </>
                    )}
                  </div>
                  <div>

                    <ReactPlayer
                      url={courseData.course_video_link_url}
                      className='react-player'
                      playing={autoplay}
                      width='100%'
                      height='100%'
                      controls={true}
                    />
                  </div>


                </div>
              </div>
            }



          </>
          <div className="col-lg-6 col-sm-12 align-items-center d-flex justify-content-center">
            <div className="p-lg-0 px-1 py-3">
              <h1 className="text-white">
                <b>{courseData.course_title}</b>
              </h1>
              <h2 className={`py-2 text-white ${classes.courseDescription}`}>
                {courseData.course_description}
              </h2>
             
 
              <ApplyToCourse/>


            </div>

          </div>
        </div>
      </div>

      <div
        id="caselet"
        className={`row justify-content-between ${classes.equalPadding} mx-auto`}
      >
        <div id className={`col-lg-8 col-md-12 col-sm-12 pe-lg-5 p-lg-0`}>

          {caselets.length > 0 && (
            <CaseletComponent
            fullUrl={fullUrl}
              qpms={qpms}
              andQpms={andQpms}
              prodUrl={prodUrl}
              caselets={caselets}
              props={props}
              courseData={courseData}
              coursefold={coursefold}
              />
          )}

          <div
            className={`col-lg-4 col-xs-12 shadow-sm px-4 pb-4 pt-3 mt-xs-3 bg-white mt-5 mt-lg-0 ${classes.showApplication}`}
          >
            {applicationData != null ? (
              <ApplicationFold />
            ) : (
              ""
            )}
          </div>
          <div className={`bg-white mt-5 text-center py-4 px-4 mb-4 shadow-sm`}>
            <div className={`d-lg-flex ${classes.profRow}`}>
              <Image
                width="0"
                height="0"
                sizes="100vw"
                className={`img-fluid ${classes.roundedCircle}`}
                src={
                  apiUrl +
                  courseProf.prof_image.data.attributes.formats.thumbnail.url
                }
                alt={courseProf.prof_image.data.attributes.alternativeText}
              />

              {/* Faculty Section */}
              <Faculty courseProf={courseProf} />
              {/* Faculty Section end */}



            </div>
          </div>

          {/* About Course section */}

          <AboutCourse courseData={courseData} coursefold={coursefold} />

          {/* About Course section end*/}


        {/* Syllabus */}
          <div className={`bg-white px-lg-4 pb-lg-4 pt-lg-3 px-4 py-3 pb-md-4 mt-5 shadow-sm`}>
            <div className="d-flex justify-content-between pb-2">
              <h2 className={`${classes.fontTwenty}`}>
                {coursefold.syllabus_fold_title}
              </h2>

              <a href={
                courseData.pdf_download_link.data != null
                  ? `${props.apiUrl}${courseData.pdf_download_link.data.attributes.url}`
                  : ""
              }
                target="_blank"
                rel="noopener noreferrer"
              >
                <button className={classes.download_syllabus_btn} >               
                  {coursefold.download_syllabus_btn_title}
                </button>
              </a>
            </div>
            
            {courseSyllabus.map((item) => (
              <Collapse key={item.id} {...item} />
            ))}
            {/* Syllabus end */}
            <p className={`${classes.disclaimerText} pt-2 m-0 `}>
              <b>Note:</b> {courseData.notes_under_module}
            </p>
          </div>
          <div className="bg-white p-4 mt-5 shadow-sm">
            <h2 className={classes.fontTwenty}>{coursefold.wywe_fold_title}</h2>

            {/* What You Gain window */}
            <div className="row">
              {courseData.Course_page_certificates.map((certificate, index) => (
                <div className={`${courseData.Course_page_certificates.length == 2 ? "col-lg-6" : "col-lg-4"} col-12 p-2 text-center mt-3`} key={index}>
                  <div className={classes.image_container}>
                    <Image
                      height={200}
                      width={200}
                      sizes="100vw"
                      className="img-fluid"
                      style={{ objectFit: "cover" }}
                      src={apiUrl + certificate.Certificate_Image.data.attributes.url}
                      alt={"Certificate Image"}
                    />
                  </div>

                  <div className="mt-3">
                    <h3 className={classes.fontEighteenBlue}>{certificate.title}</h3>
                    <p className={classes.grey_text}>{certificate.description}</p>
                  </div>
                </div>
              ))}
            </div>
            {/* What You Gain window end*/}


            <div className="text-center">
              <Link rel="canonical" href={`/certificates-and-badges${qpms != `` ? queQpms : ``}`}>
                <button
                  style={{
                    backgroundColor: "#057092",
                    color: "white",
                    border: "none",
                    padding: "0px 16px",
                    height: "36px",
                  }}
                >
                  {coursefold.wywe_btn_title}
                </button>
              </Link>
            </div>
          </div>
          <div className={`bg-white mt-5 shadow-sm`}>
            <div className="px-lg-4 pb-lg-4 pt-lg-3 px-4 py-3">
              <h2 className={classes.fontTwenty}>
                {coursefold.course_for_fold_title}
              </h2>
              <div
                dangerouslySetInnerHTML={{
                  __html: courseData.course_for_description,
                }}
              ></div>
            </div>
          </div>

          <div className={`bg-white col-lg-12 p-4 mt-5 shadow-sm mb-3`}>
            <div className={`col ${classes.group_icon_ul}`}>
              <h2 className={`${classes.sideHeads} mb-0`}>
                {props.onlineAdvantage.data.attributes.title}
              </h2>
              <p className={`py-3`}>
                {props.onlineAdvantage.data.attributes.description}
              </p>
              <ul className={classes.group_icons_list}>
                {props.onlineAdvantage.data.attributes.advantages.map((advantage) =>
                  advantage.Course_show_flag === true ? (
                    <ol key={advantage.id} className="row">
                      <Image
                        priority={true}
                        width={47.5}
                        height={47.5}
                        alt={advantage.icon.data.attributes.alternativeText}
                        className="col-lg-1 col-2 p-1 m-0 img-fluid"
                        src={`${apiUrl}${advantage.icon.data.attributes.url}`}
                      />
                      <span className="col-lg-11 col-10">
                        <div
                          dangerouslySetInnerHTML={{
                            __html: advantage.description,
                          }}
                        ></div>
                      </span>
                    </ol>
                  ) : <></>
                )}
              </ul>

            </div>


          </div>
        </div>
        <div
          className={`col-lg-4 col-xs-12 shadow-sm px-4 pb-4 pt-3 mt-xs-3 bg-white mt-5 mt-lg-5 mb-3 ${classes.sidebar}`}
        >
          {applicationData != null ? (
            <ApplicationFold />
          ) : (
           ""
          )}
        </div>
      </div>

      {/* You may also be interested in */}
     {interestedCourses.length>0 && <InterestedCourses/>}
      {/* You may also be interested in -end */}

      <BottomFold data={props.bottomFoldData}></BottomFold>
    </>
  );
}

export const CaseletComponent = ({fullUrl, qpms,andQpms, prodUrl, caselets, props, courseData, coursefold }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const sliderRef = useRef(null);
  const settings = {
    dots: true,
    infinite: true,
    arrows: false,
    autoplay: false,
    autoplaySpeed: 8000,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    beforeChange: (oldIndex, newIndex) => setCurrentSlide(newIndex),

  };

  const FreelessonButton =()=>{
    return(
      <button className={classes.freelesson_btn}>
      {coursefold.caselet_button_title}
      <svg width="30" height="12" viewBox="0 0 15 13" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M8.6225 12.8462L7.54201 11.7797L11.9903 7.33147H0.469696V5.78791H11.9903L7.54201 1.35369L8.6225 0.273194L14.909 6.55969L8.6225 12.8462Z"
          fill="white"
        />
      </svg>
    </button>
    )
  }
  return (
    <div
      className={`mt-3 mt-lg-5 px-lg-4 px-0 px-md-4 pb-md-4 pt-md-3 pt-lg-4 ${classes.sliderContainer}`}
    >
      <Slider {...settings} ref={sliderRef} className={` p-0 ${classes.auto}`}>
        {caselets.map((caselet) => {
          return (
            <div key={caselet.id} className={classes.container}>
              <Image
                priority={true}
                width="0"
                height="0"
                sizes="100vw"
                src={`${props.apiUrl}${caselet.attributes.caselet_image.data[0].attributes.url}`}
                className="img-fluid w-100"
                alt="Caselet Image"
              />
              <div className={classes.centered}>
                {caselet.attributes.caselet_description}
              </div>
            </div>
          );
        })}
      </Slider>

      {/* Experience Free lesson buton & arrows div */}
      <div
        className={`d-flex justify-content-between`}
        style={{ paddingTop: "36px" }}
      >
        {prodUrl ===
          `https://online.isb.edu` ? (
          <>
            {courseData.enable_free_exp_prod ? (
              <div>
                <a
                // target="_blank"  // rel="noopener noreferrer" 
                // href={`${props.lxpProdUrl}${courseData.free_exp_uri_prod}/${learnTrackData.learning_track_id}/${courseData.courseid}_crs?url=${props.baseUrl}/${learnTrackData.learning_track_id}/${courseData.courseid}${qpms != `` ? andQpms : ``}_`}
                href={`${props.lxpProdUrl}${courseData.free_exp_uri_prod}/${courseData.courseid}/_crs?url=${props.baseUrl}/${courseData.courseid}${qpms != `` ? andQpms : ``}${fullUrl}_`}
                >
                <FreelessonButton fullUrl={fullUrl}/>                 
                </a>
              </div>
            ) : (
              ""
            )}
          </>
        ) : (
          <>
            {courseData.enable_free_exp_staging ? (
              <div>
                <a
                // target="_blank"
                // rel="noopener noreferrer"
                href={`${props.lxpStagingUrl}${courseData.free_exp_uri_staging}/${courseData.courseid}/_crs?url=${props.baseUrl}@!${courseData.courseid}${qpms != `` ? andQpms : ``}${fullUrl}_`}
                >
                  <FreelessonButton/>                 

                </a>
              </div>
            ) : (
              ""
            )}
          </>
        )}

        <div className={`${classes.displayNone}  d-lg-flex align-items-center`}>
          <button
            className={`${classes.slide_btn} ${currentSlide === 0? "d-none":""} `}
            onClick={() => sliderRef.current.slickPrev()}
            style={{ rotate: "180deg" }}
          >
            {arrowIcon}
          </button>
          <button className={`${classes.slide_btn} ${currentSlide === caselets.length - 1 ?"d-none":""} `}
            onClick={() => sliderRef.current.slickNext()}

          >
            {arrowIcon}
          </button>
          
          <button className={`${classes.slide_btn} ${currentSlide === caselets.length - 1 ?"":"d-none"} `}
            onClick={() => sliderRef.current.slickNext()}
          >
                      <span style={{fontSize:"13px", padding:"0px", display:"flex"}}>
                        <FontAwesomeIcon icon={faRotateRight} />
                      </span>

          </button>
          
        </div>
      </div>

    </div>
  )
}

export const AboutCourse = ({ coursefold, courseData }) => {
  return (
    <div className={`bg-white px-lg-4 pb-lg-4 pt-lg-3 px-4 py-3 mt-5 shadow-sm`}>
      <div className="d-flex">
        <div>
          <h2 className={classes.fontTwenty}>
            {coursefold.ps_fold_title}
          </h2>

          <div className="pe-lg-2"
            dangerouslySetInnerHTML={{
              __html: courseData.program_structure_content,
            }}
          ></div>

        </div>
      </div>
    </div>
  )
}

export const Faculty = ({ courseProf }) => {
  return (
    <div className={`${classes.alignLeft} ps-3 pt-0`}>
      <p style={{ fontSize: "14px" }} className={`m-0`}>
        Faculty
      </p>
      <h6 className={`py-1 ${classes.moduleTitle}`}>
        {courseProf.prof_first_name + " " + courseProf.prof_last_name}
      </h6>
      <p>{courseProf.prof_description}</p>
      {courseProf.prof_profile_link && <div className={classes.alignRight}>
        <a target="_blank" rel="noopener noreferrer" href={courseProf.prof_profile_link}>
          <b>Read Full Profile</b>
        </a>
      </div>}
    </div>
  )
}




export const getStaticPaths = async (context) => {
  const APIUrl = process.env.API_BASE_URL;
  const res = await fetch(`${APIUrl}/api/featuredcourses`);
  const response = await res.json();
  const paths = response.data.map((course) => {
    return {
      params: {
        courseid: course.attributes.courseid,
      },
    };
  });
  return {
    paths,
    fallback: false,
  };
};

export async function getStaticProps(context) {
  // const learning_track_id = context.params.learning_track_id;

  const course = context.params.courseid;

  const APIUrl = process.env.API_BASE_URL;
  const baseUrl = process.env.BASE_URL;
  const lxpStagingUrl = process.env.ISBO_STAGING_URL;
  const lxpProdUrl = process.env.ISBO_PROD_URL;
  const [
    foldData,
    courseData,
    courseCompletionData,
    interestedCourses,
    // trackData,
    onlineAdvantage,
    bottomFoldData,
  ] = await Promise.all([
    fetch(`${APIUrl}/api/course-fold`).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/featuredcourses?filters[courseid][$eq]=${course}&populate[professor][populate]=*&populate[course_caselets][populate]=*&populate[syllabi][populate]=*&populate[course_media][populate]=*&populate[pdf_download_link][populate]=*&populate[meta_tag][populate]=*&populate[course_short_image][populate]=*&populate[application_data][populate]=*&populate[Course_page_certificates][populate]=*`
    ).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/learning-track-completion?populate=deep,3`
    ).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/featuredcourses?filters[courseid][$ne]=${course}&populate[course_short_image][populate]=*`
    ).then((r) => r.json()),
    // fetch(
    //   `${APIUrl}/api/learning-tracks?filters[learning_track_id][$eq]=${learning_track_id}`
    // ).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/online-advantage?populate[advantages][populate]=*`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);

  return {
    props: {
      courseData: courseData,
      foldData: foldData,
      apiUrl: APIUrl,
      courseCompletionData: courseCompletionData,
      interestedCourses: interestedCourses,
      // trackData: trackData,
      onlineAdvantage: onlineAdvantage,
      bottomFoldData: bottomFoldData,
      lxpStagingUrl: lxpStagingUrl,
      lxpProdUrl: lxpProdUrl,
      baseUrl: baseUrl
    },
    revalidate: 240,
  };
}

