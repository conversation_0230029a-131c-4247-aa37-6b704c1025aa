import React, { useState, useEffect, useRef } from 'react';
import styles from './PopupLeadForm.module.css';
import Image from 'next/image';

const PopupLeadForm = ({ leadFormData, apiUrl, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const leadData = leadFormData.data.attributes.lead_form;
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    interests: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef(null);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const interests = leadFormData.data.attributes.topics.data.map((interest) => {
    return interest.attributes.topic_name;
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const toggleInterest = (interest) => {
    setFormData(prev => {
      const newInterests = prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest];
      return {
        ...prev,
        interests: newInterests
      };
    });
  };

  const handleSubmit = async (e) => {
    e?.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/leads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          interested: formData.interests
        })
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      setFormData({ name: '', email: '', interests: [] });
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isVisible) return null;

  return (
    <div className={styles.popupOverlay} onClick={onClose}>
      <div className={styles.popupContent} onClick={e => e.stopPropagation()}>
        <button className={styles.closeButton} onClick={onClose}>×</button>
        <div className={styles.contentPadding}>
          <div className={styles.header}>
            <div className={styles.headerContent}>
              <h2>{leadData.title}</h2>
              <Image
                src={`${apiUrl}${leadData.icon.data.attributes.url}`}
                alt="ISB Logo"
                width={80}
                height={45}
                priority
              />
            </div>
            <p>{leadData.sub_title}</p>
          </div>

          <form ref={formRef} onSubmit={handleSubmit}>
            <div className={styles.formField}>
              <label htmlFor="name" style={{color:"#192890",fontSize:"16px"}}>{leadData.name_label}</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Your Name*"
                required
                disabled={isSubmitting}
              />
            </div>

            <div className={styles.formField}>
              <label htmlFor="email" style={{color:"#192890",fontSize:"16px"}}>{leadData.email_label}</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Email*"
                required
                disabled={isSubmitting}
              />
            </div>

            <div className={styles.formField}>
              <label style={{color:"#192890",fontSize:"16px"}}>{leadData.interested_in_text}</label>
              <div ref={dropdownRef}>
                <div 
                  className={styles.multiSelect}
                  onClick={() => !isSubmitting && setIsDropdownOpen(!isDropdownOpen)}
                >
                  <div className={styles.selectedText}>
                    {formData.interests.length === 0 ? 'Select interests...' : formData.interests.join(', ')}
                  </div>
                  <div className={styles.dropdownArrow}>
                    ▼
                  </div>
                </div>
                {isDropdownOpen && (
                  <div className={styles.dropdown}>
                    <div
                      className={`${styles.dropdownItem} ${
                        formData.interests.length === interests.length ? styles.selected : ''
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (formData.interests.length === interests.length) {
                          setFormData(prev => ({ ...prev, interests: [] }));
                        } else {
                          setFormData(prev => ({ ...prev, interests: [...interests] }));
                        }
                      }}
                    >
                      {/* <input
                        type="checkbox"
                        checked={formData.interests.length === interests.length}
                        onChange={() => {}}
                        className={styles.checkbox}
                      />
                      <span>All</span> */}
                    </div>
                    {interests.map((interest, index) => (
                      <div
                        key={index}
                        className={`${styles.dropdownItem} ${
                          formData.interests.includes(interest) ? styles.selected : ''
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleInterest(interest);
                        }}
                      >
                        {/* <input
                          type="checkbox"
                          checked={formData.interests.includes(interest)}
                          onChange={() => {}}
                          className={styles.checkbox}
                        /> */}
                        <span>{interest}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </form>
        </div>

        <div className={styles.footerSection}>
          <button 
            type="button"
            className={styles.submitButton}
            disabled={isSubmitting}
            onClick={() => formRef.current?.requestSubmit()}
          >
            {isSubmitting ? 'Submitting...' : leadData.submit_btn_title}
          </button>

          <div className={styles.illustration}>
            <Image
              src={`${apiUrl}${leadData.footer_img.data.attributes.url}`}
              alt="ISB Campus Illustration"
              width={800}
              height={120}
              className={styles.campusImage}
              priority
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PopupLeadForm;
