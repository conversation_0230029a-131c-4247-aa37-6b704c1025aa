.leadFormContainer {
  background: #fff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 1100px;
  margin: 2rem auto;
  position: relative;
  border: 1px solid var(--isb-edge-blue-font-color);
}

.formContent {
  padding: 2rem;
  padding-top: 1rem;
  position: relative;
  padding-bottom: 3rem;
}
.headermain{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
.header{
  margin-bottom: 1rem;
}

.headermain h2 {
  color: var(--isb-edge-blue-font-color);
  font-family: var(--isb-edge-font-family-Reckless);
  font-weight: 600;
  font-size: 30px;
  padding-top: 1rem;

}
.formField label{
 font-family: var(--isb-edge-font-family-inter);
 font-weight: 400;
 font-size: 14px;
 color: var(--isb-edge-blue-font-color);
}
.header p {
  color:var(--isb-edge-blue-font-color);
  margin-bottom: 2rem;
  font-size: 18px;
  font-weight: 600;
  font-family: var(--isb-edge-font-family-inter);
  line-height: 21px;
  text-decoration: underline;
  text-decoration-color: var(--isb-edge-green-color);
  text-decoration-thickness: 2px;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.formField {
  position: relative;
}

.input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #E2E8F0;
  border-radius: 4px;
  font-size: 1rem;
  color: #2D3748;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #002D72;
}

.input::placeholder {
  color: #A0AEC0;
}

.interestsSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.interestsSection label {
  color: #2D3748;
  font-size: 1rem;
}

.interestsGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.interestButton {
  padding: 0.5rem 1rem;
  border: 1px solid #7F7F7F;
  border-radius: 20px;
  font-weight: 500;
  background: none;
  cursor: pointer;
  color: var(--isb-edge-blue-font-color);
  transition: all 0.2s ease;
}

.interestButton:hover {
  background: #F7FAFC;
  border-color: #CBD5E0;
}

.interestButton.active {
  background: var(--isb-edge-green-color);
  color: var(--isb-edge-blue-font-color);
  font-weight: 500;
  border-color: var(--isb-edge-green-color);
}



/* .submitButton:hover {
  background: #001F4D;
} */

/* .submitButton:disabled {
  background-color: #CBD5E0;
  cursor: not-allowed;
} */

.submitMessage {
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.success {
  background-color: #DEF7EC;
  color: #03543F;
  border: 1px solid #B7EBD1;
}

.error {
  background-color: #FDE8E8;
  color: #9B1C1C;
  border: 1px solid #F8B4B4;
}

.submitButton {
  color: #002D72;
  background: white;
  padding: 0.75rem 2rem;
  border: 2px solid #002D72;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  position: absolute;
  left: 50%;
  bottom: -1.5rem;
  transform: translateX(-50%);
  z-index: 1;
}

.illustration {
  margin-top: 0;
  position: relative;
  z-index: 0;
}

.campusImage {
  width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .illustration{
    padding-left: 0;
    padding-right: 0;
  }
  .interestsGrid{
    padding-bottom: 20px;
  }
  .inputGroup{
    display: flex;
    flex-direction: column;
  }
}
@media (max-width: 640px) {
  .formContent {
    padding: 1.5rem;
  }

  .header h2 {
    font-size: 1.25rem;
  }
 
}
