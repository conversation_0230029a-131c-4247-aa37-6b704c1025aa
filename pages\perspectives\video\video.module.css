@media (min-width: 992px) {
    .breadcrumbContainer{
      height: var(--isb-edge-breadcrumb-navbar-height);
      background-color: var(--isb-edge-body-background-color);
      /* border-top: var(--isb-edge-border); */
      /* border-bottom: var(--isb-edge-border); */
      align-content: center;
      padding: 0 60px;
  }
  }
  .article {
    width: 100%;
    max-width: var(--isb-container-max-width);
    margin: 0 auto;
    padding-top: 50px;
    padding-bottom: 50px;
    background-color: #F4F8FA !important;
  }
  .article .posteddate{
    color:#8A8A8A;
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 1rem;
    font-family: var(--isb-edge-font-family-inter);
  } 
  .summarymain p{
    color: #8F8F8F;
    font-size: 18px;
    line-height: 21px;
  
  }
  .description b{
    color: #000000;
    font-style: 18px;
    font-weight: 600;
    line-height: 21px;
  }
  .readTime p{
  font-size: 14px;
  color: #8A8A8A;
  font-weight: 400;
  }
  .container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 4rem;
    position: relative;
  }
  
  .mainContent {
    max-width: 800px;
  }
  
  .header {
    margin-bottom: 3rem;
  }
  
  .headerContent {
    margin-bottom: 2rem;
  }
   
  .headerContent h1 {
    font-size: 2.5rem;
    color: black;
    margin-bottom: 1rem;
    line-height: 1.2;
    margin-top: 1rem;
    font-weight: 700;
  }
   .subtext{
    margin-bottom: 0;
   }
  .meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #666;
    font-size: 0.9rem;
  }
  
  .headerImage {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    height: auto;
    overflow: hidden;
  }
  .programmesforyou{
    font-size: 24px;
    font-weight: 700;
    line-height: 29px;
    margin-bottom: 2rem;
  }
  .sectionImage .image {
    object-fit: contain;
    width: 100%;
    height: 100%;
    overflow: hidden;
  
  }
  .headerImage .image {
    object-fit: cover;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
  }
  
  .section {
    margin-bottom: 3rem;
  }
  .section p{
    color: #000000;
    font-weight: 400;
  
  }
  
  .section h2 {
    font-size: 1.5rem;
    color: black;
    margin-bottom: 2rem;
    font-weight: 700;
    line-height: 22px;
    word-spacing: 1px;
  }
  .section p{
    line-height: 28px;
  }
  .contentImage {
    margin: 2rem 0;
  }
  
  .imageWrapper {
    position: relative;
    width: 100%;
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .imageLink {
    display: block;
    position: relative;
  }
  
  .imageLinkOverlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  .imageLinkOverlay span {
    color: white;
    font-size: 1.1rem;
  }
  
  .imageLink:hover .imageLinkOverlay {
    opacity: 1;
  }
  .asidemain{
    position: sticky;
    top:6rem;
  }
  .pickContent p{
    color: black;
  }
  .pickImage{
    object-fit: cover;
  }
  .pickContent h5{
    color: black;
    font-size: 18px;
    font-weight: 600;
  }
  .tableOfContents {
  
    top: 2rem;
    height: fit-content;
    padding: 1.5rem;
    background: #F4F8FA;
    /* border-radius: 8px; */
    border: 1px solid black;
    margin-bottom: 2rem;
  }
  
  .tableOfContents h4 {
    font-size: 1.2rem;
    color: black;
    margin-bottom: 1rem;
    font-weight: 700;
  }
  
  .tableOfContents ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
  }
  
  .tableOfContents li {
    margin-bottom: 0.5rem;
  }
  
  .tableOfContents a {
    color: black;
    text-decoration: none;
    transition: color 0.2s;
    font-size: 16px;
    line-height: 19px;
  }
  
  .tableOfContents li.active a {
    color: #2196f3;
    font-weight: 500;
  }
  
  .progressWrapper {
    margin-top: 1.5rem;
    height: 4px;
    background: #ddd;
    border-radius: 2px;
    margin: 2rem 0 4rem 0;
  }
  
  .progress {
    height: 100%;
    background: #2196f3;
    border-radius: 2px;
    transition: width 0.2s;
  }
  
   
  
  .relatedArticles h3 {
    font-size: 1.5rem;
    color: #1a237e;
    margin-bottom: 2rem;
  }
  
  .relatedGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
  
  
  .relatedImage {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    margin-bottom: 1rem;
  }
  
  .relatedCard h4 {
    font-size: 26px;
    color: var(--isb-edge-blue-font-color);
    margin: 0;
    font-weight: 600;
    font-family: Reckless;
    line-height: 28px;
  }
  .relatedArticles h3{
  color: var(--isb-edge-blue-font-color);
  font-family: Reckless;
  font-weight: 600;
  font-size: 32px;
  line-height: 35px;
  }
  .noImage {
    padding: 2rem;
    background: #f5f5f5;
    border-radius: 8px;
  }
  
  .shareArticle {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    padding-bottom: 1rem;
    gap: 10px;
    margin-bottom: 1rem;
  }
  
  .shareText {
    font-size: 0.875rem;
    color: #666;
  }
  
  .socialIcons {
    display: flex;
    gap: 0.6rem;
  }
  
  .shareIcon {
    width: 30px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    /* border: 1px solid #e0e0e0; */
    /* border-radius: 50%; */
    /* background: white; */
    cursor: pointer;
    /* transition: all 0.2s ease; */
  }
  
  .shareIcon:hover {
    background: #f5f5f5;
    transform: translateY(-1px);
  }
  
  .shareIcon img {
    width: 30px;
    height: 30px;
    /* opacity: 0.7; */
  }
  
  /* .shareIcon:hover img {
    opacity: 1;
  } */
  
  .picksList {
    margin-bottom: 1rem;
    padding: 1rem 0;
  }
  
  .professorPicks,
  .programmes {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
  }
  
  .professorPicks h4,
  .programmes h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }
  
  .programmes {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #ddd;
  }
  
  .programmes h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }
  
  .programmeWrapper {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #ddd;
    margin-bottom: 2rem;
  }
  
  .programmeWrapper h4 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
  }
  
  .programmeCard {
    background: white;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }
  
   
  .programmeImage {
    width: 100%;
    height: 160px;
    position: relative;
  }
  
  .programmeImage .image {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }
  
  .programmeContent {
    padding: 0.8rem ;
  }
  
  .programmeContent h5 {
    font-size: 22px;
    line-height: 24px;
    font-weight: 700;
    color: black;
    margin-bottom: 1rem;
    font-family: Reckless;
    font-weight: 700;
  }
  
  .programmeContent p {
    font-size: 1rem;
    color: #000000;
    line-height: 1.5;
    margin-bottom: 2rem;
    min-height: 100px;
  }
  
  .exploreBtn {
    background: #192890;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
    width: 100%;
  }
  .mobileiframeWrapper{
    min-height: 210px !important;
  }
  .exploreBtn:hover {
    background: #131d6b;
  }
  .readMoreBtn{
    text-wrap: nowrap;
  }
  @media (max-width: 1024px) {
    .container {
      grid-template-columns: 1fr;
    }
  
    .tableOfContents {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .section{
      margin-bottom: 0.5rem;
      margin-top: 3rem;
    }
    .header{
      margin-bottom: 2rem;
    }
    .progressWrapper{
      display: none;
    }
    .mobileiframeWrapper{
      min-height: 170px !important;
    }
    .container{
      display: flex !important;
      flex-direction: column;
      /* padding: 0.5rem; */
      gap: 2rem;
    }
    .relatedArticles h3{
      text-align: center;
    }
    
    .article {
      padding: 1rem;
    }
    .headerContent{
      margin-bottom: 1rem;
    }
    .headerContent h1 {
      font-size: 2rem;
    }
  
    .headerImage {
      height: 235px;
    }
  
    .relatedGrid {
      grid-template-columns: 1fr;
    }
  
    .shareArticle,
    .picksList,
    .tableOfContents,
    .programmeWrapper {
      margin-bottom: 0.5rem;
      padding: 1rem 0 1rem 0;
    }
  
    .programmes {
      padding: 1rem;
    }
  
    .programmeContent {
      padding: 0.75rem;
    }
  
    .programmeContent h5 {
      font-size: 1rem;
    }
  
    .programmeContent p {
      font-size: 0.8125rem;
    }
  
    .exploreBtn {
      padding: 0.4rem 0.75rem;
      font-size: 0.8125rem;
    }
  
    .readMoreBtn {
      background: none;
      border: none;
      color: #192890;
      font-weight: 600;
      cursor: pointer;
      transition: color 0.2s ease;
    }
    
    .readMoreBtn:hover {
      color: #131d6b;
      text-decoration: underline;
    }
  }
  .videoWrapper iframe{
     width: 800px;
  }
  
  .videoWrapper {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    width: 100%;
    margin: 2rem 0;
  }
  
  .videoWrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
  }
  
  
  .relatedArticles {
    padding: 20px 20px 100px 20px;
  }
  
  .relatedCard {
    width: 338px;
    background: #ffffff;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s;
    min-height: 450px;
  }
  
  .relatedImage {
    position: relative;
    width: 100%;
    height: 180px;
    overflow: hidden;
  }
  
  .relatedImage img {
    object-fit: cover;
  }
  
  .iconWrapper {
    position: absolute;
    top: 140px;
    right: 10px;
    background: #66cc33;
    padding: 5px;
    /* border-radius: 5px; */
  }
  
  .iconWrapper img {
    width: 25px;
    height: 25px;
  }
  
  .articleContent {
    padding: 40px 38px 38px 24px;
    display: flex;
    flex-direction: column;
    flex: 1;
  }
  
  .title {
    font-size: 1.1rem;
    font-weight: bold;
    color: #11224d; /* Dark blue */
    margin-bottom: 5px;
  }
  
  .subtext {
    font-size: 0.9rem;
    color: var(--isb-edge-p-font-color);
  }
  
  .metaInfo {
    font-size: 0.8rem;
    color: var(--isb-edge-p-font-color);
    display: flex;
    justify-content: space-between;
  }
  .metaInfo span{
    line-height: 12px;
    font-weight: 400;
  }
  .metaInfo span:first-child{
   color: #000000;
  
  
  }
  .relatedArticles{ 
  max-width: var(--isb-container-max-width);
  margin: 0 auto;
  }
  
  .categoryIcon {
    width: 32px;
    height: 32px;
    background: #66cc33;
    display: flex;
    align-items: center;
    justify-content: center;
  
  
  }
  .podcastPlayer {
    background: var(--isb-edge-p-font-color);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
  }
  
  .playerLayout {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
  }
  
  .imageSection {
    flex: 0 0 180px;
  } 
  
  .episodeImage {
    width: 180px;
    height: 180px;
    border-radius: 8px;
    object-fit: cover;
  }
  
  .contentSection {
    flex: 1;
  }
   
  
  .episodeInfo h2 {
    font-size: 16px;
    margin-bottom: 0.5rem;
    color: #FFFFFF;
    font-weight: 500;
    font-family: var(--isb-edge-font-family-inter);
  }
  
  .posteddate {
    color: #666;
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }
  
  .audioPlayerWrapper {
    width: 100%;
  }
  
  .audioPlayerWrapper :global(.rhap_container) {
    background-color: transparent !important;
    box-shadow: none !important;
    padding: 0 !important;
  }
  
  .audioPlayerWrapper :global(.rhap_progress-filled),
  .audioPlayerWrapper :global(.rhap_progress-indicator) {
    background-color: #0066cc !important;
  }
  
  .audioPlayerWrapper :global(.rhap_progress-bar) {
    background-color: #e0e0e0 !important;
  }
  
  .audioPlayerWrapper :global(.rhap_button-clear) {
    color: #0066cc !important;
  }
  
  .audioPlayerWrapper :global(.rhap_time) {
    color: #666 !important;
  }
  
  .audioPlayerWrapper :global(.rhap_volume-button),
  .audioPlayerWrapper :global(.rhap_main-controls-button) {
    color: #333 !important;
  }
  
  @media (max-width: 768px) {
    .playerLayout {
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
    }
  
    .imageSection {
      flex: none;
    }
  
    .contentSection {
      width: 100%;
    }
  
    .episodeInfo {
      text-align: center;
    }
  }
  .edge_video {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
  }
