import classes from "./blog.module.css";
import Image from "next/image";
import Link from "next/link";
import laptop from "../assets/Laptop Large.webp"
import BottomFold from "../components/bottom_fold_one";
import image from "../assets/image 109.webp"
import { NextSeo } from 'next-seo';

export default function blog(props) {

  return (
    <>

       <NextSeo
            title="Exploring The Benefits"
            description="Exploring the Benefits: 9 Reasons to Choose Online Learning"
        />
           
      <div>
        <div div className={`${classes.fold1bg}`}>
          <div className={`row flex-row-reverse col-lg-12 m-0 mx-auto ${classes.equalPadding}`}>
            <Image
              width="0"
              height="0"
              sizes="100vw"
              className="img-fluid col-lg-6 col-md-12 p-0 py-lg-4"
              src={laptop}
            alt="laptop"
            />

            <div className="col-lg-6 px-lg-1 px-3 py-lg-0 py-4 col-md-12 align-items-center d-flex justify-content-center pe-lg-4">
              <div>
                <h1 className={`my-3 ${classes.heading}`}>
                  Insights
                </h1>
                <h2 className={`my-3 ${classes.description}`}>Exploring the Benefits: 9 Reasons to Choose Online Learning</h2>
              </div>
            </div>
          </div>

        </div>

        <div className={classes.body}>
          <div className={`row flex-row-reverse col-lg-12 mx-auto ${classes.equalPadding} py-0 pb-lg-1 py-md-0`}>
            <p className="pt-lg-5 pt-3">
            Online learning has become increasingly popular in recent years. The Indian School of Business (ISB) is now making its <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}/about-page`}>world-class, research-backed curriculum</Link> accessible to learners through ISB Online. Here are nine advantages of ISB Online that make it a compelling choice for learners.            
             
            
            </p>
            <p>
              <b>Flexibility and Convenience: </b>ISB Online offers unparalleled flexibility and convenience in learning, allowing participants to study at their own pace and anywhere. Learners can access course materials, attend video lectures, and complete assignments at a time and place that suits them best. This makes it ideal for individuals with busy schedules or those who cannot access campus classes.
            </p>
            <p>
              <b>Customized Learning: </b>ISB Online offers <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}/learning-tracks`}>customized learning experiences</Link> with course materials tailored to meet learner&apos;s individual needs and interests. Learners can choose courses and programmes that suit their career goals, interests, and schedules. The learning tracks are specially designed, keeping varied levels of content that a learner can benefit from.
            </p>
          </div>

          <div className={`row flex-row-reverse col-lg-12 pt-lg-5 pt-3 mx-auto ${classes.equalPadding} py-0 pb-lg-1 py-md-0`}>
            <p>
              <b>Research-Backed Curriculum: </b> ISB Online offers a research-backed curriculum designed and delivered by experienced programme advisors who are experts in their respective fields. The curriculum is based on the latest research findings and is regularly updated to ensure learners receive relevant and up-to-date knowledge.
            </p>

            <p>
              <b>Interactive Learning: </b>ISB Online uses multimedia tools, such as videos, knowledge check and priming questions to create an interactive and engaging learning experience. This helps learners to retain information better and apply it to real-world situations.
            </p>

            <p>
              <b>Professional Development: </b>ISB Online offers opportunities for professional development by providing learners with the knowledge and skills necessary to excel in their chosen field. Online courses and programmes are often designed with industry experts, ensuring learners receive relevant and up-to-date knowledge.
            </p>
          </div>

          <div className={`row flex-row-reverse col-lg-12 m-0 mx-auto ${classes.equalPadding} pt-lg-5 pt-3`}>
            <Image
              width="0"
              height="0"
              sizes="100vw"
              className="img-fluid col-lg-6 col-md-12 p-0"
              src={image}
            alt="networkingImage"
            />
            <div className="col-lg-6  py-lg-0 py-4 col-md-12  d-flex justify-content-center">
              <p className="">
                <b>Networking Opportunities: </b>ISB Online provides networking opportunities, enabling learners to connect with industry leaders and build professional networks. Learners can participate in online discussions and group activities, connecting with peers and programme advisors worldwide.
                <br/><br/><b>Academic Support: </b> ISB Online offers academic support services, including academic advising and technical support, to help learners succeed. Programme advisors are available to help learners navigate the course content and provide guidance on academic planning, while technical support assists learners with any technical issues that may arise during the course.

              </p>
            </div>

          </div>

          <div className={`row flex-row-reverse col-lg-12 mx-auto ${classes.equalPadding} pt-lg-5 pt-0`}>
            <p>
            From flexibility and convenience to research-backed curriculum and social learning, our well-thought-out programmes across different tracks cannot be matched easily by other institutions. We truly aspire to support our learners through their professional journey.
            </p>
          </div>

          <div className="text-center">
            <Link rel="canonical" href="/learning-tracks">
              <button className={`${classes.showBtn} mb-5`} type="submit">
                Explore Learning Tracks
              </button>
            </Link>
          </div>

          <BottomFold data={props.bottomFoldData}></BottomFold>

        </div>
      </div>
    </>
  );
}
export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;
  const [tqData, bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/thankyou-ref`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      tqData: tqData,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData
    },
  };
}