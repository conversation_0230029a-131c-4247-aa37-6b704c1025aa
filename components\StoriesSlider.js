import Image from "next/image";
import Slider from "react-slick";
import classes from '../pages/v2/[learning_track_id]/index.module.css';
import { CaseletNextArrow, CaseletPrevArrow } from "./FacultySlider";
import dynamic from "next/dynamic";



const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });

export const StoriesSlider=({lpflag,title, apiUrl, storyData,  default_settings})=>{
  const settings = {
    dots: true,
    infinite: true,
    speed: 400,
    autoplay: false,
    autoplaySpeed: 4000,
    slidesToShow: storyData.length === 1 ? 1 : storyData.length ===2 ? 2  :  3,
    slidesToScroll: 1,
    arrows: (lpflag ? true :false),
    nextArrow: <CaseletNextArrow paddinngLeft={"0px"}/>,
    prevArrow: <CaseletPrevArrow paddinngLeft={"0px"}/>,
    responsive: [
      {
        breakpoint: 990,
        settings: {
          arrows: true,
          slidesToShow: 1,
        },
      },
    ],
  };
    return(
      <div  className={`${classes.faulty_slider} ${lpflag? `${classes.equalPadding}` : `${classes.equalPadding} shadow-sm bg-white`} px-3 px-md-1  container-fluid`}>
      <StoriesComp
      lpflag={lpflag}
      settings={settings}
      title={title}
      storyData={storyData}
      apiUrl={apiUrl}

      />
    </div>
  
    )
  }

 


  export const StoriesComp=({lpflag,apiUrl,storyData,settings, title})=>{
    return(
      <div className={`container-fluid  ${lpflag ? "" :'bg-white'}`}>
      <div className="row">
      <div className={`col ${lpflag ? "px-0" :'px-4'} pb-5 pt-4 m-auto d-flex flex-column`}>
        <h2 className={`${classes.sideHeads} pb-3`}>
          {title}
        </h2>
        <Slider {...settings}>
          {storyData.map((card, index) => {
            const cardlength = 265;
            const cardDescription = card.description.length > cardlength ? 
            `${card.description.substring(0, cardlength)}...` : 
            card.description;
          return(
            <div key={card.id} className="d-lg-flex mb-3">
              <div className={`col mx-md-2 mx-0 p-md-4 p-3  shadow-sm ${classes.sc_main}`} style={{ backgroundColor:"var(--isb-lightblue-color)",  }}>
                <div className={`d-flex flex-sm-column flex-md-row flex-column justify-content-center align-items-center text-center text-md-start text-lg-start ${classes.profRow} w-100 h-100`}>
                 
                  <div className={classes.storiescardcontent}>
                
                
                  <div className={` ${card.learner_video_url ? classes.storycard_video :""}`}>
                  
                          {card.learner_video_url ?
                            <div className={classes.storycard_video_container} >
                              <ReactPlayer
                                url={card.learner_video_url}
                                className='react-player'
                                playing={false}
                                width='100%'
                                height='100%'
                                controls={true}
                                playIcon
                                preload={true}
                                // light={true}
                              />
                            </div>
                            : <div className={classes.carddescBlack} dangerouslySetInnerHTML={{ __html: cardDescription }} />
                          }
                  
                  </div>


                <div className="d-md-flex align-items-center justify-content-start gap-2">
                  {card.avatar.data &&
                  <div>
                  <Image className="bg-white" width={70} height={70} alt={''}  src={apiUrl+card.avatar.data.attributes.url}></Image>
                  </div>
                  }
                  <div className="d-flex align-items-center gap-2" >
                  <div>
                    {/* <Image alt="Social Media" className={`${ index % 2 ? "":"bg-white"} img-fluid rounded-circle`} src={apiUrl+card.social_media_dp.data.attributes.url} width={30} height={20}></Image> */}
                  
                   <div>
                     {card.social_media_dp.data && <a href={card.linkedin_url}>
                     <Image alt="Social Media" className={` img-fluid rounded-circle`} src={apiUrl+card.social_media_dp.data.attributes.url} width={30} height={20}></Image></a>}
                   </div>
                    <div className={classes.stories_footer_content}>
                      <h6>{card.avatar_title}</h6>
                      <p>{card.avatar_subtitle}</p>
                <p>{card.company_name.length > 30 ?(card.company_name.slice(0,30)+"..."):card.company_name}</p>

                    </div>
                  
                  </div>
                  </div>
                </div>


              </div>
                   
                </div>
              </div>
            </div>
          )})}
        </Slider>
      </div>
        </div>
    </div>
    )
  }