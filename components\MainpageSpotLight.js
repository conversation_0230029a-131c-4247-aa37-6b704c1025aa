// components/MainpageSpotLight.js
import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import chevronRight from "../assets/Chevron Right.svg";
import Link from 'next/link';
import { useRouter } from 'next/router';
import Slider from 'react-slick';
import ReactPlayer from 'react-player/lazy';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import styles from './MainpageSpotLight.module.css';

export default function MainpageSpotLight({ apiUrl }) {
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const sliderRef = useRef(null);
  const router = useRouter();
  const [isEnd, setIsEnd] = useState(false);
  useEffect(() => {
    // Fetch spotlight videos when component mounts
    const fetchVideos = async () => {
      try {
        setLoading(true);
        // Optimized API call with targeted population to only fetch needed fields and include spotlight_video flag
        const apiEndpoint = `${apiUrl}/api/videos?populate[video_first_section][populate]=video_url,thumbnail,post_name,duration,publishedAt,spotlight_video&populate[topics][populate]=*`;
        const response = await fetch(apiEndpoint);
        if (!response.ok) {
            throw new Error(`Error: ${response.status}`);
        }
        
        const data = await response.json();
        // Filter videos to only include those flagged as spotlight_video
        const spotlightVideos = data.data.filter(video => 
          video.attributes.video_first_section?.spotlight_video === true
        );
        setVideos(spotlightVideos || []);
      } catch (err) {
        console.error('Error fetching spotlight videos:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, [apiUrl]);

  // Slider settings

  // Slider settings
  const settings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false, // Custom arrows for desktop
    autoplay: false,
    lazyLoad: 'anticipated', // Preload next/prev slide
    adaptiveHeight: true,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          arrows: true, // Show default Slick arrows on mobile
          adaptiveHeight: true
        }
      }
    ]
  };

  // Track playing video by index
  const [playingVideo, setPlayingVideo] = useState(null);
  const [activeVideoIndex, setActiveVideoIndex] = useState(0);
  
  // Reference to player components
  const playerRefs = useRef({});

  // Navigation functions
  const goToNext = () => {
    // Stop any playing video
    setPlayingVideo(null);
    sliderRef.current.slickNext();
  };

  const goToPrev = () => {
    // Stop any playing video
    setPlayingVideo(null);
    sliderRef.current.slickPrev();
  };

  // Handle slide change to update active video index
  const handleBeforeChange = (current, next) => {
    setActiveVideoIndex(next);
    setPlayingVideo(null);
    
    // Update end state for arrow navigation
    setIsEnd(next === videos.length - 1);
  };

  // Play video by index
  const playVideo = (index) => {
    // Pause any currently playing video
    setPlayingVideo(index);
  };
  
  // Check if specific video is currently playing
  const isVideoPlaying = (index) => {
    return playingVideo === index;
  };

  // Format time from seconds to MM:SS
  

  // Handle topic click navigation
  const handleTopicClick = (topicName, index) => {
    // Format topic name for URL-friendly format
    const formattedTopic = topicName.replace(/\s+/g, '+');
    
    // Navigate to alltopics page with topic filter
    router.push({
      pathname: '/perspectives/alltopics',
      query: { 
        topic: formattedTopic,
        type: 'All'
      }
    }, undefined, { shallow: true });
  };

  if (loading) return <div className={styles.loadingContainer}>Loading spotlight videos...</div>;
  if (error) return <div className={styles.errorContainer}>Error loading videos: {error}</div>;
  if (!videos.length) return null;
  return (
    <div className={styles.spotlightContainer}>
      <h2 className={styles.sectionTitle}>Spotlight</h2>
      
      <div className={styles.sliderWrapper}>
        {/* Custom Navigation Arrows - Only show prev button if not on first slide */}
        {activeVideoIndex > 0 && (
          <button className={`${styles.navButton} ${styles.prevButton}`} onClick={goToPrev} aria-label="Previous">
            <div className={styles.rotatedChevron}>
              <Image src={chevronRight} alt="Previous" width={15} height={15} />
            </div>
          </button>
        )}

        <Slider 
          ref={sliderRef} 
          {...settings} 
          className={styles.slider}
          beforeChange={handleBeforeChange}
        >
          {videos.map((video, index) => {
            const videoData = video.attributes;
            const videoUrl = videoData?.video_first_section?.video_url;
            const thumbnailUrl = videoData?.video_first_section?.thumbnail?.data?.attributes?.url;
            const title = videoData?.video_first_section?.post_name || '';
            const category = videoData?.topic?.category?.data?.attributes?.name || '';
            const topics = videoData?.topics?.data || [];
            const duration = videoData?.video_first_section?.duration_and_date || '';
            const isActive = activeVideoIndex === index;
            
            return (
              <div key={`video-${index}`} className={styles.slideItem}>
                <div className={styles.videoCard}>
                  <div className={styles.thumbnailContainer}>
                    {videoUrl ? (
                      <div 
                        className={styles.playerWrapper} 
                        onClick={() => playVideo(index)}
                      >
                        <ReactPlayer
                          ref={element => playerRefs.current[index] = element}
                          url={videoUrl}
                          width="100%"
                          height="100%"
                          playing={isVideoPlaying(index)}
                          controls={true}
                          light={true}
                          preload="metadata"
                          onPause={() => setPlayingVideo(null)}
                          onPlay={() => setPlayingVideo(index)}
                          playIcon={<button 
                            className={styles.playButton} 
                            aria-label="Play video"
                          >
                            <div className={styles.playIcon}></div>
                          </button>}
                          style={{ margin: '0 auto' }}
                          config={{
                            file: {
                              attributes: {
                                preload: isActive ? 'auto' : 'none',
                                controlsList: 'nodownload',
                                disablePictureInPicture: true,
                                playsinline: true
                              },
                              forceVideo: true,
                              forceHLS: true
                            },
                            youtube: {
                              playerVars: {
                                modestbranding: 1,
                                playsinline: 1,
                                fs: 0
                              }
                            }
                          }}
                        />
                      </div>
                    ) : (
                      <>
                        <div 
                          className={styles.playButtonContainer}
                          onClick={() => playVideo(index)}
                        >
                          <button 
                            className={styles.playButton} 
                            aria-label="Play video"
                          >
                            <div className={styles.playIcon}></div>
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                  
                  <div className={styles.videoDetails}>
                    <div className="d-flex justify-content-between flex-md-row flex-column">
                      <div>
                        {topics && topics.length > 0 && (
                          <div className={styles.videoTopics}>
                            {topics.map((topic, topicIndex) => (
                              <span
                                key={`topic-${topicIndex}`}
                                className={styles.videoTopic}
                                onClick={() => handleTopicClick(topic.attributes.topic_name)}
                              >
                                {topic.attributes.topic_name.toUpperCase()}
                                {topicIndex < topics.length - 1 && " + "}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                      <div>
                      <span className={styles.videoDuration}>{duration}</span>
                      </div>
                      {/* <div>
                        <span className={styles.videoCategory}>{category}</span> 
                      </div> */}
                    </div>
                    
                    <h3 className={styles.videoTitle}>
                      <Link href={`/perspectives/video/${video.attributes.post_id}`}>
                        {title}
                      </Link>
                    </h3>
                    
                    <p className={styles.videoDescription}>
                      {videoData?.video_first_section?.short_summary || ''}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </Slider>

        {/* Custom Navigation Arrow - Show grayed out if at end */}
        {videos.length > 1 && (
          <button 
            className={`${styles.navButton} ${styles.nextButton} ${isEnd ? styles.arrowDisabled : ''}`} 
            onClick={goToNext} 
            aria-label="Next"
            disabled={isEnd}
          >
            <Image src={chevronRight} alt="Next" width={15} height={15} />
          </button>
        )}
      </div>
    </div>
  );


}
