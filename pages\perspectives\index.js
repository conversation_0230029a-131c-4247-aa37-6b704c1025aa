import Image from 'next/image';
import styles from './index.module.css';
import React, { useEffect, useState, useMemo } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import aboutimage from "/assets/aboutpage2.png"
import TrackPageBottomFold from '../../components/TrackPageBottomFold';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import PopupLeadForm from '../../components/EdgeLeadForm/PopupLeadForm';
import { capitalizeFirstLetter, TopicsComponent } from '../../utils';
import { TopicsComponentForSlug } from '../../components/PerspectiveComponents';
import MainpageTopics from '../../components/MainpageTopics';
import MainpageSpotLight from '../../components/MainpageSpotLight';
import TopicsDropdown from '../../components/TopicsDropdown';


export default function Edge(props) {
  
  const apiPosts = props.posts?.data?.attributes?.articles?.data || [];
  const apiPost = props.posts?.data?.attributes?.infographics?.data || [];
  const apiPodcast = props.posts?.data?.attributes?.podcasts?.data || [];
  const apiVideo = props.posts?.data?.attributes?.videos?.data || [];

  const headLineText = props.posts?.data?.attributes?.Headline_text;
  const mainTitle = props.posts?.data?.attributes?.title;
  const topicsBool = props.posts?.data?.attributes?.topics_slider;
  const spotlightBool = props.posts?.data?.attributes?.spotlight_section;
  const apiUrl = props.apiUrl;
  const router = useRouter();
  const { topics } = router.query;
  const currentQuery = router.asPath.includes("?") ? router.asPath.split("?")[1] : "";
  // Create array by combining and mapping both sources
  const combinedArray = [
    // Map all articles
    ...apiPosts.map(post => ({
      id: post.id,
      attributes: {
        ...post.attributes.post_first_section,
        post_id : post.attributes.post_id,
        article_body : post.attributes.articles_body,
        topics : post.attributes.topics,
        position : post.attributes.position
      }
    })), 
    
    // Map all infographics
    ...apiPost.map(post => ({
      id: post.id,
      attributes: {
        ...post.attributes.info_first_section,
        post_id : post.attributes.post_id,
        article_body : post.attributes.info_body,
        topics : post.attributes.topics,
        position : post.attributes.position
      }
    })),
    
    ...apiPodcast.map(post => ({
      id: post.id,
      attributes: {
        ...post.attributes.podcaste_first_section,
        post_id : post.attributes.post_id,
        topics : post.attributes.topics,
        position : post.attributes.position
      }
    })),
    
    ...apiVideo.map(post => ({
      id: post.id,
      attributes: {
        ...post.attributes.video_first_section,
        post_id : post.attributes.post_id,
        topics : post.attributes.topics,
        position : post.attributes.position
      }
    }))
    ]
  // Filter out elements with null position values
  .filter(item => item.attributes.position !== null && item.attributes.position !== undefined)
  // Sort the remaining elements by position
  .sort((a, b) => {
    const posA = a.attributes.position;
    const posB = b.attributes.position;

    return posA - posB;
  });



   const [postDataArray, setPostDataArray] = useState([]);
   const [topicsData, setTopicsData] = useState([]);
   const [posts, setPosts] = useState(combinedArray);
  
   const [showPopup, setShowPopup] = useState(false);

  useEffect(() => {
    if (posts && posts.length > 0) {
      const articleBodies = posts.map(post => post.attributes.article_body).filter(Boolean);
      setPostDataArray(articleBodies);
      // Pass the complete topics data from props instead of trying to extract it from posts
      setTopicsData(props.topics);
    }
  }, [posts]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowPopup(true);
    }, 5000); // Show popup after 5 seconds

    return () => clearTimeout(timer);
  }, []);

  // Function to handle card click and navigation
  const handleCardClick = (post) => {
    const slug = post.attributes.post_id;
    const postType = post.attributes.post_type;
    const { topics, ...otherQueries } = router.query; // Remove `topics` but keep other params
  
    // Convert remaining query params into a string
    const newQuery = new URLSearchParams(otherQueries).toString();
    const route = `/perspectives/${postType}/${slug}${newQuery ? `?${newQuery}` : ""}`;
  
    router.push(route);
  };
  

  // Calculate reading time
  const calculateReadingTime = (articleBody) => {
    if (!articleBody || !Array.isArray(articleBody)) return 0;
    
    let totalWords = 0;
    let wordsPerMinute = 0;
    // Calculate total words from all article sections
    articleBody.forEach(section => {
      if (section.description) {
        const plainText = section.description.replace(/<[^>]*>/g, '');
        totalWords += plainText.trim().split(/\s+/).length;
        wordsPerMinute = section.words_per_min || 200;
      }
    });

    // Average reading speed (words per minute)
    // Calculate and round up reading time
    return Math.ceil(totalWords / wordsPerMinute);
  };

  // Calculate total reading time from all sections
  const totalReadingTime = useMemo(() => {
    let totalTime = 0;


    
    if (postDataArray && postDataArray.length > 0) {
      postDataArray.forEach(section => {
        if (section.description) {
          totalTime += calculateReadingTime([section]);
        }
      });
    }
  
    return totalTime;
  }, [ postDataArray]);

  const readTimePermin = Math.ceil( totalReadingTime);

  const selectedTopics = useMemo(() => {
    if (!topics) return [];
    return topics.split("+").map((t) => decodeURIComponent(t.toLowerCase())); 
  }, [topics]);

  // Filter the combinedArray based on topic_name
  const filteredCards = useMemo(() => {
    if (!selectedTopics.length) return combinedArray; // Return all if no topics selected
    return combinedArray.filter((item) =>
      item.attributes.topics.data.some((topic) =>
        selectedTopics.includes(topic.attributes.topic_name.toLowerCase())
      )
    );
  }, [combinedArray, selectedTopics]);

  const renderCard = (post, layout) => {
    const postData = post.attributes;
    const readingTime = calculateReadingTime(postData.article_body);
    
    const isHorizontal = layout === 'horizontal' || layout === 'large' || layout === 'lastOdd' || layout === 'medium';
    const cardClass = isHorizontal ? styles.horizontalCard : styles.verticalCard;
    const columnClass = layout === 'large' ? `${filteredCards.length===1 ? 'col-md-12' : `${styles.horzontalsinglefulengthcard} col-md-8`}` :
      layout === 'small' ? `${styles.secondsmallCard} col-md-4 mb-3 mb-md-0` :
        layout === 'medium' ? 'col-md-6 mb-3 mb-md-0' :
          layout === 'lastOdd' ? 'col-12' :
            'col-12';

    const cardSizeClass = layout === 'small' ? styles.smallCard :
      layout === 'large' ? styles.largeCard :
        layout === 'medium' ? styles.mediumCard : '';

    return (
      <div key={post.id} className={columnClass} >
        <div 
          className={`${styles.card} ${cardClass} ${cardSizeClass} ${styles.clickable}`}
          role="button"
          tabIndex={0}
        >
          <div 
            className={styles.imageWrapper} 
            onClick={() => handleCardClick(post)}
            style={{ 
              maxWidth: layout === 'lastOdd' ? '260px' : layout === 'mediumCard' ? '30%' : '100%',
              flex: layout === 'lastOdd' || layout === 'mediumCard' ? '0.8' : layout === 'small' ? 'none' : '1'
            }}
          >
           {postData.home_page_image.data&& <Image
              src={`${apiUrl}${postData.home_page_image.data.attributes.url}`}
              alt={postData.post_name}
              fill
              priority
               
              style={{ objectFit: 'cover' }}
            />}
          <div className={`${styles.categoryIcon} ${layout === 'lastOdd' ? styles.centeredIcon : ''}`}>
        {postData.post_type === 'article' ? (
            postData.category_icon && postData.category_icon.data && <Image width={20} height={20} src={`${apiUrl}${postData.category_icon.data.attributes.url}`} alt="Category Icon" />
          ) : postData.post_type === 'infographic' ? (
            postData.category_icon && postData.category_icon.data && <Image width={20} height={20} src={`${apiUrl}${postData.category_icon.data.attributes.url}`} alt="Category Icon" />
          ) : postData.post_type === 'podcast' ? (
            postData.category_icon && postData.category_icon.data && <Image width={20} height={20} src={`${apiUrl}${postData.category_icon.data.attributes.url}`} alt="Category Icon" />
          ) : postData.post_type === 'video' ? (
            postData.category_icon && postData.category_icon.data && <Image width={20} height={20} src={`${apiUrl}${postData.category_icon.data.attributes.url}`} alt="Category Icon" />
          ) : (
            '📄'
          )}
        </div>
          </div>
          

          <div className={styles.content}>
            {postData.topics && postData.topics.data.length > 0 && <span className={styles.category}>
 
              {/* <TopicsComponent Topics={postData.topics.data}/> */}
              <TopicsComponentForSlug Topics={postData.topics.data} />

            </span>
            }



            <h2 onClick={() => handleCardClick(post)} className={styles.title}>{postData.post_name}</h2>
            <p onClick={() => handleCardClick(post)} className={styles.excerpt}>{postData.short_summary}</p>
            <div onClick={() => handleCardClick(post)} className={styles.meta}>
              <span>{postData.author_name}</span>
              <span>{postData.date}{readingTime >0 && ` | ${readingTime} Min`}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderRows = () => {
    let rows = [];
    let currentIndex = 0;

    const totalCards = filteredCards.length;
    const isOdd = totalCards % 2 !=0
    
    if (currentIndex < filteredCards.length) {
      const rowPosts = filteredCards.slice(currentIndex, currentIndex + 2);
      rows.push(
        <div key={`row-${currentIndex}`} className="row">
          {renderCard(rowPosts[0], 'large')}
          {rowPosts.length > 1 && renderCard(rowPosts[1], 'small')}
        </div>
      );
      currentIndex += 2;
    }

    // Middle rows: All 6-6 split
    while (currentIndex < (isOdd % 2 !== 0 ? totalCards - 1 : totalCards)) {
      const rowPosts = filteredCards.slice(currentIndex, currentIndex + 2);
      rows.push(
        <div key={`row-${currentIndex}`} className="row mb-0 mb-md-3">
          {rowPosts.map(post => renderCard(post, 'medium'))}
        </div>
      );
      currentIndex += 2;
    }

    // Last card for odd number of total cards
    if (isOdd % 2 !== 0 && currentIndex < totalCards) {
      rows.push(
        <div key={`row-${currentIndex}`} className="row mb-4">
          {renderCard(filteredCards[currentIndex], 'lastOdd')}
        </div>
      );
    }

    return rows;
  };

  const breadcrumbItems = [
    { label: 'Perspectives', href: '/perspectives' },
    // { label: 'Home', href: '/perspectives' },
  ]
  
  const [navData, setProgrammes] = useState(null);

  useEffect(() => {
    fetchData()
     
  }, [])

const [windowWidth, setWindowWidth] = useState(0);


  useEffect(() => {
    if (typeof window !== "undefined") {
      setWindowWidth(window.innerWidth - 456);
    }
  }, []);


  const fetchData = async () => {
    const url = process.env.NEXT_PUBLIC_API_BASE_URL;
    const res = await fetch(`${url}/api/navbars?populate=deep,4`);
    const data = await res.json();
    setProgrammes(data?.data[0].attributes);

  };
const navbarData = navData && navData;
  return (
    <div className={styles.bodymastercontainer}>
         <div style={{ position: 'relative', zIndex: 1000, width: '100%' }}>
          <TopicsDropdown />
        </div>
        <div className="container">
          <Breadcrumb   items={breadcrumbItems} headlinetext={headLineText}/>
        </div>
        
      {/* <div className={styles.divider} style={{ marginTop: '10px' }}>
        <div className={styles.subdivider}>
        <div className={styles.supersubdivider}>

        </div> 
        </div>
      </div> */}
      <div className={styles.bodymaincontainer}  >
        <div className={`${styles.maincontainer}`}   >
        
          <div className={`${styles.posts_title}  `}  >


            {combinedArray.length > 0 && (
            <div className='d-flex flex-row'>
              <div className='me-2' style={{ whiteSpace: 'nowrap', minWidth: 'fit-content' }}>
                <h1>{mainTitle}</h1>
              </div>
              <div className={styles.horizontalline}></div>
            </div>

            )}
            {renderRows()}
           {topicsBool &&  <div className="topics_slider_main">



              <div className='d-flex mt-5 mb-0 mb-md-1 px-md-0 px-3 align-items-center justify-content-between'>
                <div className='  '  >
                  <h1 className='my-0 me-2 pt-0'>Topics</h1>
                </div>
                  <div className={styles.topics_horizontalline} style={{ width: '100%' }}></div>
                <div className=' ms-2'>
                  <Link href="/perspectives/alltopics" className="text-decoration-none ">
                    <div className="d-flex align-items-center mt-1" style={{ whiteSpace: 'nowrap', fontSize: '14px', color: '#0041E9', fontFamily: 'var(--isb-edge-font-family-inter)' }}>
                      View All
                    </div>
                  </Link>
                </div>
              </div>


          

              {topicsData && <MainpageTopics topics={topicsData} apiUrl={apiUrl} />}
            </div>}

            {spotlightBool && <MainpageSpotLight apiUrl={apiUrl} />}
          </div>
        </div>
        <div className={styles.bottomsupersubdivider}>

        </div>
      </div>
      
        <TrackPageBottomFold navData={navbarData} getIsbMail={"isbemail"} apiUrl={apiUrl} />
        {/* {showPopup && (
          <PopupLeadForm 
            apiUrl={apiUrl} 
            leadFormData={props.leadFormData}
            onClose={() => setShowPopup(false)} 
          />
        )} */}

    </div>
  );
}
 
export async function getStaticProps() {
  const APIUrl = process.env.API_BASE_URL;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;

  const [perspectivesRes, leadFormRes, topicsRes] = await Promise.all([
    fetch(`${APIUrl}/api/edge-main-page?populate=deep,10`),
    fetch(`${APIUrl}/api/edge-leadform?populate=deep,3`),
    fetch(`${APIUrl}/api/topics?populate=deep,10`)
  ]);

  const [perspectivesData, leadFormData, topicsData] = await Promise.all([
    perspectivesRes.json(),
    leadFormRes.json(),
    topicsRes.json()
  ]);

  return {
    props: {
      posts: perspectivesData,
      apiUrl: APIUrl,
      baseUrl:baseUrl,
      leadFormData: leadFormData,
      topics: topicsData,
      perspectivesMetaData: perspectivesData
    },
    revalidate: 120,
  };
}
