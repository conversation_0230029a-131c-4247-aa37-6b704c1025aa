.fold1bg{
    background-image: url('../assets/TealBackground.jpg');
    background-size: cover;
    background-color: #057092;
  }

  .p_text_lg{
    font-size: 18px;
    font-weight: 400;
    line-height: 32.2px;
    color:white; 
}
.equalPadding{
  max-width: 1000px;
}

.faqQuestion{
    font-size: 18px;
    font-weight: 700;
    text-align: left;
}
.faqAnswer{
    background-color: #F9FAFA;
    color: white !important;
    text-align: left;
}

.faqAnswer p {
margin: 0%;
}

.horizontalLine{
border-top:1px solid #7e8181;
}

.faqSection{
font-family: Open Sans; 
color: #057092;
font-size: 24px;
font-weight: 700;
line-height: 44px;
letter-spacing: 0em;
text-align: left;

}

.viewbtn
{
	background-color: #057092 !important;
    border:none;
    padding: 10px 36px 10px 36px;
    font-weight: 600;
    flex-wrap: wrap;
    margin-right: 8px;
    margin-left: 8px;
    border-radius: 0px!important;
}

.whiteBackground{
    background-color: white;
}

.breadCrumb p{
    font-size: 12px;
    color: white;
  }
  
  .breadCrumblast{
    font-size: 12px;
    color: white;
    text-decoration: none !important;
  }
  
  .breadCrumb p:hover {
    font-size: 12px;
    color: white;
    text-decoration: underline;
  }
  
  .hideCrumb{
    max-width: 1000px;
  }
 
  @media screen and (min-width:0px) and (max-width: 499px) {

    .hideCrumb{
      display: none;
    }
  }
  
  @media screen and (min-width:500px) and (max-width: 976px){
  
    .hideCrumb{
      display: none;
    }
  }
   