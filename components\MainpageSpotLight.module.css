/* components/MainpageSpotLight.module.css */
.spotlightContainer {
  width: var(--isb-edge-width-1200);
  margin: 0 auto;
  padding: 30px 0;
  position: relative;
  overflow: hidden;
}

@media (max-width: 1200px) {
  .spotlightContainer {
    width: 100%;
    max-width: 95%;
  }
}

@media (max-width: 768px) {
  .spotlightContainer {
    max-width: 100%;
    padding: 20px 15px;
    overflow: visible;
  }
  
  /* Increasing video container size for smaller screens */
  .playerWrapper, .thumbnailContainer {
    min-height: 260px;
  }
  
  .sliderWrapper {
    overflow: visible;
    width: 100%;
    margin: 0;
    padding: 0;
  }
  
  .slider {
    overflow: visible !important;
    margin: 0;
    padding: 0;
  }

  .sectionTitle {
    font-size: 24px;
    margin-bottom: 10px;
  }

  .sectionTitle::before,
  .sectionTitle::after {
    margin: 0 10px;
  }

  .navButton {
    width: 30px;
    height: 30px;
    font-size: 14px;
    top: 40%;
    opacity: 1;
  }
  
  .prevButton {
    left: 5px;
  }
  
  .nextButton {
    right: 5px;
  }
  
  .videoCard {
    width: 100%;
    padding: 0;
    margin: 0;
  }
  
  .slideItem {
    padding: 0;
    margin: 0;
  }
}

.sectionTitle {
  text-align: center;
  font-family: var(--isb-edge-font-family-Reckless);
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 30px;
  color: #182891;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sectionTitle::before,
.sectionTitle::after {
  content: "";
  display: inline-block;
  flex-grow: 1;
  height: 2px;
  background-color: lightgray;
  margin: 0 20px;
}

/* Topics styles */
.topicsContainer {
  width: 100%;
  margin: 0 auto 30px;
  padding: 0 15px;
}

.topicsWrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.topicButton {
  padding: 8px 16px;
  background-color: #f5f7fa;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-family: var(--isb-edge-font-family-inter);
  font-size: 14px;
  color: #182891;
  cursor: pointer;
  transition: all 0.3s ease;
}

.topicButton:hover {
  background-color: #182891;
  color: white;
  border-color: #182891;
}

.viewAllTopics {
  padding: 8px 16px;
  background-color: transparent;
  border: 1px solid #182891;
  border-radius: 20px;
  font-family: var(--isb-edge-font-family-inter);
  font-size: 14px;
  color: #182891;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.viewAllTopics:hover {
  background-color: #182891;
  color: white;
}

.sliderWrapper {
  position: relative;
  max-width: 100%;
  margin: 0 auto;
  padding-top: 0;
}

.slider {
  width: 100%;
  overflow: visible !important;
}

/* Custom navigation buttons */
.navButton {
  position: absolute;
  top: 36%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff;
  border: none;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
  opacity: 0;
}

.prevButton {
  left: 11.5%;
}

.nextButton {
  right: 11.5%;
}

.sliderWrapper:hover .navButton {
  opacity: 1;
}

/* Rotated chevron for left navigation button */
.rotatedChevron {
  transform: rotate(180deg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.navButton:hover {
  background: #f5f5f5;
}

/* Disabled state for custom navigation buttons */
.arrowDisabled {
  opacity: 0 !important;
  cursor: default !important;
  pointer-events: none !important;
}

.arrowDisabled:hover {
  background: #fff !important;
}

.arrowDisabled, .arrowDisabled:before {
  color: #cccccc !important;
}

/* Video card styling */
.slideItem {
  padding: 0;
}

.videoCard {
  display: flex;
  flex-direction: column;
  background-color: transparent;
  overflow: hidden;
  width: 880px;
  margin: 0 auto;
  position: relative;
}

@media (max-width: 960px) {
  
  .videoCard {
    width: 100%;
    max-width: 880px;
  }
}

.thumbnailContainer {
  position: relative;
  width: 880px;
  height: 501px;
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  padding: 0;
  transform: translateY(-15px);
}

@media (max-width: 960px) {
  .thumbnailContainer {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    margin: 0 auto;
  }

  .videoTitle {
    font-size: 20px;
    line-height: 1.4;
    margin-top: 10px;
  }

  .videoDescription {
    font-size: 14px;
    line-height: 1.5;
  }

  .topicsWrapper {
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
  }

  .slideItem {
    padding: 0;
  }

  .playButton {
    width: 40px;
    height: 40px;
  }

  .playIcon {
    border-width: 10px 0 10px 15px;
  }
}

.playerWrapper {
  width: 880px;
  height: 501px;
  margin: 0 auto;
  background-color: #000;
  position: relative;
  padding: 0;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(-0px);
  cursor: pointer; /* Make entire wrapper clickable */
}

@media (max-width: 960px) {
  .playerWrapper {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    margin: 0;
    padding: 0;
  }
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholderThumbnail {
  width: 100%;
  height: 100%;
  background-color: #d0d0d0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Play button */
.playButtonContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer; /* Make entire container clickable */
}

.playButton {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.playButton:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
}

.playIcon {
  width: 0;
  height: 0;
  border-top: 15px solid transparent;
  border-bottom: 15px solid transparent;
  border-left: 25px solid #182891;
  margin-left: 5px; /* Center the triangle visually */
}

/* Video details */
.videoDetails {
  padding: 20px 0;
  background-color: inherit;
}

.videoTopics {
  margin-bottom: 8px;
}

.videoTopic {
  color: #245BFF;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 400;
  font-family: var(--isb-edge-font-family-inter-variable);
  cursor: pointer;
  transition: color 0.2s ease;
}

.videoTopic:hover {
  color: #2a3ebd;
  text-decoration: none;
}

.videoMeta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.videoCategory {
  color: #182891;
  text-transform: uppercase;
  font-weight: 600;
  font-family: var(--isb-edge-font-family-inter);
}

.videoDuration {
  color: #666;
  font-weight: 400;
  font-family: var(--isb-edge-font-family-inter);
}

.videoTitle {
  font-family: var(--isb-edge-font-family-Reckless);
  font-size: 24px;
  font-weight: 600;
  margin: 8px 0 12px 0;
  line-height: 1.3;
  color: #182891;
}

.videoTitle a {
  color: inherit;
  text-decoration: none;
  color: #000000;
  transition: color 0.2s ease;
  font-family: var(--isb-edge-font-family-Reckless);
  font-size: 26px;
  font-weight: 600;
  line-height: 1.4;
  color: var(--isb-edge-color-primary);
}

.videoTitle a:hover {
  color: #2a3ebd;
}

.videoDescription {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  font-family: var(--isb-edge-font-family-inter);
}

/* Loading and error states */
.loadingContainer, .errorContainer {
  text-align: center;
  padding: 40px 0;
  color: #666;
  font-family: var(--isb-edge-font-family-inter);
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .spotlightContainer {
    width: 95%;
  }
}

@media (max-width: 768px) {
  .slider :global(.slick-slide) {
    padding: 0 !important;
  }
  /* Style default Slick arrows for mobile */
  .slider :global(.slick-prev),
  .slider :global(.slick-next) {
    display: block !important;
    width: 32px;
    height: 32px;
    z-index: 100;
    top: 21%;
    /* background-color: white; */
    border-radius: 50%;
    /* box-shadow: 0 2px 5px rgba(0,0,0,0.2); */
  }
  
  .slider :global(.slick-prev) {
    left: -25px;
    padding-right: 10px;
  }
  
  .slider :global(.slick-next) {
    right: -25px;
    padding-left: 10px;
  }
  
  .slider :global(.slick-prev:before),
  .slider :global(.slick-next:before) {
    color: #182891;
    font-size: 18px;
    /* opacity: 1; */
    line-height: 1;
  }

 
  
  .playButton {
    width: 60px;
    height: 60px;
  }
  
  .videoTitle {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .sectionTitle {
    font-size: 28px;
  }
  
  .sectionTitle::before,
  .sectionTitle::after {
    margin: 0;
  }
  
  .navButton {
    display: none; /* Hide custom arrows on mobile */
  }
  
  .videoTitle a {
    font-size: 22px;
  }
  
  .videoDescription {
    font-size: 15px;
  }
}

@media (max-width: 576px) {
  .sectionTitle {
    font-size: 24px;
  }
  
  .videoTitle a {
    font-size: 18px;
  }

  .videoDescription {
    font-size: 14px;
  }

  .thumbnailContainer {
    aspect-ratio: 16/9;
    margin: 0;
    padding: 0;
    cursor: pointer; /* Make thumbnail container clickable */
  }
  
  .playerWrapper {
    aspect-ratio: 16/9;
    margin: 0;
    padding: 0;
    cursor: pointer; /* Make player wrapper clickable */
  }

  .playButton {
    width: 50px;
    height: 50px;
  }

  .playIcon {
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 16px solid #182891;
  }
  
  .slideItem {
    padding: 0;
    margin: 0;
  }
  
  .videoCard {
    margin: 0;
    padding: 0;
  }
}
