import { useRouter } from "next/router";
import classes from "./index.module.css";
import Moment from "moment";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import { useEffect, useState } from "react";
import CaseLetComponent from "../../../components/CaseLetComponent";
import ProgramPageHeroSection from "../../../components/ProgramPageHeroSection";
import { StickyBanner } from "../../../components/StickyBanner";
import { ProgramDetailsCard } from "../../../components/ProgramDetailsCard";
import CryptoJS from 'crypto-js';

function LearningTracksv2(props) {
  Moment.locale("en");
  const [modalStatus, setModalStatus] = useState(false) 
  const [fullUrl, setFullUrl] = useState('');

  function getModalStat(stat){
   setModalStatus(stat)
  }
  const { query } = useRouter();
  const learnTrackData = props.learningTrackData.data[0].attributes;
  const caselets = learnTrackData.learning_track_caselets.data;
  const programDetails = {download_syllabus_title : learnTrackData.download_syllabus_title, program_name: learnTrackData.learning_track_name, no_of_courses:learnTrackData.no_of_courses, no_of_weeks:learnTrackData.learning_track_weeks}
  const trackFoldData = props.trackFold.data.attributes;
  const courseProf = learnTrackData.professors.data
  const landingPageResponse = props.tracklandingpage.data[0].attributes
  const courseData = learnTrackData.newcourse_trackpages
  const interestedSection = props.interestedData.data;
  const qpms = query.utm_source != undefined
  ? `utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`
      : ``;
  const queQpms = `?${qpms}`;
  const andQpms = `&${qpms}`;
const [mailId, setMailId] = useState("")
const getIsbMail = (mail)=>{
  setMailId(mail)
}
const tags =`utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`;
const URL = `${props.baseURL}${props.isv2 ? `/` : `/v2/`}${props.meetupId}&${tags}`
useEffect(() => {


  if (typeof window !== 'undefined') {
    const cookies = document.cookie
      .split(';')
      .map((cookie) => cookie.trim())
      .reduce((acc, cookie) => {
        const [key, value] = cookie.split('=');
        acc[key] = decodeURIComponent(value);
        return acc;
      }, {});

    const {
      leadform_name,
      leadform_email,
      leadform_mobile,
      leadform_ProgramId,
      leadform_role,
      leadform_country_code,
      leadform_id,
      leadform_location,
      leadform_years_of_experience
    } = cookies;
    const countryCode = localStorage.getItem('countryCode');

    const formData = {
      name: leadform_name,
      email: leadform_email,
      role: leadform_role,
      mobile: leadform_mobile,
      checkBox: true,
      countryCode: leadform_country_code || countryCode,
      programId: leadform_ProgramId,
      leadform_years_of_experience:leadform_years_of_experience,
      leadform_id : leadform_id,
      leadform_location:leadform_location
    };

    const secretKey = process.env.NEXT_PUBLIC_CRYPTO_KEY; // Use a secure secret key
    const encryptedParams = CryptoJS.AES.encrypt(
      JSON.stringify(formData),
      secretKey
    ).toString();

    const allValuesValid = Object.values(formData).every(
      (value) => value !== undefined && value !== 'undefined' && value !== null && value !== ''
    );

    if (allValuesValid) {
      // const params = new URLSearchParams(formData); 
     
      const fullUrlWithParams = `?params=${encodeURIComponent(encryptedParams)}`;
      setFullUrl(fullUrlWithParams);  
    } else {
      setFullUrl('');  
    }

  const decryptParams = (encryptedString) => {
    const bytes = CryptoJS.AES.decrypt(encryptedString, secretKey);
    const decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    return decryptedData;
  };
  const decryptedData = decryptParams(encryptedParams);

}
 
}, []);
return
 
  return (
    <>
      <div>
      <ProgramPageHeroSection
      isv2={props.isv2}
      fullUrl={fullUrl}
      getModalStat={getModalStat}
      landingPageResponse={landingPageResponse}
      classes={classes}
      apiUrl={props.apiUrl}
      learnTrackData={learnTrackData}
      baseURL={props.baseURL}
      baseurl={props.baseurl}
      qpms ={qpms }
      meetupId={props.meetupId}
      utm_source={query.utm_source}
      utm_medium={query.utm_medium}
      utm_campaign={query.utm_campaign}
      utm_term={query.utm_term}
      utm_content={query.utm_content}
      andQpms ={andQpms }
      trackFoldData={trackFoldData}
      />    

        <div className={`row mt-lg-5 mt-3 justify-content-between col-lg-12 mx-auto px-lg-0 px-sm-3 px-md-3 px-3   ${classes.equalPadding}  ${classes.caseletStyle}`}>
        <div className={`col-lg-8 p-0 col-md-12`}>

        <CaseLetComponent
        fullUrl={fullUrl}
        qpms={qpms}
        andQpms={andQpms}
        trackFoldData={trackFoldData}
         baseURL={props.baseURL} 
         lxpStagingUrl={props.lxpStagingUrl}
         lxpProdUrl={props.lxpProdUrl}
          apiUrl={props.apiUrl}
          learnTrackData={learnTrackData}
          classes={classes} 
          caselets={caselets}/>         

        </div>

          <ProgramDetailsCard
          url={URL}
          tags={tags}
            isv2={props.isv2}
            apiUrl={props.apiUrl}
            fullUrl={fullUrl}
            meetupId={props.meetupId}
            mailId={mailId}
            landingPageResponse={landingPageResponse}
            trackFoldData={trackFoldData}
            classes={classes}
            learnTrackData={learnTrackData}
            baseURL={props.baseURL}
            qpms={qpms}
            andQpms={andQpms}
            utm_source={query.utm_source}
            utm_medium={query.utm_medium}
            utm_campaign={query.utm_campaign}
            utm_term={query.utm_term}
            utm_content={query.utm_content}
          />        
        </div>

   {/* Sticky header with permalink tabs start */}
  {/* {scrolled&&  */}
  <StickyBanner
  query={query}
  isv2={props.isv2}
  getModalStat={getModalStat}
  interestedSection={interestedSection}
  fullUrl={fullUrl}
   landingPageResponse={landingPageResponse}
   getIsbMail={getIsbMail}
    classes={classes}
    modalStatus={modalStatus}
    learnTrackData={learnTrackData}
    andQpms={andQpms}
    qpms={qpms}
    baseURL={props.baseURL}
    apiUrl={props.apiUrl}
    trackFoldData={trackFoldData}
    courseData={courseData}
    queQpms={queQpms}
    programDetails={programDetails}
    courseProf={courseProf}
      meetupId={props.meetupId}
      utm_source={query.utm_source}
      utm_medium={query.utm_medium}
      utm_campaign={query.utm_campaign}
      utm_term={query.utm_term}
      utm_content={query.utm_content}
   />
   {/* } */}
            {/* Sticky header with permalink tabs end */}
      </div>
    </>
  );
}
export default LearningTracksv2;

export const getStaticPaths = async (context) => {
  const APIUrl = process.env.API_BASE_URL;
  const res = await fetch(`${APIUrl}/api/new-track-pages`);
  const response = await res.json();
  const paths = response.data.map((learningTrack) => {
    return {
      params: {
        learning_track_id:
          learningTrack.attributes.learning_track_id.toString(),
      },
    };
  });
  return {
    paths,
    fallback: false,
  };
};


export async function getStaticProps(context) {
  const meetupId = context.params.learning_track_id;
  const APIUrl = process.env.API_BASE_URL;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
  const lxpStagingUrl = process.env.ISBO_STAGING_URL;
  const lxpProdUrl = process.env.ISBO_PROD_URL;
  
  const [
    tracklandingpage,
    learningTrack,
    trackFold,
    interestedLearningTracks
  ] = await Promise.all([
    fetch(`${APIUrl}/api/landing-pagev2s?filters[learning_track_id][$eq]=${meetupId}&populate=deep,5`).then((r) => r.json()),
    fetch(`${APIUrl}/api/new-track-pages?filters[learning_track_id][$eq]=${meetupId}&populate=deep,10`).then((r) => r.json()),
    fetch(`${APIUrl}/api/new-track-page-fold?populate=*`).then((r) => r.json()),
    fetch(`${APIUrl}/api/new-track-pages?filters[learning_track_id][$ne]=${meetupId}&populate[learning_track_short_image][populate]=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      learningTrackData: learningTrack,
      trackFold: trackFold,
      tracklandingpage:tracklandingpage,
      interestedData :interestedLearningTracks,

      meetupId:meetupId,  
      apiUrl: APIUrl,
      baseURL: baseUrl,
      lxpStagingUrl: lxpStagingUrl,
      lxpProdUrl: lxpProdUrl,
    },
    revalidate: 240,
  };
}