import Link from "next/link";
import Image from "next/image";
import Nav from "react-bootstrap/Nav";
import Navbar from "react-bootstrap/Navbar";
import appLogo from "../../../../assets/ISB_Online Logo.png";
import classes from "../index.module.css";
import classes1 from "../../../finance.module.css"
import React, { useMemo, useState } from "react";
import Group from "../../../../assets/New Online Documents.svg";
import projectduration from "../../../../assets/New project duration.svg";
import RupessDocuments from "../../../../assets/New Rupees Documents.svg";
import verfiedReport from "../../../../assets/New Verified Report.svg";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useRef, useEffect } from "react";
import useCollapse from "react-collapsed";
import { useRouter } from "next/router";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import "react-phone-input-2/lib/style.css";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { NextSeo } from "next-seo";
import group from "../../../../assets/Group_2688.png";
import playIcon from "../../../../assets/play_icon.png";
import dynamic from "next/dynamic";
import { KeyConcepts } from "../../../../components/KeyConcepts";
import { FacultySlider } from "../../../../components/FacultySlider";
import { LeadForm } from "../../../../components/LeadForm";
import { Whowillbenefit } from "../../../../components/Whowillbenefit";
import { GetLocation } from "../../../../components/GeoLocation";
import { StoriesSlider } from "../../../../components/StoriesSlider";
import HighlightsSection from "../../../../components/HighlightsSection";
import LandingPageHeroText from "../../../../components/LandingPageHeroText";
import LpEligibilityBanner from "../../../../components/LpEligibilityBanner";
import TrackPageBottomFold from "../../../../components/TrackPageBottomFold";
import { WhatYouGain } from "../../../../components/WhatYouGain";
import { ThankYouSection } from "../../../../components/ThankYouSection";
import { IsbAdvantage } from "../../../../components/IsbAdvantage";
import Card from "../../../../components/Card";
import CardContainer from "../../../../components/CardContainer";
import banner from "../../../../assets/footer_banner.jpeg";
import { AiComponent } from "../../../../components/AiComponent";
import { VideoSection } from "../index";
import { BrochureFileNameFormatter } from "../../../../utils";

const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });


export default function ThankYouPage(props) {
  const router = useRouter();
  const { query } = router;
  const qpms =
  query.utm_source != undefined
    ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`
    : ``;
    const andQpms = `&${qpms}`;
    const landingPageResponse = props.apiData.data[0].attributes;
    const bannerFullData = props.stickyBanner.data[0].attributes
  
  
    const [thankyouScreen,setThankyouScreen] = useState(false)
    
      const learnTrackData= landingPageResponse.new_track_page.data.attributes
    const [currentLocation, setCurrentLocation] = useState({ city:'',  state: '', country: ''});
    const trackFoldData = props.trackFold.data.attributes;
    const { showModal } = router.query;

    useEffect(() => {
      if (showModal !== 'true') {
        router.replace(`/lpv2/${props.meetupId}`); 
      }
    
    }, [showModal, props.meetupId]);
    



    useEffect(() => {
      const fetchLocation = async () => {
        try {
          const loc = await GetLocation();
          setCurrentLocation(loc);
      } catch (error) {
        console.error(error);
      }
    };
    fetchLocation();
    setThankyouScreen(true)
  }, [ ])
      const pdfsArray = useMemo(()=>[
      {
          url: "../../Axis Bank.pdf",
          fileName: "Axis Bank.pdf"
      },
      {
          url: "../../Bank of Baroda Education Loan for ISB.pdf",
          fileName: "Bank of Baroda Education Loan for ISB.pdf"
      },
      {
          url: "../../HDFC Credila.pdf",
          fileName: "HDFC Credila.pdf"
      },
      {
          url: "../../idfc.pdf",
          fileName: "idfc.pdf"
      },
      {
          url: "../../Liquiloans-finance-ISB-Oct2022.pdf",
          fileName: "Liquiloans-finance-ISB-Oct2022.pdf"
      },
      {
          url: "../Propelld Financing Option.pdf",
          fileName: "Propelld Financing Option.pdf"
      },
  ],[])
  
  const API_Key = process.env.NEXT_PUBLIC_ADOBE_CLIENTID;
  useEffect(() => {
    // let hostName = window.location.hostname
    const script = document.createElement('script');
    script.src = 'https://acrobatservices.adobe.com/view-sdk/viewer.js';
    script.async = true;
    document.body.appendChild(script);
  
    script.onload = () => {
        document.addEventListener('adobe_dc_view_sdk.ready', function() {
          pdfsArray.forEach(pdf => {
                const adobeDCView = new AdobeDC.View({
                  clientId: API_Key,
                    divId: "adobe-dc-view-" + pdf.fileName  
                });
                adobeDCView.previewFile({
                    
                    content: { location: { url: `${pdf.url}` } },
                    metaData: { fileName: pdf.fileName }
                }, { embedMode: "SIZED_CONTAINER" });
            });
        });
    };
  
    return () => {
        document.body.removeChild(script);
    };
  }, [API_Key, pdfsArray]);  
  
  
  let selectedIndices = []
  
  for (let i = 1; i < pdfsArray?.length; i += 3) {
   selectedIndices.push(i);
  }
  
 
  
    const [showIframe, setShowIframe] = useState(false);
  
    const handleImageClick1 = () => {
      if(landingPageResponse?.Iframe_fold?.Iframe_url){
        setAutoplayMobile(true);
        setShowIframe(true);
      }else{
        return null;
      }
    };
  
  
    const [isImageHidden, setIsImageHidden] = useState(false);
    const [autoplay, setAutoplay] = useState(false);
    const [autoplayMobile, setAutoplayMobile] = useState(false);
  
  
    const handleImageClick = () => {
      if(landingPageResponse?.Iframe_fold?.Iframe_url){
        setIsImageHidden(true);
        setAutoplay(true);
      }else{
        return null;
      }
    };
  
  
  
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [showFinancOptionsModal, setShowFinancOptionsModal] = useState(false);
  
    const handleClosePaymentModal = () => {
      document.getElementById("modal_content").scrollTop = 0;
      document.documentElement.style.overflow="unset"
      document.documentElement.style.paddingRight="unset"
      setShowPaymentModal(false)
      setShowFinancOptionsModal(false)
    };
    const handleShowPaymentModal = (e) => {
      document.getElementById("modal_content").scrollTop = 0;
      document.documentElement.style.overflow="hidden"
      document.documentElement.style.paddingRight="17px"
      const id= e.target.id
      if(id==="payment_modal"){
        setShowPaymentModal(true);
      }else if(id==="financing_options"){
        setShowFinancOptionsModal(true)
      }
     
    }
  
  
    
    useEffect(() => {
      if (window.location.hash === '#thankyou') {
        router.replace(window.location.pathname, undefined, { shallow: true });
      }
    }, []);
  
    
  
    const [showButton, setShowButton] = useState(false);
  
    useEffect(() => {
      window.addEventListener("scroll", () => {
        if (window.pageYOffset > 300) {
          setShowButton(true);
        } else {
          setShowButton(false);
        }
      });
    }, []);
  
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: "smooth", // for smoothly scrolling
      });
    };
  
    const C = (
      <FontAwesomeIcon
        icon={faMinus}
        className="fa-solid fa-minus"
        style={{ color: "#7C8495" }}
      />
    );
    const E = (
      <FontAwesomeIcon
        icon={faPlus}
        className="fa-solid fa-plus"
        style={{ color: "#7C8495" }}
      />
    );
  
  
    const Collapse = ({ index, ...data }) => {
      
      const { getCollapseProps, getToggleProps, isExpanded } = useCollapse();
  
      return (
        <>
          <div>
            <h5
              className={`${classes.faqQuestion} d-flex py-2 justify-content-between text-black m-0 mb-2`}
              {...getToggleProps()}
            >
              {`Course ${index+1}: ${data.attributes.course_title}`}
  
              {isExpanded ? C : E}
            </h5>
            <div {...getCollapseProps()}>
              <div dangerouslySetInnerHTML={{ __html: data.attributes.landingpage_course_description }}></div>
            </div>
          </div>
          <div className={`mb-3 p-0 ${classes.foottitle}`}></div>
        </>
      );
    };
  
   
  
    const handleDownload = async () => {
      const meetupId = props.apiData.data[0].attributes.new_track_page.data.attributes.learning_track_id
      try {
        const response = await fetch(
          props.apiUrl + landingPageResponse.brochure.data.attributes.url
        );
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        const formattedName = BrochureFileNameFormatter(meetupId);
        link.download = `${formattedName +".pdf"}`;
        link.click();
      } catch (error) {
        console.error("Error downloading file:", error);
      }
    };
    
  
    const scrollToOverview = (target) => {
      const scrollTarget = document.getElementById("overview");
      if (scrollTarget) {
        let offset = 50.0; // Default offset for desktop view
  
        if (window.innerWidth <= 768) {
          offset = 0; // Offset for mobile view
        }
  
        const scrollPosition =
          scrollTarget.getBoundingClientRect().top + window.pageYOffset;
        const adjustedPosition = scrollPosition - offset;
  
        window.scrollTo({
          top: adjustedPosition,
          behavior: "smooth", // Adjust scrolling behavior (e.g., smooth, auto)
        });
      }
    };
    const scrollToHighlights = (target) => {
      const scrollTarget = document.getElementById("Highlights");
      if (scrollTarget) {
        let offset = 0; 
  
        if (window.innerWidth <= 768) {
          offset = 0; 
        }
  
        const scrollPosition =
          scrollTarget.getBoundingClientRect().top + window.pageYOffset;
        const adjustedPosition = scrollPosition - offset;
  
        window.scrollTo({
          top: adjustedPosition,
          behavior: "smooth", 
        });
      }
    };
    const scrollToFaculty = (target) => {
      const scrollTarget = document.getElementById("Faculty");
      if (scrollTarget) {
        let offset = 0; // Default offset for desktop view
  
        if (window.innerWidth <= 768) {
          offset = 0; // Offset for mobile view
        }
  
        const scrollPosition =
          scrollTarget.getBoundingClientRect().top + window.pageYOffset;
        const adjustedPosition = scrollPosition - offset;
  
        window.scrollTo({
          top: adjustedPosition,
          behavior: "smooth", // Adjust scrolling behavior (e.g., smooth, auto)
        });
      }
    };
    const scrollToEligibility = (target) => {
      const scrollTarget = document.getElementById("Eligibility");
      if (scrollTarget) {
        let offset = 0; // Default offset for desktop view
  
        if (window.innerWidth <= 768) {
          offset = 0; // Offset for mobile view
        }
  
        const scrollPosition =
          scrollTarget.getBoundingClientRect().top + window.pageYOffset;
        const adjustedPosition = scrollPosition - offset;
  
        window.scrollTo({
          top: adjustedPosition,
          behavior: "smooth", 
        });
      }
    };
   
    const [showSticky, setShowSticky] = useState(false);
    const scrollDivRef = useRef(null);
    const [scrolled, setScrolled] = useState(false);
    useEffect(() => {
      const handleScroll = () => {
        if(window.scrollY>100){
          setScrolled(true)
        }
        if (scrollDivRef.current) {
          const div = scrollDivRef.current;
          const rect = div.getBoundingClientRect();
  
          if (rect.top < 0) {
            setShowSticky(true);
          } else {
            setShowSticky(false);
          }
        }
      };
  
      window.addEventListener("scroll", handleScroll);
      return () => window.removeEventListener("scroll", handleScroll);
    }, []);
  
    const professors = landingPageResponse.professors.data;
  
  
  
  const containerMargin = 'container mt-2 px-0'
   const bannerImagestyle = {
    filter:"brightness(100%)"
   }
   if (showModal !== 'true') {
    return null;
  }

  let cards = [];

 if (landingPageResponse.alumni_status_cards) {
   cards = [
     {
       content: landingPageResponse.alumni_status_cards[0]?.description,
       backgroundImage: `${props.apiUrl}${landingPageResponse.alumni_status_cards[0]?.image?.data?.attributes?.formats?.thumbnail?.url || ''}`,
     },
     {
       content: landingPageResponse.alumni_status_cards[1]?.description,
       backgroundImage: `${props.apiUrl}${landingPageResponse.alumni_status_cards[1]?.image?.data?.attributes?.formats?.thumbnail?.url || ''}`,
     },
     {
       content: landingPageResponse.alumni_status_cards[2]?.description,
       backgroundImage: `${props.apiUrl}${landingPageResponse.alumni_status_cards[2]?.image?.data?.attributes?.formats?.thumbnail?.url || ''}`,
     },
   ];
 }

  const settings1 = {
    dots: true, // Display carousel indicators
    infinite: true,
    speed: 400,
    autoplay: true,
    autoplaySpeed: 8000,
    slidesToShow: 3, // Display 3 slides on larger screens
    slidesToScroll: 1,
    arrows: false,
    responsive: [
      {
        breakpoint: 768, // Adjust this breakpoint for mobile devices
        settings: {
          slidesToShow: 1, // Display 1 slide on smaller screens
        },
      },
    ],
  };
    return (
      <>
        <NextSeo
          // title="Certificate Masters Programme in Management Essentials"
          title={landingPageResponse.meta.title}
          canonical={`${process.env.NEXT_PUBLIC_BASE_URL}` + router.asPath}
          noindex = {true}
          nofollow = {true}
          // description="Make informed decisions, inspire your teams, and confidently manage projects by discovering modern management principles' power."
          description={landingPageResponse.meta.description}
          openGraph={{
            type: `website`,
            url: `${process.env.NEXT_PUBLIC_BASE_URL}` + router.asPath,
            title: `${landingPageResponse.meta.title}`,
  
            description: `${landingPageResponse.meta.description}`,
            locale: "en",
            images: [
              {
                url: `${
                  props.apiUrl +
                  landingPageResponse.meta.image.data.attributes.url
                }`,
                alt: "image.png",
              },
            ],
            site_name: `ISB Online`,
          }}
          twitter={{
            handle: "@handle",
            site: "@site",
            cardType: `${landingPageResponse.meta.title}`,
          }}
          additionalMetaTags={[
            {
              name: "keywords",
              content: `${landingPageResponse.meta.keywords}`,
            },
            {
              name: "robots",
              content: `${landingPageResponse.meta.robots}`,
            },
          ]}
        />
  
        <Navbar
          // sticky="top"
          className={`${classes.navelems} px-4`}
          bg="white"
          expand="lg"
          id="myNavbar"
        >
          <Navbar.Brand>
            <Image width={170} src={appLogo} alt="ISB Logo" />
          </Navbar.Brand>
  
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse
            className="d-base-flex justify-content-end"
            id="basic-navbar-nav"
          >
            <Nav className="text-right" id="myNavItem">
              <Nav.Link as="span">
                <a onClick={scrollToOverview} className={classes.para} href="#">
                  Overview
                </a>
              </Nav.Link>
  
              <Nav.Link as="span">
                <a onClick={scrollToHighlights} className={classes.para} href="#">
                  Highlights
                </a>
              </Nav.Link>
  
              <Nav.Link as="span">
                <a onClick={scrollToFaculty} className={classes.para} href="#">
                  Faculty
                </a>
              </Nav.Link>
  
              <Nav.Link as="span">
                <a
                  onClick={scrollToEligibility}
                  className={classes.para}
                  href="#"
                >
                  Eligibility
                </a>
              </Nav.Link>
  
              <DownloadBrocureBtn thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse}/>
  
            </Nav>
          </Navbar.Collapse>
        </Navbar>
  
  {/* fold one div */}
        <div
          className={`${classes.fold1bg} ${classes.singupbg} `}
           
        >
          <Image
              src={`${props.apiUrl}${landingPageResponse.hero_image_desktop.data.attributes.url}`}
              alt="DesktopheroImage"
              fill="1000vw"
              priority={true}
              style={bannerImagestyle}
            />
      
          <div className={`row py-lg-5 p-0 ${classes.equalPadding} mx-auto m-0`} style={{ position: "relative", zIndex: 1 }}>
            <div
              className={`${classes.mobileFoldbg} col-lg-6 col-md-12 py-lg-0 py-md-4 py-sm-4 py-3 px-0 d-flex flex-column justify-content-center`}
            style={{minHeight:"630px"}}
            >
           <LandingPageHeroText
           title={landingPageResponse.title}
           classes={classes}
           rating={landingPageResponse.program_rating}
           starText={landingPageResponse.based_on_participant}
           starColor={"orange"}
           />
            </div>
             
             <ThankYouSection 
             andQpms={andQpms} qpms={qpms}
             baseURL={props.baseurl} landingpageflag={true} learnTrackData={learnTrackData} trackFoldData={trackFoldData} landingPageResponse={landingPageResponse} apiUrl={props.apiUrl} handleDownload={handleDownload} meetupId={props.meetupId}/>
          </div>
        </div>
         
  
  {/* fold one for mobile div */}
        <div
          className={`${classes.equalPadding} position-relative row mx-auto ${classes.showMobileFold}`}
        >
         
          <div className="position-relative text-center px-0">
            <Image
              height={0}
              width={0}
              priority={true}
              sizes="100vw"
              className="col-12 img-fluid p-0"
              alt="heroImage"
              style={bannerImagestyle}
              src={
                props.apiUrl +
                landingPageResponse.hero_image_mobile.data.attributes.url
              }
            ></Image>
              <div
                className={`${classes.mobileimgcontainer} px-0`}
              >
             <LandingPageHeroText
             title={landingPageResponse.title}
             classes={classes}
             rating={landingPageResponse.program_rating}
             starText={landingPageResponse.based_on_participant}
             starColor={"orange"}
             isMobile={true}
             />
              </div>
            
          </div>
  
  {/* mobile lead form div */}
            
          <div className="col-12 bg-white">
            <div className="py-4 ">
              <div>
                <p className={`p-0 m-0 mb-2  text-md-start text-center ${classes.blueBoldText}`}>
                  {landingPageResponse.signup_form.signup_title}
                </p>
                
                  <div className="border px-2 py-3">
                    <ThankYouSection learnTrackData={learnTrackData} trackFoldData={trackFoldData} landingPageResponse={landingPageResponse} apiUrl={props.apiUrl} handleDownload={handleDownload} meetupId={props.meetupId}/>
                  </div>
              </div>
            </div>
          </div>
        </div>
  {/* fold two  div */}
        
  
          <div id="Eligibility" className={`${classes.back_to_top} bg-white`}>
            <LpEligibilityBanner
              lpthankyoupageflag={true}
              landingPageResponse={landingPageResponse}
              classes={classes}
              Group={Group}
              projectduration={projectduration}
              RupessDocuments={RupessDocuments}
              handleShowPaymentModal={handleShowPaymentModal}
              verfiedReport={verfiedReport}
            />
          </div>
      
          <div ref={scrollDivRef}></div>
          {showSticky && (
          <div  className={`${classes.hideButton} ${classes.lp_sticky_bar}`}>
            <div className={`${classes.equalPadding} mx-auto`}>
              <div className="row py-2 py-md-3 py-lg-2 px-lg-0 px-5 px-md-0">
                <div className={`col-lg-3 col-md-6 col-12  ${classes.rightBorder}`}>
                  <div className={`row ${classes.small_img}`}>
                    <div className={`col-lg-4 col-3 text-end`}>
                      <Image src={Group} alt="Card" width="0" height="0" />
                    </div>
                    <div className={`col-lg-8 col-9  p-0`}>
                      <p className={`p-0 m-0 ${classes.head_line}`}>
                        {" "}
                        {landingPageResponse.lt_data.start_title}{" "}
                      </p>
                      <div className={``}>
                        <p className={`p-0 m-0 ${classes.date_line}`}>
                          {" "}
                          {landingPageResponse.lt_data.start_date}
                        </p>
                        <p className={`p-0 m-0 ${classes.last_line}`}>
                           {landingPageResponse.lt_data.last_date_apply_title}{" "}
                          {landingPageResponse.lt_data.end_date}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className={`col-lg-3 col-md-6 col-12  ${classes.rightBorder}`}
                >
                  <div className={`row`}>
                    <div className="col-lg-4 col-3 text-end">
                      <Image
                        src={projectduration}
                        alt="Card"
                        width="0"
                        height="0"
                        // sizes="100vw"
                      />
                    </div>
                    <div className={`col-lg-8 col-9 p-0`}>
                      <p className={`p-0 m-0 ${classes.head_line}`}>
                        {landingPageResponse.lt_data.duration_title}
                      </p>
                      <div className={``}>
                        <p className={`p-0 m-0 ${classes.date_line}`}>
                          {landingPageResponse.lt_data.duration_in_weeks} Weeks,
                          Online
                        </p>
                        <p className={`p-0 m-0 ${classes.date_line}`}>
                          {landingPageResponse.lt_data.duration_in_hrs} hours per
                          week
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className={`col-lg-3 col-12 col-md-6  ${classes.rightBorder}`}
                >
                  <div className={`row`}>
                    <div className="col-lg-4 col-3 text-end">
                      <Image
                        src={verfiedReport}
                        alt="Card"
                        width="0"
                        height="0"
                        
                      />
                    </div>
                    <div className={`col-lg-8 col-9 p-0 pe-2`}>
                      <p className={`p-0 m-0 ${classes.head_line}`}>
                        {landingPageResponse.lt_data.fee_title}
                      </p>
                      <div className={``}>
                        <p className={`p-0 m-0 ${classes.date_line}`}>
                          {" "}
                          INR {landingPageResponse.lt_data.fee}
                        </p>
                        <p className={`p-0 m-0 ${classes.last_line}`}>
                        {/* View payment plan */}
                        <FinanceLinks handleShowPaymentModal={handleShowPaymentModal} landingPageResponse={landingPageResponse}/>
                      </p>
                      </div>
                    </div>
                  
                  </div>
                </div>
                <div className={`col-lg-3 col-12 col-md-6 align-center ${classes.centerAlignText}`}>
                <DownloadBrocureBtn thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse}/>
  
                </div>
              </div>
            </div>
          </div>
        )}
  
  {/*New_fold  div */}
  
  <div id="overview"  className={`${classes.light_bg} ${classes.lpv2videosection} p-0 `}>
  
  <h2 className={classes.blueHeading}>{landingPageResponse.program_guidance_title}</h2>
  <VideoSection 
      autoplayMobile={autoplayMobile}
      bannerFullData={bannerFullData}
      autoplay={autoplay} 
      handleImageClick={handleImageClick} 
      isImageHidden={isImageHidden} 
      apiUrl={props.apiUrl} 
      landingPageResponse={landingPageResponse} 
      handleImageClick1={handleImageClick1} 
      showIframe={showIframe}
      learnTrackData={learnTrackData}
      />
        </div>
         
      <div className="bg-white pb-lg-5 pb-lg-4">
        <Whowillbenefit
          apiUrl={props.apiUrl}
          bannerFullData={bannerFullData}
          learnTrackData={learnTrackData}
          bgColor={"lpv2"}
        />
      </div>
  
          <div id="Highlights" className={`${classes.whitebackground} p-0 m-0`}>
            <HighlightsSection
           classes={classes}
           apiUrl={props.apiUrl}
           data={landingPageResponse.new_highlightcards}
         title={landingPageResponse.new_highlight_title}
           />
            <div className={`col-lg-12 col-md-12 col-12 p-0  mx-auto ${classes.equalPadding}`}
            >
            </div>
          </div>

          <div>
         {landingPageResponse.AI_Component?.ai_image_component?.length>0 && <AiComponent componentFlag={true} apiUrl={props.apiUrl} landingPageResponse={landingPageResponse}/>}


        </div>
  
  {/* fold Programme Courses  div */}
  
        <div className="px-2 bg-white">
          <div
            className={`col-lg-12 col-md-12 col-12 mx-auto p-2 ${classes.equalPadding}`}>
            <h2 className={`text-center ${classes.main_blue_head} py-4 m-0`}>
              {landingPageResponse.programme_course_title}
            </h2>
  
            {landingPageResponse.new_track_page.data.attributes.newcourse_trackpages.data.map(
              (item, i) => {
                return <Collapse key={item.id} index={i} {...item}  />;
              }
            )}
  
            <p className={`px-2 px-lg-0  ${classes.bdc_note_mobile}`} style={{fontStyle:"italic",fontFamily: "sans-serif"}}>{landingPageResponse.syllabus_note}</p>
          </div>
          <div className={`d-flex justify-content-center bg-white ${learnTrackData.story_fold?.length ===0 ? "pb-5":"pb-3"}`}>
          <DownloadBrocureBtn thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse}/>
          </div>
        </div>
  
         
       {learnTrackData.story_fold?.length>0 ? <div className={`px-4`}>
          <StoriesSlider
            apiUrl={props.apiUrl}
            storyData={learnTrackData.story_fold}
            title={bannerFullData.story_fold_title}
            default_settings={true}
            lpflag={true}
          />
        </div> :<div style={{width:"100%", border:"1px solid transparent"}}></div> }
  
        <div className=" bg-white">
          <div className={`${classes.equalPadding} m-auto`}>
            <div id="Faculty" className={`${classes} bg-white p-0 pb-4`} style={{ position: "relative" }}>
              <FacultySlider
                title={bannerFullData.faculty_fold_title}
                courseProf={professors}
                apiUrl={props.apiUrl}
                flag={true}
              />
            </div>
          </div>
        </div>
  
  
        { <>
          <div className={`${classes.equalPadding} ${containerMargin} px-0`}>
            <WhatYouGain
            bdcnote={landingPageResponse.bdc_note}
              classes={classes}
              title={landingPageResponse.bdc_title}
              certificateData={landingPageResponse.certificate}
              flag={true}
              apiUrl={props.apiUrl}
  
            />
          </div>
  
          {/* <HighlightsSection
            classes={classes}
            apiUrl={props.apiUrl}
            data={landingPageResponse.why_isb_online}
            title={landingPageResponse.why_isb_online_title}
            flag={true}
  
          /> */}

          {landingPageResponse.alumni_status_cards?.length>0 &&  
          <div className={`${classes.light_bg} p-0 `}>
                      <div className={`mx-auto ${classes.equalPadding}  py-4`}>
                        <div className={` m-0 py-3`}>
                          <div  className={`text-center`}>
      
                            <h2 className={`text-center ${classes.main_blue_head}`}>{landingPageResponse.alumni_status_title}</h2>
                          </div>
                          <div className={`${classes.first_div}  `}>
                        <div className={`row px-4`}>
                          <Slider {...settings1}>
                            {landingPageResponse.alumni_status_cards.slice(0, 3).map((card, i) => (
                            <div key={i} >
                              <Card content={card.description} backgroundImage={`${props.apiUrl}${card.image?.data?.attributes?.formats?.thumbnail?.url}`} />
                            </div>
                            ))}
                          </Slider>
                    </div>
                    </div>
                  <div className={`${classes.second_div} px-md-4 px-0 `}>
                    <CardContainer cards={cards} />
      
                  </div>
                        </div>
                        {/* <div className=" d-flex justify-content-center">
                          <button
                            onClick={scrollToTop}
                            type="button"
                            className={`${classes.land_btn} btn btn-primary my-3 `}
                          >
      
                            Download Brochure
                          </button>
                        </div> */}
                      </div>
                     
          </div>
      }
  
          <IsbAdvantage landingPageResponse={landingPageResponse} classes={classes} />
          {/* <div className=" d-flex justify-content-center py-3 bg-white">
            <DownloadBrocureBtn mobile={true} thankyouScreen={thankyouScreen} handleDownload={handleDownload} landingPageResponse={landingPageResponse} />
  
          </div> */}
          <Image
                    className="img-fluid w-100 mt-4"
                    src={banner}
                    alt="banner_image"
                  />
  
          <TrackPageBottomFold apiUrl={props.apiUrl} />
          <div className={`mt-5 ${classes.showBanner}`}></div>
        </>}
         {/*payment modal */}
        <PaymentModal landingPageResponse={landingPageResponse}  handleClosePaymentModal={handleClosePaymentModal} showPaymentModal={showPaymentModal} />
        <FinancingOptionModal pdfs={pdfsArray} handleClosePaymentModal={handleClosePaymentModal} showFinancOptionsModal={showFinancOptionsModal} selectedIndices={selectedIndices}/>
      </>
    );
  }

export const getStaticPaths = async (context) => {

  const APIUrl = process.env.API_BASE_URL;

  const res = await fetch(`${APIUrl}/api/landing-pagev2s`);
  const response = await res.json();
 
  const paths = response.data.map((learningTrack) => {
   
    return {
      params: {
        learning_track_id:
          learningTrack.attributes.learning_track_id.toString(),
      },
    };
  });
  return {
    paths,
    fallback: false,
  };
};

export async function getStaticProps(context) {
  const meetupId = context.params.learning_track_id;

  const APIUrl = process.env.API_BASE_URL;
  const Baseurl = process.env.NEXT_PUBLIC_BASE_URL;

  const [
    stickyBanner,
    tracklandingpage, 
    trackFold
   
  ] = await Promise.all([
    fetch(`${APIUrl}/api/sticky-banners?populate=deep,4`).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/landing-pagev2s?filters[learning_track_id][$eq]=${meetupId}&populate=deep,5`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/new-track-page-fold?populate[apply_to_lt_title][populate]=*`).then((r) => r.json()),

     
  ]);
  return {
    props: {
      stickyBanner: stickyBanner,
      apiData: tracklandingpage,
      trackFold: trackFold,
      apiUrl: APIUrl,
      
      baseurl: Baseurl,
      meetupId:
        tracklandingpage.data[0].attributes.new_track_page.data.attributes
          .learning_track_id,
    },
    revalidate: 120,
  };
}



export const PaymentModal=({landingPageResponse, handleClosePaymentModal, showPaymentModal})=>{
  return(
    <div id="myModal" className={`${classes.fin_modal} d-${showPaymentModal ? "flex":"none"}`}>
        <div  className={`${classes.modal_content}`} style={{maxWidth:showPaymentModal?"900px":""}}>
       
       <div className={classes.payment_plan_div}>
        <div className="d-flex justify-content-between">
        
          <h2 className=" ">{landingPageResponse.Payment_plan_title}</h2>
              <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
        </div>
            <div className={classes.paymodal_body}>
              <div className={` `}>
                <div className={`${classes.programfee}`}>{landingPageResponse.Programme_fee_title}</div>
                <div className={classes.admin_price}>{landingPageResponse.Programme_fee}</div>
                <div className={classes.gst_text}>{landingPageResponse.Payment_plan_description}</div>

                {landingPageResponse.payment_plan.map((pay, index) =>{
                  return (
                    <div key={index} className={classes.payment_table_wrpr}>
                      <h6>{pay.PaymentScheduleTitle}</h6>
                      <table className={`${classes.table_style} table`}>
                        <thead>
                          <tr>
                            <th scope="col" className={` ${classes.bg_gray}`}>
                              {pay.payment_date_title}
                            </th>
                            <th scope="col" className={` ${classes.bg_gray}`}>
                              {pay.amount_due_title}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                        {pay.Particulars.map((par, index)=>{
                          return(
                            <tr key={index}>
                            <td>{par.Date}</td>
                            <td>{par.Fee}</td>
                          </tr> )}) }
                        </tbody>
                      </table>
                    </div>
                  )
                })
                }

              </div>
            </div>
      </div> 

        </div>

      </div>
  )
}

const FinancingOptionModal=({pdfs,showFinancOptionsModal,selectedIndices, handleClosePaymentModal})=>{
  return(
    <div id="myModal" className={`${classes.fin_modal} d-${showFinancOptionsModal ? "flex":"none"}`} >
    <div  className={classes.modal_content}>
   
  <div className="financing_optns_div">
  <div className="d-flex justify-content-between">
      <h2 >Financing Options</h2>
          <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
    </div>
        <div id="modal_content" className={classes.paymodal_body}>
        <div className={`${classes.contentext} container-md pt-4 pb-3 pb-md-4`}>
            <div className={`${classes.contentext_child} mx-auto`} >
                <p>
                We have partnered with multiple lenders to aid participants, as listed below. You can find details of the financing options in the documents listed here; select ‘download this file’ to access them. You may reach out to the respective contacts mentioned in the documents for the next steps in availing the loans.
                </p>
                
            </div>
                <div className={"container-md p-0 m-0 d-flex justify-content-center"}>
                    <div className="" style={{maxWidth:"1100px"}}>

                        <div className={` `}>
                            <div className={`${classes1.iframe_main}`}>
                                 

                                <div className='row mx-auto d-flex justify-content-between ' style={{maxWidth:"1000px", minHeight:"80vh"}}>
            
            {pdfs.map((pdf, index) => (
                <div key={index} className={`${selectedIndices.includes(index) ?  "px-2" : "px-0"} pt-md-4 pb-4 ${index==0 ? "pt-0":""} pb-0 col-md-4 col-sm-6 `} style={{minWidth:"300px"}}>
                <div className={`${classes1.pdf_viewer_main}`} id={"adobe-dc-view-" + pdf.fileName} style={{ width: '100%'}}></div>

                </div>
            ))}
        </div>


                            </div>
                        </div>
                    </div>
                </div>
                <div className="finance_contentext_child__nwUCn mx-auto"><p className="mb-0">For any further clarifications, you may write to us at <a href="mailto:<EMAIL>" className='text-decoration-none'><EMAIL>.</a></p></div>
            </div>

        </div>
  </div> 

    </div>

  </div>
  )
}

export const FinanceLinks=({handleShowPaymentModal, landingPageResponse})=>{
return(
  <>
     <a className={`${classes.financing_options_link}`} id={"payment_modal"}  onClick={(e) => handleShowPaymentModal(e)}>{landingPageResponse.lt_data.view_paymet_plan}</a><br/>
      <a className={`${classes.financing_options_link}`} id={"financing_options"}  onClick={(e) => handleShowPaymentModal(e)}>{landingPageResponse.lt_data.enterprise_pricing}</a>
  </>
)
}
const DownloadBrocureBtn=({thankyouScreen,handleDownload,landingPageResponse, mobile})=>{
  return(
    <>
      
              <button
                className={`${classes.nav_button} text-white btn m-0`}
                type="button"
                onClick={handleDownload}
              >
                {landingPageResponse.header_and_sticy_cta}
              </button>
             
    </>
  )
}