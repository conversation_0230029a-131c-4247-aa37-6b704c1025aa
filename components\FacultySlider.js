import { useState } from "react";
import Slider from "react-slick";
import classes from "../pages/v2/[learning_track_id]/index.module.css";
import Image from "next/image";

 
export const FacultySlider = ({ title, flag, courseProf, apiUrl }) => {
  const [showFacultyModal, setShowFacultyModal] = useState(false);
  const [facultyData, setFacultyData] = useState(null);
 
  const settings = {
    dots: true,
    infinite: true,
    speed: 400,
    autoplay: false,
    autoplaySpeed: 8000,
    slidesToShow: courseProf.length ===1 ? 1 : 2,
    slidesToScroll: 1,
    arrows: true,
    nextArrow: <CaseletNextArrow />,
    prevArrow: <CaseletPrevArrow />,
    responsive: [
      {
        breakpoint: 990,
        settings: {
          arrows: true,
          slidesToShow: 1,
        },
      },
    ],
  };

  const handleCloseFacultyModal = () => {
    document.getElementById("modal_content").scrollTop = 0;
    document.documentElement.style.overflow="auto"
    document.documentElement.style.paddingRight="0px"
    setShowFacultyModal(false)
  };
  const handleShowFacultyModal = (data) => {
    setFacultyData(data.attributes)
    document.getElementById("modal_content").scrollTop = 0;
    document.documentElement.style.overflow="hidden"
    if(window.innerWidth>768){
    document.documentElement.style.paddingRight="17px"}
      setShowFacultyModal(true)
  }
  return (
    <div className={`container-fluid bg-white ${flag ? "" : 'shadow-sm'}`}>
      <div className="row">
        <div className="col-md-11 col-10 p-0 pb-5 pt-4 px-4 bg-white m-auto d-flex flex-column">
          <h2 className={classes.sideHeads}>{title}</h2>
          <Slider {...settings}>
            {courseProf.map((professor) => (
              <div key={professor.id} className="d-lg-flex bg-white">
                <div className={`col bg-white mt-md-4 py-md-4 py-2 mt-2 ps-md-4 ps-lg-4 ps-sm-2 ps-xs-0 pe-2 mb-2 `} style={{ borderRadius: "12px" }}>
                  <div className={`d-flex flex-sm-column flex-md-row flex-column justify-content-center align-items-center text-center text-md-start text-lg-start ${classes.profRow} w-100`}>
                    <div className={`${classes.centeredImage}`}>
                      <Image
                        width="0"
                        height="0"
                        sizes="100vw"
                        alt="professorImage"
                        className={`${classes.roundedCircle}`}
                        src={`${apiUrl}${professor.attributes.prof_image.data.attributes.url}`}
                      />
                    </div>
                    <div className={`${courseProf.length ===1 ? "":"w-100"} ps-3 pt-0 ${classes.facultytext}`}>
                      <h6 className={`${classes.moduleTitle}`}>
                        {professor.attributes.prof_first_name + " " + professor.attributes.prof_last_name}
                      </h6>
                      <div className={`${classes.centeredContent} ${classes.new_prof_description}`}>
                        <p className={`${professor.attributes.prof_description.length > 30 ? "mb-0" : ""}`}>
                          {professor.attributes.prof_description}
                        </p>
                        {professor.attributes.faculty_landing_page_description ? (
                          <div className={classes.alignRight}>
                            <span
                              className="text-primary text-decoration-underline pointer"
                              onClick={() => handleShowFacultyModal(professor)}
                            >
                              <b>Read Full Profile</b>
                            </span>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </Slider>

          {/* Modal */}

          <FacultyModal apiUrl={apiUrl} facultyData={facultyData} classes={classes} handleCloseFacultyModal={handleCloseFacultyModal} showFacultyModal={showFacultyModal}/>
           
        </div>
      </div>
    </div>
  );
}

export function CaseletNextArrow(props) {
  const { className, style, onClick } = props;
  return (
    <div
      className={className}
      style={{ ...style, display: "block", paddingLeft: props.paddinngLeft ? props.paddinngLeft : "20px" }}
      onClick={onClick}
    />
  );
}

export function CaseletPrevArrow(props) {
  const { className, style, onClick } = props;
  return (
    <div
      className={className}
      style={{ display: "block", marginLeft: props.paddinngLeft ? props.paddinngLeft : "-20px" }}
      onClick={onClick}
    />
  );
}





export const FacultyModal = ({ apiUrl, facultyData, classes, showFacultyModal, handleCloseFacultyModal }) => {
  return (
    <div id="myModal" className={`${classes.fin_modal} d-${showFacultyModal ? "flex" : "none"}`} >
      {facultyData && <div className={classes.modal_content}>
        <div className="financing_optns_div" style={{ maxWidth: "900px" }}>
          <div className="position-relative">
            <span className={`${classes.closebtn} position-absolute end-0`} onClick={handleCloseFacultyModal}>&times;</span>
          </div>
          <div id="modal_content" className={classes.paymodal_body}>
            <div className={`${classes.contentext} container-md pt-4 pb-3 pb-md-4`}>
              <div className='row mx-auto d-flex justify-content-between ' style={{ maxWidth: "1000px", maxHeight:"430px", overflow:"auto"}}>


                <div className="d-flex flex-column">
                  <div className={`${classes.centeredImage}`}>
                    <Image
                      width="0"
                      height="0"
                      sizes="100vw"
                      alt="professorImage"
                      className={`${classes.roundedCircle}`}
                      src={`${apiUrl}${facultyData.prof_image.data.attributes.url}`}
                    />
                  </div>
                  <div className="mt-2">
                    <h6 className={`${classes.moduleTitle}`}>
                      {(facultyData?.prof_first_name ? facultyData.prof_first_name : "") + " " + (facultyData?.prof_last_name ? facultyData.prof_last_name : "")}
                    </h6>
                  </div>
                  <p className={`${facultyData.prof_description.length > 30 ? "" : ""}`}>
                    {facultyData.prof_description}
                  </p>
                </div>

                <div className={`${classes.contentext_child} mx-auto`} >
                  <p>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: facultyData && facultyData.faculty_landing_page_description,
                      }}
                    ></div>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>}
    </div>
  )
}