.btnStyle{
  background-color: white !important;
  color: #057092;
  border:none;
  padding: 10px 36px 10px 36px;
  font-weight: 600;
  flex-wrap: wrap;
  margin-right: 8px;
  margin-left: 8px;
  border-radius: 0px!important;
  width: 275px;
  height: 45px;
}
.showBanner{
  display: none;
}
  .row {
    display: flex;
    flex-direction: column;
    height: 100vh; /* Adjust this as needed */
  }
  .highlightbackground{
    background-color: var(--isb-lightblue-color);
  }
  .titleDiv{
    margin-top: auto; /* Align to bottom */
  }

  .rightBorder{
    border-right: none; 
    padding: 5px;
  }

  .rightBorde{
    padding: 5px;
  }

  /* .programTitle{
    background-size: cover;
    background-repeat: no-repeat;
    font-weight: 700;
    color: white;
  } */

  .programTitle{
    background-size: cover;
    box-shadow: inset 100px 150px 100px 100px rgb(24 91 112 / 91%);
    background-repeat: no-repeat;
    font-weight: 700;
    font-size: 20px;
    color: white;
  }

  .mobileProgramTitle{
    /* max-height: 850px; */
    background-repeat: no-repeat;
    font-weight: 700;
    font-size: 18px;
    color: white;
  }

  .mobileBackGround{
    background-color: #057092;

  }


.highlights li{
    color: white !important;
}
  .equalPadding{
    max-width: var(--isb-container-max-width);
  }

  .blackHeading{
    font-weight: 700;
    font-size: 26px;
    line-height: 35.47px;
    color: #000;
  }

  .whiteHeading{
    font-weight: 700;
    font-size: 26px;
    line-height: 35.47px;
    color: #fff;
  }

.image_border{
border-radius: 0%;
 min-height: 138px;
}

.centerAlignText{
    display: flex;
    align-items: center;
}
    
.learnTrackTitle{
   color: black;
   font-size: 16px;
   font-weight: 700;
}
.boxshadow{
  border: none;
  border-radius: 0%;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12); 
}

.buttonPosition{
    bottom: 0px;
    position: absolute;
}

.viewbtn {
background-color: #057092 !important;
padding: 6px 20px;
border:none;
border-radius: 0px!important;
color: #fff;
}
      
.viewbtn:disabled{
 background-color: grey !important;
 padding: 6px 20px;
 border:none;
 border-radius: 0px!important;
 color: white!important;
}    
      
.execBlock{
    background-color: #057092 !important;
}

.lightBlueBg{
    background-color: #F9FAFA;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
}

.lightBlueBorder{
    background-color: #F9FAFA;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #ECECEC;
}

.blueBoldText{
    font-weight: 700;
    font-size: 20px;
    line-height: 27px;
    color: #057092;
}

.formLabelText{
    font-size: 13px;
    font-weight: 500 !important;
}

.checkBox{
    height: 12px; 
    width: 18px;
    margin: 0px;
}

.land_btn{

  background-color: #057092 !important;
    border:none;
    padding: 8px 35px;
    font-weight: 600;
    flex-wrap: wrap;
    margin-right: 8px;
    margin-left: 8px;
    border-radius: 5px!important;
    width: 275px;
    /* height: 42px; */

}
.formLabelTextcolor{
  color: red;

}
.forminput{
  max-height: 32px;
  border-radius: 5px !important;
  width: 100% !important;
  border-color: #708FAB !important;
  background-color:#EFF9FE !important;
  font-size: 14px;
}

.formselect{
  border-radius: 5px;
  border-color: #708FAB;
  box-shadow: none !important;
  border-color: #708FAB !important;
  background-color:#EFF9FE !important;
  font-size: 14px;
}

.showMobileFold{
  display: none;
}

.lp_sticky_bar{
  position: sticky;
  top: -1px;
  background: rgb(236, 238, 237);
  z-index: 5;
}

.programTitle{
  font-size: 32px;
}

@media (min-width: 976px) {
  .rightBorder{
    border-right: 2px solid #eff9fe;
        padding: 5px;
  }
  /* Styles for desktop and larger screens */
  .fold1bg {
    background-size: cover;
    background-repeat: no-repeat;
  
  }
  .back_to_top {
      top: 0px;
      background-color: #ECEEED;
      z-index: 9;
  }
  .hideButton{
      display: block;
    }
    .rightBorder{
      border-right: 2px solid #EFF9FE;
      padding: 5px;
    }
}
@media screen and (min-width:0px) and (max-width: 499px) {

    .paragraph{
        font-size: 14px;
    }
    
    .title{
        font-size: 24px;
    }

    .rightBorder{
      border-right: none;
      border-bottom: 2px solid  #EFF9FE;
      ;
      padding: 10px;
    }
    .fold1bg {
      display: none;  
    }
    .showMobileFold{
      display: block;
    }
    .showBanner{
      display: block;
    }

    .cookie_banner {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 10px;
      background-color: #057092;
      border-top: 1px solid #ddd;
      text-align: center;
      z-index: 2;
    }

    .hideButton{
      display: none;
    }
}

@media screen and (min-width:500px) and (max-width: 976px){
    .paragraph{
        font-size: 14px;
    }
    
    .title{
        font-size: 24px;
    }
    
    .fold1bg {
      display: none;  
    }

     
      .showMobileFold{
        display: block;
      }

      .hideButton{
          display: none;
        }
      .showBanner{
        display: block;
      }
      
      .cookie_banner {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 10px;
        background-color: #057092;
        border-top: 1px solid #ddd;
        text-align: center;
        z-index: 2;
      }

      .back_to_top {
          top: 0px;
          background-color: #ECEEED;
      }
}
.bluebackground{
  background-image: url('../../../assets/TealBackground.jpg');
}
.sideHeads {
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 50px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;
  color: var(--isb-blue-color);
  text-wrap: nowrap;
}
.image_container {
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.lpkeyconceptsul{
  max-height: 300px;
  overflow: auto;
  min-height: 290px;
}
.lpkey_concepts{
  padding-left:10px;
}
.custombulleticon{
  list-style: none;
  position: relative;
  padding-left: 50px; /* Adjust based on your icon size */
  font-size: 17px;min-height: 30px;
}
.custombulleticon::before{
  content: '';
  position: absolute;
  left: 0;
  top: 10%;
  width: 20px; /* Adjust based on your icon size */
  height: 100%; /* Adjust based on your icon size */
  background: url('../../../assets/checked (1) 1.svg') no-repeat top;
  background-size: contain;

}
.lpcustombulleticon{
  list-style: none;
  position: relative;
  padding-left: 45px; 
  min-height: 55px;
}
.lpcustombulleticon::before{
  content: '';
  position: absolute;
  left: 0;
  top: 5px;
  width: 25px; 
  height: 100%; 
  background: url('../../../assets/BlueTick.svg') no-repeat top;
  background-size: 25px;
  min-height: 25px;
  

}
@media screen and (min-width:0px) and (max-width: 499px) {
  
  .custombulleticon::before{
    top: 10%;
  }
  .lpkeyconceptsul{
    max-height: 100%;
  }
}
@media screen and (min-width:500px) and (max-width: 976px) {
 
  .custombulleticon::before{
    top: 10%;
  }
}
@media (max-width: 990px) {
.programTitle{
  font-size: 18px;
}
  .custombulleticon::before{
    top: 10%;
  }
}
 
.light_bg .blueHeading{
  display: flex;
  justify-content: center;
  text-align: center;
  padding-top: 2rem;
  padding-bottom: 0.5rem;
}

.blueHeading{
  font-size:24px;
  /* line-height: 32px; */
  color: var(--isb-blue-color);
  font-weight: bold;

 }

 .blackHeading{
  font-size:24px;
  line-height: 32px;
 }

.subheading{
  color: #000000;
}

 
.nwlTitle{
  font-weight: 600;
  font-size: 20px;
  line-height: 34.47px;
  color: #057092;
  display: flex;
}
.hr{

width: 100px;
height: 0px;
margin: auto;
border: 2px solid #2E2661;
display: flex;
justify-content: center;



}
.pro_title{
 
font-family: Open Sans;
font-size: 20px;
font-weight: 700;
line-height: 25px;
letter-spacing: 0em;
text-align: center;

}


.roundedCircle{
  border-radius:50%;
  width:125px;
  height:125px;
  text-align: -webkit-right;
  }

.whitebackground{

  background: #FFFFFF;
  background-size: cover;

}


.card_main{
  background:#26ABCD;
  background-size: cover;
  border-radius: 0px;
}

.foottitle{
  border-bottom:1px solid rgb(215, 224, 232);
  color:white;
  padding-bottom:8px;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  }
  
.head_line{
font-style: normal;
font-weight: 700;
font-size: 18px;
color: #000000;


}
.date_line{
font-style: normal;
color: #000000;
}
.new_date_line{
  font-style: normal;
  font-weight: 700;
  color: #000000;
  }

.last_line{
font-family: 'Arial';
font-style: italic;
font-weight: 400;
font-size:smaller;
color: #000000;
}

.testmonial_fold{
  background: #D7E0E8;
  background-image: url(../../../assets/bg_Image_fold.png);
  background-size: cover; 
  background-repeat: no-repeat; 
  background-position: center; 

}
.small_img{
  margin-right: 10px;
}
.last_last_line{
font-family: 'Inter';
font-style: italic;
font-weight: 400;
font-size: 14px;
text-decoration-line: underline;
color: #000000
}

.stickyButtonText{
  font-family: 'Inter';
  font-weight: 400;
  font-size: 12px;
  color: #000000
  }

.icon{
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.coursal_image{
  height:150px;
}

.faqQuestion{
  font-size: 17px;
  font-weight: 500;
}

.faqAnswer{
  text-align: left;
  color: black !important;
}

.black_text{
  color: #000000;
}

.card1_bg{
  background-color: #ECEEED
}

.card2_bg{
  background-color: #B0E0D3;
}
.card3_bg{
  background-color:#ECEEED ;
}

.badgebackground{

  background-color: #F9FAFA;
}

.avatar {
  margin-bottom: -50px;
  overflow: hidden;
  border: 3px solid #fff;
  border-radius: 50%;
  }

  .rank_des{
font-family: Open Sans;
font-size: 16px;
font-weight: 400;
line-height: 22px;
letter-spacing: 0em;
text-align: center;
gap: 10px;
color: #585865;

}
.rank{

  font-family: Arial;
font-size: 64px;
font-weight: 700;
line-height: 74px;
letter-spacing: 0em;
text-align: left;

font-family: Arial;
font-size: 42px;
font-weight: 700;
line-height: 41px;
letter-spacing: 0em;
text-align: left;
color:#000000;
}
.para{
  color: black !important;
  text-decoration: none;
  background-color: transparent;
  align-items: baseline;
  font-weight: 400;
  vertical-align: center;
  margin: 0;
  padding-right: 50px;
}

.nav_button{
  background-color: #057092 !important;
    border:none;
    padding: 10px 22px 10px 22px;
    font-weight: 700;
    flex-wrap: wrap;
    margin-right: 8px;
    margin-left: 8px;
    border-radius: 8px!important;
    width: 210px;
    height: 45px;
}

.middle_bg{
  background-color: #B0E0D3;
}

.card_text{
 font-family: Open Sans;
 color: #057092;
}

.note_color{
  color: #7C8495;
}

.disablePointer{
  pointer-events: none;
}

.card_high{
  min-height: 184px;
}



.light_bg{
  background-color: #EFF9FE;
}

.container_main {
  position: relative;
  text-align: center;
  color: white;
}
.centered{
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
}

.h_w{
    height: 380px !important;
  width: 320px !important;
  display:inline !important;
}

.bg_image_head{
  font-weight: 700;
  font-size: 24px;
  line-height: 34.47px;
  color: #057092;
}

.bg_image_head_title{
  font-weight: 700;
  font-size: 24px;
  line-height: 34.47px;
  color:#000000;
}

.description{
  font-size: 17px;
  vertical-align: center;
  font-weight: 400 !important;
  line-height: normal;
}

.new_description p{
  font-size: 17px;
  vertical-align: center;
  font-weight: 400 !important;
  line-height: normal;
  color: #000000 !important;
}
.new_prof_description p{
  color: #000000 !important;
  font-size: 15px;
}

.roundedCircle{
  border-radius:2%;
  height: 147px;
  width: 147px;
  object-fit: cover;
  }

.aiimagestyle{
  height: 60px;
  width: 250px;
  object-fit: contain;
  }
  .ai_bottom_text p{
    font-weight: 500;
    font-size: 16px;
    color: #000;
  }
  
.responsive_div{
  border: 1px solid #057092;
  width: 50%;
  margin: 15px auto;
}
@media (max-width: 768px) {
  .responsive_div {
    width: 90%;  
  }
}

@media (max-width: 480px) {
  .responsive_div {
    width: 70%;  
  }
}
  .description-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-height: 3em;  
  }

  .custom_top_margin_fold{
    margin-top: 42px;
   
  }

 .bg_phone .flag-dropdown .selected-flag {
    background-color: #2596ce !important;
  }
  .secondLine {
    font-size: 45px; 
  }

  .main_blue_head{
    color:#057092;
    font-weight: bold;
    font-size: 24px;
  }

  .main_white_head{
    color:#FFFFFF;
    font-weight: bold;
    font-size: 24px;
  }

  @media (max-width: 768px) {
    .custom_top_margin_fold {
      text-align: center;
    }
    .custom_top_margin{
      text-align: center;
      margin-top: 30px
    }

    .centeredImage {
      display: flex;
      justify-content: center;
      align-items: center; 
    }
    .bdc_note_mobile{
      font-size: 10px !important;
      font-style: italic !important;
    }
    .firstLine{
      font-size: 18px;

    }
    .secondLine {
      font-size: 26px; /* Set the font size for the second line */
    }

    .centered_image{
      position: relative !important;
      transform: translate(-50%, -50%) !important;
      margin-top: -200px;
    
    }
 
  }




.moduleTitle{
color: #057092;
font-size: 24px;
font-weight: 700;
}

.highlightcards_description p{ 
color: #000000 !important;
font-weight:400 !important;
font-size: 14px !important;
line-height: 22px;
}

.highlightcards_title{ 
color: #000000 !important;
font-weight:700 !important;
font-size: 14px !important;
 
}
.prgrmhighlightcards_title{ 
color: #000000 !important;
font-weight:700 !important;
font-size: 16px !important;
/* text-wrap: nowrap; */
text-align: center;
margin-bottom: 4px;
}
.container_main .h_w .slick-slide img{
display: inline !important ;
}



.error_tooltip {
position: absolute;
color: white;
padding: 0px 8px;
border-radius: 4px;
margin-bottom: 2px;
display: block;
color: red;
font-size: 12px;
}

.form-group:hover .error_tooltip {
display: block;
}

.centered_image{
position: relative;
left: 50%;
transform: translate(-50%, -50%);
margin-top: -200px;
}

.playIcon {
position: absolute;
top: 50%;
left: 50%;
transform: translate(-50%, -50%);
}

.imageWrapper {
position: relative;
display: inline-flex;
align-items: center;
justify-content: center;
}




  @media (max-width: 990px) {
    .aiimagestyle{
      height: 35px;
      width: 155px;
      object-fit: contain;
      }
    .sideHeads {
      justify-content: center;
    }
    .modal_content{
      padding: 15px 10px !important;
      max-width: 100% !important;

      margin: 10px !important;
      
    }
    .closebtn{
      margin-top: -10px !important;
    }
    .iframe_main{
      padding-right: 0px !important;
    }
    .custom_top_margin_fold {
  text-align: center;
  }

  .custom_top_margin{
  text-align: center;
  margin-top: 30px
  }


}
/* Styles for desktop (screens wider than 768px) */
@media (min-width: 1000px) {
    .custom_top_margin {
      margin-top: 42px;
      padding-left: 50px;
    }

    .bdc_note_mobile{
      font-size: 14px !important;
      font-style: italic !important;
    }
    
  }

  @media (min-width: 765px) {
    .first_div {
      display: none !important;
    }
    
  }
  @media (max-width: 766px) {
    .second_div {
      display: none !important;
    }
    .bg_image_head_title {
      text-align: center;
    }
  
    .new_description {
      text-align: center;
    }
    .new_text{
      text-align: center;
    }
  }


  .newpara p {
    color: white !important;
  }

  .rank_description p{
    font-size: 15px !important;

  }

  .card_desc p {
    color: white !important;
  }

  .card_desc li {
    color: white !important;
  }

  .card_desc ul {
    margin-bottom: 0 !important;
  }


  .card {
    flex: 1;
    border: 1px solid #ccc;
    padding: 16px;
    margin: 8px;
    background-size: cover;
    background-position: center;
    color: white; /* Set text color to ensure readability */
  }
  .cardContainer {
    display: flex;
  }


  .land_btn:disabled{
    background-color:grey !important;
    opacity: 50%;
  }
  .video_box{
    position:relative;
}
.video_overlays {
    position:absolute;

}

.singupbg{
  width: 100%;
    min-height: 726px;
    background-position: left top;
    background-size: cover;
    background-repeat: no-repeat;
    max-width: 1920px;
    margin: 0 auto;
    position: relative;

}

@media (min-width: 989px) {
  .first_div_landing {
    display: none !important;
  }
  .second_div_landing{
    display: block !important;
    max-height: 250px;
  }
 
}
@media (max-width: 990px) {
  .first_div_landing {
    display: block !important;
    max-height: 250px;
  }
  .second_div_landing{
    display: none !important;
  }


}
 .prgm_highlights{
  max-width: 350px !important;
  margin-bottom: 18px;
 }
 .lp_whyisbonline{
  max-width: 300px !important;
  margin-bottom: 18px;
 }
.highlight_section_main{
  max-width: 1200px;

}
.advnew_highlightcards_description p{ 
  color: #414040 !important;
  display: flex;
  text-align: center;
  padding: 0px 16px;
  line-height: 22px;
}
.new_highlightcards_description p{ 
  color: #000000 !important;
  font-weight:600 !important;
  font-size: 14px !important;
  line-height: 22px;
}
.financing_options_link{
  color:#000000;
  font-style:normal;
  font-size:0.875rem;
  cursor:pointer;
  line-height: 22px;

}
.financing_options_link:hover{
  color:#000000;
}



/* //============Financing Modal Styles=============== */
.payment_plan_div h2{
font-size: 2rem;
margin-bottom: 16px;
width: calc(100% - 40px);
font-weight: 700
}
.programfee{
  color: #007bc7 !important;
  margin-bottom: 6px;
  text-transform: uppercase;
  font-family: "Roboto", arial, sans-serif !important;
  font-size: .875rem;

}
 .admin_price{
  font-size: 1.625rem;
    line-height: 2rem;
    font-weight: 700;
 }
 .gst_text{
  font-size: .875rem;
 }
 .payment_table_wrpr{
  margin-top: 25px;
}
.payment_table_wrpr h6{
  font-weight: bold;
  margin-bottom: 0px;
}
.bg_gray{
  background-color: #f3f3f3 !important;
}
.table_style{
  border-top: 1px solid  black;
}
.table_style th, .table_style td{
  text-align: center;
  font-size: 0.9rem;
} 
.table_style td:last-child{
  font-weight: 700;
}
.paymodal_body::-webkit-scrollbar {
  width: 8px !important;
}
.paymodal_body::-webkit-scrollbar-thumb{
  background: #afb1b3 !important;
  border-radius: 8px !important;
}

.fin_modal {
  position: fixed; 
  z-index: 1020;  
  left: 0;
  top: 0;
  width: 100%;  
  height: 100%;  
  overflow: hidden;  
  background-color: rgb(0,0,0);  
  background-color: rgba(0,0,0,0.4); 
  align-items: center;
  justify-content: center;
}
.iframe_main{
  padding: 10px 10px 0px 0px;
  width: 100%;
  display:flex;

}
/* Modal Content */
.modal_content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px 25px;
  border: 1px solid #888;
  max-width: 80%;
 
}
 
.paymodal_body{
  max-height: 80vh;
  overflow: auto;
}
/* The Close Button */
.closebtn {
  float: right;
  font-size: 28px;
  font-weight: bold;
  text-align: flex-start;
  margin-top: -18px;
  width: 15px;
}

.closebtn:hover,
.closebtn:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}


@media only screen and (max-width : 480px) {

  
}

/* Small Devices, Tablets */
@media only screen and (max-width : 768px) {
  .borderedcls{
    border-right: none !important;
  }
  .lpkeyconceptsul{
    background-color: transparent;
  }
}

/* Medium Devices, Desktops */
@media only screen and (min-width : 992px) {

}

/* Large Devices, Wide Screens */
@media only screen and (min-width : 1200px) {

}

.mobileimgcontainer{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}
.lpv2videosection{
  min-height: 520px;
}
.lp_centered_image{

  margin-top: -130px;
}