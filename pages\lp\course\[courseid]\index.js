import Link from "next/link";
import Image from "next/image";
import Nav from "react-bootstrap/Nav";
import Navbar from "react-bootstrap/Navbar";
import appLogo from "../../../../assets/ISB_Online Logo.png";
import classes from "../../../lp/[learning_track_id]/index.module.css";
import classes1 from "../../../finance.module.css"
import React, { useState } from "react";
import Group from "../../../../assets/New Online Documents.svg";
import projectduration from "../../../../assets/New project duration.svg";
import RupessDocuments from "../../../../assets/New Rupees Documents.svg";
import verfiedReport from "../../../../assets/New Verified Report.svg";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useRef, useEffect } from "react";
import useCollapse from "react-collapsed";
import { useRouter } from "next/router";
import BottomFold from "../../../../components/bottom_fold_one";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { NextSeo } from "next-seo";
import group from "../../../../assets/Group_2688.png";
import banner from "../../../../assets/footer_banner.jpeg";
import profser_bg from "../../../../assets/prof_fold_bg.webp"
import playIcon from "../../../../assets/play_icon.png";
import dynamic from "next/dynamic";
import { BrochureFileNameFormatter } from "../../../../utils";

const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });
 

export default function LandingPage(props) {
 
  const courselandingPageResponse = props.apiData.data[0].attributes;
  const [countryCodeEntry,setCountryCodeEntry] = useState()
  const [coutrycode_exclude,setCoutrycode_exclude] = useState()
  

 
  const pdfsArray = [
    {
        url: "../../Axis Bank.pdf",
        fileName: "Axis Bank.pdf"
    },
    {
        url: "../../Bank of Baroda Education Loan for ISB.pdf",
        fileName: "Bank of Baroda Education Loan for ISB.pdf"
    },
    {
        url: "../../HDFC Credila.pdf",
        fileName: "HDFC Credila.pdf"
    },
    {
        url: "../../idfc.pdf",
        fileName: "idfc.pdf"
    },
    {
        url: "../../Liquiloans-finance-ISB-Oct2022.pdf",
        fileName: "Liquiloans-finance-ISB-Oct2022.pdf"
    },
    {
        url: "../../Propelld Financing Option.pdf",
        fileName: "Propelld Financing Option.pdf"
    },
];
const API_Key = process.env.NEXT_PUBLIC_ADOBE_CLIENTID;
useEffect(() => {
  
  const script = document.createElement('script');
  script.src = 'https://acrobatservices.adobe.com/view-sdk/viewer.js';
  script.async = true;
  document.body.appendChild(script);

  script.onload = () => {
      document.addEventListener('adobe_dc_view_sdk.ready', function() {
        pdfsArray.forEach(pdf => {
              const adobeDCView = new AdobeDC.View({
                clientId: API_Key,
                  divId: "adobe-dc-view-" + pdf.fileName  
              });
              adobeDCView.previewFile({
                  
                  content: { location: { url: `${pdf.url}` } },
                  metaData: { fileName: pdf.fileName }
              }, { embedMode: "SIZED_CONTAINER" });
          });
      });
  };

  return () => {
      document.body.removeChild(script);
  };
}, []);  


let selectedIndices = []

for (let i = 1; i < pdfsArray?.length; i += 3) {
 selectedIndices.push(i);
}

  const { query } = useRouter();
  const [agree, setAgree] = useState(true);
 
  const [showIframe, setShowIframe] = useState(false);

  const handleImageClick1 = () => {
    setShowIframe(true);
  };


  const [isImageHidden, setIsImageHidden] = useState(false);
  const [autoplay, setAutoplay] = useState(false);


  const handleImageClick = () => {
    setIsImageHidden(true);
    setAutoplay(true);
  };

 

  const checkboxHandler = () => {
    setAgree(true);
    return false;
  };
 
 

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    number: '',
    role: 'Select',
    agree: true,
  });

  const [formErrors, setFormErrors] = useState({});

  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showFinancOptionsModal, setShowFinancOptionsModal] = useState(false);

  const handleClosePaymentModal = () => {
    document.getElementById("modal_content").scrollTop = 0;
    document.documentElement.style.overflow="unset"
    document.documentElement.style.paddingRight="unset"
    setShowPaymentModal(false)
    setShowFinancOptionsModal(false)
  };
  const handleShowPaymentModal = (e) => {
    document.getElementById("modal_content").scrollTop = 0;
    document.documentElement.style.overflow="hidden"
    document.documentElement.style.paddingRight="17px"
    const id= e.target.id
    if(id==="payment_modal"){
      setShowPaymentModal(true);
    }else if(id==="financing_options"){
      setShowFinancOptionsModal(true)
    }
   
  }

 
  const handleChange = (e) => {
    const { name, value, type } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));
  };


const handleChange1=(e)=>{
 
if(Number(e.slice(0,2)) ===91){
  setCountryCodeEntry(e.slice(0,2))
  let sliced_mobile = e.slice(2,12)
      setCoutrycode_exclude(sliced_mobile)

}else if(Number(e.slice(0,3)) ===971){
  setCountryCodeEntry(e.slice(0,3))
  let sliced_mobile = e.slice(3,13)
  setCoutrycode_exclude(sliced_mobile)

}else if(Number(e.slice(0,2)) ===65 || Number(e.slice(0,2)) ===61){
  setCountryCodeEntry(e.slice(0,2))
  let sliced_mobile = e.slice(2,13)
  setCoutrycode_exclude(sliced_mobile)
  
}

else{
  setCountryCodeEntry(e)
}

 
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        "number": '',
      }));
    
    setFormData((prevData) => ({
      ...prevData,
      "number": e,
    }));
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      "number": '',
    }));
}

  const isValidEmail = (email) => {
    return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email);
  };
 

  const isValidMobileNumber = (number) => {
    let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    return /^[0-9]{0,15}$/.test(cleanedNumber);
  };
  
  

  const [buttonDisabled, setButtonDisabled] = useState(false);

  const submitContact = async (event) => {
    event.preventDefault();
    const errors = {};
    setButtonDisabled(true);

    if (!formData.name) {
      errors.name = 'Name is required';
    }
    if( formData.name.length<MIN_NAME_LENGTH){
      errors.name = 'Name should be atleast 3 characters';
    }
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!isValidEmail(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.number) {
      errors.number = 'Mobile number is required';
    } 
   if(countryCodeEntry){
     if (countryCodeEntry.slice(0,2) ==91 && (coutrycode_exclude).length<10) {
      errors.number = 'Please enter valid mobile number'; 
    }else if((countryCodeEntry.slice(0,2) ==65 || countryCodeEntry.slice(0,2) ==61) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter valid mobile number'; 
    }
    else if((countryCodeEntry.slice(0,3) ==971) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter a valid mobile number '; 
    }
  }
    else if (!isValidMobileNumber(event.target.number.value)) {
      errors.number = 'Please enter a valid mobile number';
    }

    if (formData.role === 'Select') {
      errors.role = 'Please select a role';
    }



    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setButtonDisabled(false);
      return;
    }
        let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    const getCountryCode = (phoneNumber) =>
      phoneNumber.match(/^\+(\d{1,3})/)?.[1];
    const countryCode = getCountryCode(event.target.number.value);

    const now = new Date();
    const expirationDate = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);

    const json = {
      first_name: formData.name,
      email: formData.email.toLowerCase(),
      country_code: countryCode,
      mobile: cleanedNumber,
      role: formData.role,
      url: `${props.baseurl}/lp/course/${props.meetupId}&utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}_`,
      program_id:process.env.NEXT_PUBLIC_BASE_URL === 'https://online.isb.edu'
      ? courselandingPageResponse.prod_program_id
      : courselandingPageResponse.staging_program_id,
      tags: `utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}_`,
    };

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_LANDINGPAGE_SFDC_URL}/backend/free_lesson/user_create`,
      {
        body: JSON.stringify(json),
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
      }
    );
    const data = await response.json();
    const userId = data.user_id;

    if (response.status === 200) {
      document.cookie = `leadform_name=${formData.name.trim()}; expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_email=${formData.email?.trim()}; expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_mobile=${json.mobile}; expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_country_code=${json.country_code}; expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_ProgramId=${json.program_id}; expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_role=${formData.role}; expires=${expirationDate.toUTCString()}; path=/;`;
      document.cookie = `leadform_id=${userId}; expires=${expirationDate.toUTCString()}; path=/;`;

      const cookies = document.cookie.split(';')
      handleDownload();
      router.push({
        pathname: `/lp/course/${props.meetupId}/thankyou`,
      });
    }
    else{
      setButtonDisabled(false);
    }
  };


  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth", // for smoothly scrolling
    });
  };

  const C = (
    <FontAwesomeIcon
      icon={faMinus}
      className="fa-solid fa-minus"
      style={{ color: "#7C8495" }}
    />
  );
  const E = (
    <FontAwesomeIcon
      icon={faPlus}
      className="fa-solid fa-plus"
      style={{ color: "#7C8495" }}
    />
  );

  const router = useRouter();

  const Collapse = ({ index, ...data }) => {

    const { getCollapseProps, getToggleProps, isExpanded } = useCollapse();
    const [lessons] = useState([data.attributes.lessons]);
    let module_name = data.attributes.module_name;

    return (
      <>
        <div>
          <h5
            className={`${classes.faqQuestion} d-flex py-2 justify-content-between text-black m-0 mb-2`}
            {...getToggleProps()}
          >
            {`Module ${index + 1}: ${module_name}`}

            {isExpanded ? C : E}
          </h5>
          <div {...getCollapseProps()}>
            {lessons[0].map((item, index) => (
              <ul key={index} >
                <li>{item.lesson_name}</li>
              </ul>
            ))}
          </div>
        </div>
        <div className={`mb-3 p-0 ${classes.foottitle}`}></div>
      </>
    );
  };

 

  function ProfessorCard({ professor }) {
    const [showFullDescription, setShowFullDescription] = useState(false);
  
    const toggleDescription = () => {
      setShowFullDescription(!showFullDescription);
    };
  
    return (
      <div className="d-lg-flex">
        <div className="col"></div>
        <div className={`col-lg-11 bg-white mt-5 py-4 px-4 mb-4 shadow-sm`} style={{ borderRadius: "12px" }}>
          <div className={`d-flex ${classes.profRow}`}>
          <div className={` ${ classes.centeredImage}`}>

            <Image
              width="0"
              height="0"
              sizes="100vw"
              alt="professorImage"
              className={` ${classes.roundedCircle}`}
              src={`${props.apiUrl}${professor.attributes.prof_image.data.attributes.url}`}
            />
            </div>
            <div className={`${classes.centeredContent} ps-3 pt-0 `}>
              <h6 className={` ${classes.moduleTitle}`}>
                {professor.attributes.prof_first_name + " " + professor.attributes.prof_last_name}
              </h6>
              
          <div className={`${classes.centeredContent} ${classes.new_prof_description}`}>
            <p>{professor.attributes.prof_description}</p>
            {professor.attributes.faculty_landing_page_description?.length > 0 ? (
              showFullDescription ? (
                <>

                  <div>
                    {professor.attributes.faculty_landing_page_description ? (
                      <div dangerouslySetInnerHTML={{ __html: professor.attributes.faculty_landing_page_description }} />
                    ) : null}
                    </div>
                  <div className={classes.alignRight}>
                    <a onClick={toggleDescription}>
                      <b style={{cursor:"pointer"}}>Read Less</b>
                    </a>
                  </div>
                </>
              ) : (
                <>
                  
            <div >
              {professor.attributes.faculty_landing_page_description ? (
                <div dangerouslySetInnerHTML={{ __html: professor.attributes.faculty_landing_page_description.slice(0, 0)  }} />
              ) : null}
          </div>
                  <div className={classes.alignRight}>
                    <a onClick={toggleDescription}>
                      <b style={{cursor:"pointer"}}>Read More</b>
                    </a>
                  </div>
                </>
              )
            ) : (
              <p>{professor.attributes.prof_landing_page_description}</p>

            )}
          </div>

            </div>
          </div>
        </div>
        <div className="col"></div>
      </div>
    );
  }
 


  const handleDownload = async () => {
    try {
      const response = await fetch(
        props.apiUrl + courselandingPageResponse.brochure.data.attributes.url
      );
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      const formattedName = BrochureFileNameFormatter(props.meetupId);
      link.download = `${formattedName +".pdf"}`;
      link.click();
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };

  const sliderRef = useRef(null);
  const settings = {
    dots: false, // Always display dots
    infinite: true,
    speed: 400,
    autoplay: true,
    autoplaySpeed: 8000,
    slidesToScroll: 1,
    arrows: true, // Enable arrows by default
    slidesToShow: 2, // Set the default number of slides to show
  
    responsive: [
      {
        breakpoint: 990, // Adjust this breakpoint to your desired screen size (e.g., for mobile)
        settings: {
          slidesToShow: 1, // Show 1 slide on smaller screens
          arrows: false, // Disable arrows on smaller screens (mobile)
          dots: true, // Enable dots on smaller screens (mobile)
        },
      },
    ],
  };

 
  
  const settings_learning= {
    dots: true, // Always display dots
    infinite: true,
    speed: 400,
    autoplay: true,
    autoplaySpeed: 8000,
    slidesToScroll: 1,
    arrows: false, // Enable arrows by default
    slidesToShow: 1, // Set the default number of slides to show
  };
  const alumsettings = {
    dots: true,
    infinite: false,
    speed: 400,
    autoplay: true,
    autoplaySpeed: 8000,
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: false,
    responsive: [
      {
        breakpoint: 796, // Adjust the breakpoint as needed for your mobile view
        settings: {
          slidesToShow: 1, // Show a single item on mobile
          slidesToScroll: 1,
        },
      },
    ],
  };
 


  const scrollToOverview = (target) => {
    const scrollTarget = document.getElementById("overview");
    if (scrollTarget) {
      let offset = 50.0; // Default offset for desktop view

      if (window.innerWidth <= 768) {
        offset = 0; // Offset for mobile view
      }

      const scrollPosition =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset;
      const adjustedPosition = scrollPosition - offset;

      window.scrollTo({
        top: adjustedPosition,
        behavior: "smooth", // Adjust scrolling behavior (e.g., smooth, auto)
      });
    }
  };
  const scrollToHighlights = (target) => {
    const scrollTarget = document.getElementById("Highlights");
    if (scrollTarget) {
      let offset = 0; 

      if (window.innerWidth <= 768) {
        offset = 0; 
      }

      const scrollPosition =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset;
      const adjustedPosition = scrollPosition - offset;

      window.scrollTo({
        top: adjustedPosition,
        behavior: "smooth", 
      });
    }
  };
  const scrollToFaculty = (target) => {
    const scrollTarget = document.getElementById("Faculty");
    if (scrollTarget) {
      let offset = 0; // Default offset for desktop view

      if (window.innerWidth <= 768) {
        offset = 0; // Offset for mobile view
      }

      const scrollPosition =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset;
      const adjustedPosition = scrollPosition - offset;

      window.scrollTo({
        top: adjustedPosition,
        behavior: "smooth", // Adjust scrolling behavior (e.g., smooth, auto)
      });
    }
  };
  const scrollToEligibility = (target) => {
    const scrollTarget = document.getElementById("Eligibility");
    if (scrollTarget) {
      let offset = 0; // Default offset for desktop view

      if (window.innerWidth <= 768) {
        offset = 0; // Offset for mobile view
      }

      const scrollPosition =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset;
      const adjustedPosition = scrollPosition - offset;

      window.scrollTo({
        top: adjustedPosition,
        behavior: "smooth", // Adjust scrolling behavior (e.g., smooth, auto)
      });
    }
  };
 
  const [showSticky, setShowSticky] = useState(false);
  const scrollDivRef = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      if (scrollDivRef.current) {
        const div = scrollDivRef.current;
        const rect = div.getBoundingClientRect();

        if (rect.top < 0) {
          setShowSticky(true);
        } else {
          setShowSticky(false);
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const professors = courselandingPageResponse.professors.data;



  const [selectedItems, setSelectedItems] = useState(courselandingPageResponse.highlightcards.slice(0, 3)); 
  
  const [selectedItems1, setSelectedItems1] = useState(courselandingPageResponse.new_highlightcards.slice(0, 3));   
  
  return (
    <>
      <NextSeo
        // title="Certificate Masters Programme in Management Essentials"
        title={courselandingPageResponse.meta.title}
        canonical={`${process.env.NEXT_PUBLIC_BASE_URL}` + router.asPath}
        noindex = {true}
        nofollow = {true}
        // description="Make informed decisions, inspire your teams, and confidently manage projects by discovering modern management principles' power."
        description={courselandingPageResponse.meta.description}
        openGraph={{
          type: `website`,
          url: `${process.env.NEXT_PUBLIC_BASE_URL}` + router.asPath,
          title: `${courselandingPageResponse.meta.title}`,

          description: `${courselandingPageResponse.meta.description}`,
          locale: "en",
          images: [
            {
              url: `${
                props.apiUrl +
                courselandingPageResponse.meta.image.data.attributes.url
              }`,
              alt: "image.png",
            },
          ],
          site_name: `ISB Online`,
        }}
        twitter={{
          handle: "@handle",
          site: "@site",
          cardType: `${courselandingPageResponse.meta.title}`,
        }}
        additionalMetaTags={[
          {
            name: "keywords",
            content: `${courselandingPageResponse.meta.keywords}`,
          },
          {
            name: "robots",
            content: `${courselandingPageResponse.meta.robots}`,
          },
        ]}
      />

      <Navbar
        // sticky="top"
        className={`${classes.navelems} px-4`}
        bg="white"
        expand="lg"
        id="myNavbar"
      >
        <Navbar.Brand>
          <Image width={170} src={appLogo} alt="ISB Logo" />
        </Navbar.Brand>

        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse
          className="d-base-flex justify-content-end"
          id="basic-navbar-nav"
        >
          <Nav className="text-right" id="myNavItem">
            <Nav.Link as="span">
              <a onClick={scrollToOverview} className={classes.para} href="#">
                Overview
              </a>
            </Nav.Link>

            <Nav.Link as="span">
              <a onClick={scrollToHighlights} className={classes.para} href="#">
                Highlights
              </a>
            </Nav.Link>

            <Nav.Link as="span">
              <a onClick={scrollToFaculty} className={classes.para} href="#">
                Faculty
              </a>
            </Nav.Link>

            <Nav.Link as="span">
              <a
                onClick={scrollToEligibility}
                className={classes.para}
                href="#"
              >
                Eligibility
              </a>
            </Nav.Link>

            <Link href="#form" scroll={false} className={classes.hideButton}>
              <button
                className={`${classes.nav_button} text-white btn m-0`}
                type="submit"
              >
                {courselandingPageResponse.header_and_sticy_cta}
              </button>
            </Link>
          </Nav>
        </Navbar.Collapse>
      </Navbar>

{/* fold one div */}
      <div
        className={`${classes.fold1bg} ${classes.singupbg} `}  style={{ position: "relative" }}
        
      >
        <Image
            src={`${props.apiUrl}${courselandingPageResponse.hero_image_desktop.data.attributes.url}`}
            alt="DesktopheroImage"
            layout="fill"
            objectFit="cover"
            // objectPosition="center"
            priority
          />
        <div className={`row py-lg-5 p-0 ${classes.equalPadding} mx-auto m-0`} style={{ position: "relative", zIndex: 1 }}>
          <div
            className={`${classes.mobileFoldbg} col-lg-6 col-md-12 py-lg-0 py-md-4 py-sm-4 py-3 px-0 d-flex flex-column justify-content-center`}
          >
            <div className={''}>
           
           <p className={`${classes.programTitle} p-3 m-0 text-center`}>
                  {courselandingPageResponse.title.includes(" in ") ? (
                    courselandingPageResponse.title.split(" in ").map((part, index, array) => (
                      <React.Fragment key={index}>
                         <span className={index === 0 ? classes.firstLine : classes.secondLine}>
                              {part}
                            </span>
                        {index < array.length - 1 ? " in " : null}
                        {index === 0 ? <br /> : null}
                      </React.Fragment>
                    ))
                  ) : (
                    courselandingPageResponse.title
                  )}
           </p>
            </div>
          </div>
          <div className="col-2"></div>
          <div id="form" className="col-lg-4 col-md-12 p-4 bg-white " style={{borderRadius:"12px"}}>
            <div>
              <p className={`p-0 m-0 mb-2 ${classes.blueBoldText}`}>
                {courselandingPageResponse.signup_form.signup_title}
              </p>
          
              <form onSubmit={submitContact} className="my-0">
                <div className="form-group col-12 py-1">
                  <label
                    className={`control-label ${classes.formLabelText} fw-bold`}
                    htmlFor="name"
                  >
                    {/* Name{" "} */}
                    {courselandingPageResponse.signup_form.Name_lable}
                    <span className={`${classes.formLabelTextcolor}`}>*</span>
                  </label>
                  <input
                    className={`form-control ${classes.forminput} border-0 `}
                    type="text"
                    id="name"
                    name="name"

                    value={formData.name}
                    onChange={handleChange}
                    onKeyPress={(event) => {
                      if (!/^[A-Za-z. ]$/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    
                  />
                    {formErrors.name && <div  className={`${classes.error_tooltip} mb-5`}>{formErrors.name}</div>}
                </div>

                <div className="form-group col-12 py-3 ">
                  <label
                    className={`control-label ${classes.formLabelText} fw-bold`}
                    htmlFor="email"
                  >
                    {/* Email{" "} */}
                    {courselandingPageResponse.signup_form.Email_lable}
                    <span className={`${classes.formLabelTextcolor}`}>*</span>
                  </label>
                  <input
                    className={`form-control ${classes.forminput} border-0`}
                    type="text"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                  />
                  {formErrors.email && <div  className={`${classes.error_tooltip}`}>{formErrors.email}</div>}
                </div>
                <div className="form-group col-12 py-1" >
                  <label
                    className={`control-label ${classes.formLabelText} fw-bold`}
                    htmlFor="number"
                  >
                    {courselandingPageResponse.signup_form.Mobile_lable}
                    <span className={`${classes.formLabelTextcolor}`}>*</span>
                  </label>
                  <div className="bg_phone border-0">
                  
                <PhoneInput
                    country={"in"}
                    inputClass={`form-control ${classes.forminput} border-0`}
                    // disableDropdown
                    placeholder={""}
                    defaultCountry={"in"}
                    name= "number"
                    // searchPlaceholder ={""}
                    value = {formData.number}
                    onChange ={handleChange1}
                    inputProps={{
                      id: "number",

                      onKeyPress: (event) => {
                        if (!/[0-9]/.test(event.key) ) {
                          event.preventDefault();
                        }
                      },

                    }}
                    countryCodeEditable={false} 
                    // format={false} 
                />

                  {formErrors.number && <div  className={`${classes.error_tooltip}`}>{formErrors.number}</div>}

                  </div>
                </div>
                <div className="form-group col-12 py-3">
                  <label
                    className={`control-label ${classes.formLabelText} fw-bold`}
                    htmlFor="role"
                  >
                    {/* I am interested in programmes for{" "} */}
                    {courselandingPageResponse.signup_form.dropdown_lable}

                    <span className={`${classes.formLabelTextcolor}`}>*</span>
                  </label>
                  <select
                    className={`form-select ${classes.formselect} border-0`}
                    id="role"
                    name="role"
                    // required
                    value={formData.role}
                    onChange={handleChange}
                  >
                     <option value="Select">Select</option>
                      <option value="MySelf">My Self</option>
                      <option value="My Team">My Team</option>
                      <option value="My Organisation [L&D Responsibility]">
                        My organisation (L & D Responsibility)
                      </option>
                  </select>
                  {formErrors.role && <div className={`${classes.error_tooltip}`}>{formErrors.role}</div>}
                  
                </div>

                <div className={`mt-3 ${classes.disablePointer}`}>
                  <label style={{ fontSize: "13px" }} htmlFor="agree">
                    <input
                      defaultChecked={true}
                      className={classes.checkBox}
                      type="checkbox"
                      id="agree"
                      onClick={checkboxHandler}
                      onChange={checkboxHandler}
                    />
                    &ensp;
                    {/* {landingPage.lead_form_terms_content} */}
                    {courselandingPageResponse.signup_form.checkbox_text}
                  </label>
                </div>

                <div className="text-center">
                  <button
                    // disabled={!agree}
                    disabled={buttonDisabled}
                    className={`${classes.land_btn}  text-white mt-3`}
                    type="submit"
                  >
                    {courselandingPageResponse.signup_form.brochure_btn}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
       

{/* fold one for mobile div */}
      <div
        className={`${classes.equalPadding} row mx-auto ${classes.showMobileFold}`}
      >
        <div className={classes.mobileBackGround}>
          <p className={`${classes.mobileProgramTitle} p-3 m-0 text-center`}>
            {/* {courselandingPageResponse.title} */}
            {courselandingPageResponse.title.includes(" in ") ? (
                    courselandingPageResponse.title.split(" in ").map((part, index, array) => (
                      <React.Fragment key={index}>
                         <span className={index === 0 ? classes.firstLine : classes.secondLine}>
                              {part}
                            </span>
                        {index < array.length - 1 ? " in " : null}
                        {index === 0 ? <br /> : null}
                      </React.Fragment>
                    ))
                  ) : (
                    courselandingPageResponse.title
                  )}
          </p>
        </div>
        <Image
          height={0}
          width={0}
          sizes="100vw"
          className="col-12 img-fluid p-0"
          alt="heroImage"
          src={
            props.apiUrl +
            courselandingPageResponse.hero_image_mobile.data.attributes.url
          }
        ></Image>
        <div className="col-12 bg-white">
          <div className="col-lg-4 col-md-12 p-4 ">
            <div>
              <p className={`p-0 m-0 mb-2  ${classes.blueBoldText}`}>
                {courselandingPageResponse.signup_form.signup_title}
              </p>
                <form onSubmit={submitContact} className="my-0">
                <div className="form-group col-12 py-1">
                  <label
                    className={`control-label ${classes.formLabelText} fw-bold`}
                    htmlFor="name"
                  >
                    {/* Name{" "} */}
                    {courselandingPageResponse.signup_form.Name_lable}
                    <span className={`${classes.formLabelTextcolor}`}>*</span>
                  </label>
                  <input
                    className={`form-control ${classes.forminput} border-0 `}
                    type="text"
                    id="name"
                    name="name"
                    // placeholder="Name"
                    // required

                    value={formData.name}
                    onChange={handleChange}
                    onKeyPress={(event) => {
                      if (!/^[A-Za-z. ]$/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    
                  />
                    {formErrors.name && <div  className={`${classes.error_tooltip} mb-5`}>{formErrors.name}</div>}
                </div>

                <div className="form-group col-12 py-3 ">
                  <label
                    className={`control-label ${classes.formLabelText} fw-bold`}
                    htmlFor="email"
                  >
                    {/* Email{" "} */}
                    {courselandingPageResponse.signup_form.Email_lable}
                    <span className={`${classes.formLabelTextcolor}`}>*</span>
                  </label>
                  <input
                    className={`form-control ${classes.forminput} border-0`}
                    type="text"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                  />
                  {formErrors.email && <div  className={`${classes.error_tooltip}`}>{formErrors.email}</div>}
                </div>
                <div className="form-group col-12 py-1" >
                  <label
                    className={`control-label ${classes.formLabelText} fw-bold`}
                    htmlFor="number"
                  >
                    {courselandingPageResponse.signup_form.Mobile_lable}
                    <span className={`${classes.formLabelTextcolor}`}>*</span>
                  </label>
                  <div className="bg_phone border-0">
                  
    
                 <PhoneInput
                    country={"in"}
                    inputClass={`form-control ${classes.forminput} border-0`}
                    // disableDropdown
                    placeholder={""}
                    defaultCountry={"in"}
                    name= "number"
                    // searchPlaceholder ={""}
                    value = {formData.number}
                    onChange ={handleChange1}
                    inputProps={{
                      id: "number",

                      onKeyPress: (event) => {
                        if (!/[0-9]/.test(event.key) ) {
                          event.preventDefault();
                        }
                      },

                    }}
                    countryCodeEditable={false} 
                    // format={false} 
                />

                  {formErrors.number && <div  className={`${classes.error_tooltip}`}>{formErrors.number}</div>}

                  </div>
                </div>
                <div className="form-group col-12 py-3">
                  <label
                    className={`control-label ${classes.formLabelText} fw-bold`}
                    htmlFor="role"
                  >
                    {/* I am interested in programmes for{" "} */}
                    {courselandingPageResponse.signup_form.dropdown_lable}

                    <span className={`${classes.formLabelTextcolor}`}>*</span>
                  </label>
                  <select
                    className={`form-select ${classes.formselect} border-0`}
                    id="role"
                    name="role"
                    // required
                    value={formData.role}
                    onChange={handleChange}
                  >
                     <option value="Select">Select</option>
                      <option value="MySelf">My Self</option>
                      <option value="My Team">My Team</option>
                      <option value="My Organisation [L&D Responsibility]">
                        My organisation (L & D Responsibility)
                      </option>
                  </select>
                  {formErrors.role && <div className={`${classes.error_tooltip}`}>{formErrors.role}</div>}
                  
                </div>

                <div className={`mt-3 ${classes.disablePointer}`}>
                  <label style={{ fontSize: "13px" }} htmlFor="agree">
                    <input
                      defaultChecked={true}
                      className={classes.checkBox}
                      type="checkbox"
                      id="agree"
                      onClick={checkboxHandler}
                      onChange={checkboxHandler}
                    />
                    &ensp;
                    {/* {landingPage.lead_form_terms_content} */}
                    {courselandingPageResponse.signup_form.checkbox_text}
                  </label>
                </div>

                <div className="text-center">
                  <button
                    // disabled={!agree}
                    disabled={buttonDisabled}
                    className={`${classes.land_btn}  text-white mt-3`}
                    type="submit"
                  >
                    {courselandingPageResponse.signup_form.brochure_btn}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
{/* fold two  div */}
      

        <div id="Eligibility" className={`${classes.back_to_top} bg-white`}>
          <div className={`${classes.equalPadding} mx-auto `}>
            <div className="row py-3 px-lg-0 px-5 px-md-0 " >
              <div
                className={`col-lg-3 col-md-6 col-12  ${classes.rightBorder} mt-lg-4 mb-lg-4`}
              >
                <div className={`row ${classes.small_img}`}>
                  <div className={`col-lg-4 col-3 text-end`}>
                    <Image src={Group} alt="Card" width="0" height="0" />
                  </div>
                  <div className={`col-lg-8 col-9  p-0`}>
                    <p className={`p-0 m-0 ${classes.head_line}`} >
                      {" "}
                      { courselandingPageResponse.course_data.start_title}{" "}
                    </p>
                    <div className={``} >
                      <p className={`p-0 m-0 ${classes.new_date_line}`}>
                        {" "}
                        {courselandingPageResponse.course_data.start_date}
                      </p>
                      <p className={`p-0 m-0 ${classes.last_line}`}>
                      {courselandingPageResponse.course_data.last_date_apply_title}{" "}
                        {courselandingPageResponse.course_data.end_date}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`col-lg-3 col-md-6 col-12  ${classes.rightBorder} mt-lg-4 mb-lg-4`}
              >
                <div className={`row`}>
                  <div className="col-lg-4 col-3 text-end">
                    <Image
                      src={projectduration}
                      alt="Card"
                      width="0"
                      height="0"
                      // sizes="100vw"
                    />
                  </div>
                  <div className={`col-lg-8 col-9 p-0`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {courselandingPageResponse.course_data.duration_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {courselandingPageResponse.course_data.duration_in_weeks} Weeks,
                        Online
                      </p>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {courselandingPageResponse.course_data.duration_in_hrs} hours per
                        week
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`col-lg-3 col-12 col-md-6  ${classes.rightBorder} mt-lg-4 mb-lg-4 `}
              >
                <div className={`row`}>
                  <div className="col-lg-4 col-3 text-end">
                    <Image
                      src={RupessDocuments}
                      alt="Card"
                      width="0"
                      height="0"
                      // sizes="100vw"
                    />
                  </div>
                  <div className={`col-lg-8 col-9 p-0 pe-2`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {courselandingPageResponse.course_data.fee_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {" "}
                        INR {courselandingPageResponse.course_data.fee}
                      </p>
                      <p className={`p-0 m-0 ${classes.last_line}`}>
                      {/* View payment plan */}
                      <FinanceLinks handleShowPaymentModal={handleShowPaymentModal} courselandingPageResponse={courselandingPageResponse}/>
                    </p>

                 
                    </div>
                  </div>
                </div>
              </div>
              <div className={`col-lg-3 col-12 col-md-6 mt-lg-4 mb-lg-4 ${classes.rightBorde}`}>
                <div className={`row pt-2 pt-lg-0`}>
                  <div className="col-lg-4 col-3 text-end">
                    <Image
                      src={verfiedReport}
                      alt="Card"
                      width="0"
                      height="0"
                    />
                  </div>
                  <div className={`col-lg-8 col-9 p-0 `}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {" "}
                      {courselandingPageResponse.course_data.eligibility_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {" "}
                        {/* Any Graduate/ Diploma holder */}
                        {courselandingPageResponse.course_data.eligibility_content}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    
        <div ref={scrollDivRef}></div>
        {showSticky && (
        <div style={{ position: "sticky", top: "0", background: "#ECEEED", zIndex:"5" }} className={classes.hideButton}>
          <div className={`${classes.equalPadding} mx-auto`}>
            <div className="row py-3 px-lg-0 px-5 px-md-0">
              <div className={`col-lg-3 col-md-6 col-12  ${classes.rightBorder}`}>
                <div className={`row ${classes.small_img}`}>
                  <div className={`col-lg-4 col-3 text-end`}>
                    <Image src={Group} alt="Card" width="0" height="0" />
                  </div>
                  <div className={`col-lg-8 col-9  p-0`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {" "}
                      {courselandingPageResponse.course_data.start_title}{" "}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {" "}
                        {courselandingPageResponse.course_data.start_date}
                      </p>
                      <p className={`p-0 m-0 ${classes.last_line}`}>
                         {courselandingPageResponse.course_data.last_date_apply_title}{" "}
                        {courselandingPageResponse.course_data.end_date}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`col-lg-3 col-md-6 col-12  ${classes.rightBorder}`}
              >
                <div className={`row`}>
                  <div className="col-lg-4 col-3 text-end">
                    <Image
                      src={projectduration}
                      alt="Card"
                      width="0"
                      height="0"
                    />
                  </div>
                  <div className={`col-lg-8 col-9 p-0`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {courselandingPageResponse.course_data.duration_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {courselandingPageResponse.course_data.duration_in_weeks} Weeks,
                        Online
                      </p>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {courselandingPageResponse.course_data.duration_in_hrs} hours per
                        week
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`col-lg-3 col-12 col-md-6  ${classes.rightBorder}`}
              >
                <div className={`row`}>
                  <div className="col-lg-4 col-3 text-end">
                    <Image
                      src={verfiedReport}
                      alt="Card"
                      width="0"
                      height="0"
                      
                    />
                  </div>
                  <div className={`col-lg-8 col-9 p-0 pe-2`}>
                    <p className={`p-0 m-0 ${classes.head_line}`}>
                      {courselandingPageResponse.course_data.fee_title}
                    </p>
                    <div className={``}>
                      <p className={`p-0 m-0 ${classes.date_line}`}>
                        {" "}
                        INR {courselandingPageResponse.course_data.fee}
                      </p>
                      <p className={`p-0 m-0 ${classes.last_line}`}>

                      <FinanceLinks handleShowPaymentModal={handleShowPaymentModal} courselandingPageResponse={courselandingPageResponse}/>

                    </p>
                    
                
                    </div>
                  </div>
                
                </div>
              </div>
              <div className={`col-lg-3 col-12 col-md-6 align-center ${classes.centerAlignText}`}>
              <Link href="#form" scroll={false} className={classes.hideButton}>
              <button
                className={`${classes.nav_button} text-white btn m-0`}
                type="submit"
              >
                {courselandingPageResponse.header_and_sticy_cta}
              </button>
            </Link>
              </div>
            </div>
          </div>
        </div>
      )}

{/*New_fold  div */}

<div id="overview"  className={`${classes.light_bg} p-0 `}>
        <div className={`row m-0 mx-auto ${classes.equalPadding} py-4`}>

          <div className={`${classes.container_main} h-50 col-lg col-lg-6 col-md-12 p-0`}>
            <Image className={`${classes.h_w} `} src={group} alt="backgroundImage"></Image>
      <>
      {
                    !showIframe ? (
                      <div className={`${classes.first_div_landing} d-lg-none d-xl-block `}>
                        <div className={`${classes.centered_image} top-50`}>
                          <Image
                            onClick={handleImageClick1}
                            width={0}
                            height={0}
                            priority={true}
                            alt="Iframe_Image"
                            sizes="100vw"
                            style={{ width: "100%", height: "auto" }}
                            src={`${props.apiUrl}${courselandingPageResponse?.Iframe_fold?.Iframe_Image.data.attributes.formats.large.url}`}
                          ></Image>

                          <div className={classes.playIcon}>
                            <Image alt="" priority={true} onClick={handleImageClick1} height={62} width={62} src={playIcon}></Image>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className={`${classes.centered} ${classes.first_div_landing} top-50`}>
                        <iframe
                          className={`${classes.centered} `}
                          width="100%"
                          height="100%"
                          src={courselandingPageResponse?.Iframe_fold?.Iframe_url}
                          allowFullScreen={true}
                          allow="autoplay; fullscreen; picture-in-picture"
                        ></iframe>
                      </div>
                    )
                  }



          <div className={` ${classes.second_div_landing}  `}>
             <div className={`${classes.centered_image} top-50  `} >
              <div className={`${classes.video_box} `}>
              <div className={`${classes.video_overlays} `}>
                {!isImageHidden && (
                  <>
            <div style={{ position: 'fixed', width: '100%', height: '100%' }}>
                    <Image
                      onClick={handleImageClick}
                      width={0}
                      height={0}
                      priority={true}
                      sizes="100vw"
                      alt="Iframe_Image"
                      style={{ width: '100%', height: '100%' }}
                      src={`${props.apiUrl}${courselandingPageResponse?.Iframe_fold?.Iframe_Image.data.attributes.formats.large.url}`}
                    />
                    <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', zIndex: 1 }}>
                      <Image alt="" priority={true} onClick={handleImageClick} height={62} width={62} src={playIcon} />
                    </div>


            </div>
                  </>  
                )}
            </div>
              <div>
               
                <ReactPlayer
                url={courselandingPageResponse?.Iframe_fold?.Iframe_url}
                className="react-player"
                playing ={autoplay}
                width='100%'
                height='100%'
                controls={true}
              />
              </div>
              </div>
             </div>
          </div>


             </>   
          </div>
          <div className={`${classes.custom_top_margin} col`}>
          <h3 className={classes.bg_image_head}>{courselandingPageResponse?.Iframe_fold?.title}</h3>
          <h5>  {courselandingPageResponse.Iframe_fold?.small_description}</h5>
           
        <div className={`${classes.new_description}`}>
          {courselandingPageResponse.Iframe_fold?.description ? (
            <div dangerouslySetInnerHTML={{ __html: courselandingPageResponse.Iframe_fold?.description }} />
          ) : null}
          </div>

          </div>
        </div>
      </div>
   
    
      
{/* fold three  div */}

        <div className={`${classes.bluebackground} p-0 `}>
          <div
            className={`col-lg-12 col-md-12 col-12 mx-auto ${classes.equalPadding}`}
          >
            <div className="py-lg-4 py-2"></div>

            <div
              className={`row  align-items-center p-3 mx-lg-5 mx-md-2 mx-3`}
            >
              <div className="col-lg-6 col-sm-12 p-0"></div>
              {courselandingPageResponse.overview.map((overviewdata, index) => {
                return (
                  <div
                    key={index}
                    className="col-md-12 px-lg-3 px-md-0 px-0 "
                  >
                     <h3 className={`text-white ${classes.nwlTitle}`}>
                      {overviewdata.overview_title}
                    </h3>
                   
                    <div className={`text-align-left text-white  ${classes.newpara}`}>
                     {overviewdata.description ? (
                      <div  className={classes.highlights} dangerouslySetInnerHTML={{ __html: overviewdata.description }}  />
                    ) : null}
                    </div>


                    
                  </div>
                );
              })}
            </div>
            <div className="py-lg-4 py-2"></div>
          </div>
        </div>

<div className={`${classes.light_bg} p-0 m-0`}>
          <div className={`col-lg-12 col-md-12 col-12 mx-auto ${classes.equalPadding}`}>
            <h2 className={`text-center ${classes.main_blue_head} py-4 m-0`}>
              {courselandingPageResponse.new_highlights_title}
            </h2>
            <div className="row justify-content-center mx-2 px-lg-0" style={{ gap: "10px" }}>
              {selectedItems1.map((card, index) => (
                <div
                  key={index}
                  className={`p-0 col-lg-3 col-md-4 col-12 card`}
                >  
                  <div className="card-body">
                    <Image
                      src={props.apiUrl + card.image.data.attributes.url}
                      alt="Cardvg"
                      width="42"
                      height="41"
                      sizes="100vw"
                    />
                    <h2 className={`card-title text-black ${classes.highlightcards_title} pt-2`}>{card.title}</h2>
                    <div className={`card-title text-black ${classes.new_highlightcards_description} `} dangerouslySetInnerHTML={{ __html: card?.description }} />
                  </div>
                </div>
              ))}
             <p className={`px-lg-0 m-0 text-center ${classes.bdc_note_mobile}`} style={{fontStyle:"italic",fontFamily: "sans-serif"}}>*Details will be communicated ahead of the programme closure, subject to the availability of facilities.</p>
            </div>
          </div>
          <div className="py-2"></div>
        </div>
        
{/* fold four  div */}

        <div id="Highlights" className={`${classes.whitebackground} p-0 m-0`}>
          <div className={`col-lg-12 col-md-12 col-12 mx-auto ${classes.equalPadding}`}>
            <h2 className={`text-center ${classes.main_blue_head} py-4 m-0`}>
              {courselandingPageResponse.highlights_title}
            </h2>
            <div className="row justify-content-center mx-2 px-lg-0" style={{ gap: "10px" }}>
              {selectedItems.map((card, index) => (
                <div
                  key={index}
                  className={`p-0 col-lg-3 col-md-4 col-12 card`}
                >  
                  <div className="card-body">
                    <Image
                      src={props.apiUrl + card.image.data.attributes.url}
                      alt="Cardvg"
                      width="42"
                      height="41"
                      sizes="100vw"
                    />
                    <h2 className={`card-title text-black ${classes.highlightcards_title} pt-2`}>{card.title}</h2>
                    
                    <div className={`card-title text-black ${classes.new_highlightcards_description} `} dangerouslySetInnerHTML={{ __html: card?.description }}/>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="py-3"></div>
          <div className={`col-lg-12 col-md-12 col-12 p-0  mx-auto ${classes.equalPadding}`}
          >
            <div className={`${classes.card_main} px-4 px-lg-0 p-0 card `}>
              <div className={` d-flex justify-content-center mt-3 `}>
                <h2 className={`${classes.main_white_head}  `}>{courselandingPageResponse.achievements_title}</h2>
              </div>
              
              <div className="row d-flex justify-content-evenly">
                {courselandingPageResponse.achievements.map((ach, i) => {
                  return (
                 
                    <div key={i} className="col col-xs-3 col-sm-3 col-6">
                      <div className={`d-flex justify-content-center mt-3  `}>
                        <h3 className={` ${classes.rank} p-0 text-white`}>
                          {ach.rank_title}
                        </h3>
                      </div>
                      <div className={`text-center ${classes.rank_description}`}>
                        <p className=" py-2 text-white "> 
                        {ach.rank_description}
                      </p>
                    </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className=" d-flex justify-content-center">
              <button
                type="button"
                onClick={scrollToTop}
                className={`${classes.land_btn} btn btn-primary my-3 `}
              >
                {courselandingPageResponse.achievments_btn_title}
              </button>
            </div>
          </div>
        </div>

        <div className={`${classes.light_bg} p-0 `}>
        <div className={` m-0 mx-auto ${classes.equalPadding} py-4`}>
          <h2 className={`text-center ${classes.main_blue_head} py-4 m-0 `}>
            {courselandingPageResponse.Learning_Experience_heading}
            {/* Learning Experience */}
          </h2>
          {/* <div className="row "> */}
          <div   className="row d-lg-flex pe-0">
             
            <Slider {...settings_learning} className={`${classes} mb-4 `} >
                  {courselandingPageResponse.Exp_fold.map((ach, i) => {
                    return (
                  <div key={i}>
                  <div    className={`row m-0 mx-auto ${classes.equalPadding} py-4 d-lg-flex p-0 ${classes.first_div} `}> 
                    
                    <div  className={`col-lg-6 col-md-12 p-0 `} > 
                    <Image className={`${classes.h_w} `} src={group}   alt="backgroundImage" ></Image>

                    <div className={`${classes.centered_image}  `}>
                    <Image
                          width={0}
                          alt=""
                          height={0}
                          priority={true}
                          sizes="100vw"
                          className={` img-fluid w-100 h-auto px-2`}
                          style={{ width: "100%", height: "auto" }}
                          src={props.apiUrl + ach.image.data.attributes.url}></Image>
                    </div> 
                    </div>
                    <div className="col-lg-6 col-md-12 p-0  ">
                    <div className={`${classes}  `}>
                        <h3 className={`${classes.bg_image_head_title} text-black`}>{ach.title}</h3>
                        <h5 className={`${classes.new_text}`}>{ach.small_description} </h5>
                          <div className={`${classes.new_description}`}>
                          {ach?.description ? (
                            <div dangerouslySetInnerHTML={{ __html: ach?.description }} />
                          ) : null}
                      </div> 
                  </div>
                  </div> 

                  </div>

                  <div className={`row d-lg-flex p-0 ${classes.second_div}`}>
                    <div className={`${classes.container_main} h-50 col-lg  `}>
                      <Image className={`${classes.h_w} `} src={group}   alt="backgroundImage" ></Image>

                      <div className={`${classes.centered} top-50 `}>
                        <Image
                          // className="col-lg-6"
                          className={`${classes.centered} `}
                          src={props.apiUrl + ach.image.data.attributes.url}
                          width={420}
                          height={290}
                          alt="Learning Experience"
                          allowFullScreen
                        />
                      </div>
                    </div>
              
                    <div className={`${classes.custom_top_margin_fold}  col`}>
                      <h3 className={`${classes.bg_image_head_title} text-black`}>{ach.title}</h3>
                      <h5>{ach.small_description}</h5>

                    <div className={`${classes.new_description}`}>
                    {ach?.description ? (
                      <div dangerouslySetInnerHTML={{ __html: ach?.description }} />
                    ) : null}
                    </div>

                    </div>


                  </div>
                   </div> 
                    );
                })}
            </Slider>

            
 
       </div>
        </div>
      </div>



{/* fold Programme Courses  div */}

      <div className="px-2 bg-white">
        <div
          className={`col-lg-12 col-md-12 col-12 mx-auto p-2 ${classes.equalPadding}`}
        >
          <h2 className={`text-center ${classes.main_blue_head} py-4 m-0`}>
            {courselandingPageResponse.course_modules_title}
          </h2>

          {courselandingPageResponse.courses.data.attributes.syllabi.data.map(
            (item, i) => {
               
              return <Collapse key={i} index={i} {...item}  />;
            }
          )}

          <p className={`px-2 px-lg-0  ${classes.bdc_note_mobile}`} style={{fontStyle:"italic",fontFamily: "sans-serif"}}>{courselandingPageResponse.syllabus_note}</p>
        </div>
      </div>

   {/* fold five  div */}

      <div id="Faculty" className={`${classes} p-0 py-4`} style={{ position: "relative" }}>
      <Image
              src={profser_bg}
              alt="business_strategy_Image"
              layout="fill"
              objectFit="cover"
              objectPosition="center"
              priority
            />
                <div
                  className={`col-lg-12 col-md-12 col-12 mx-auto ${classes.equalPadding}`}  style={{ position: "relative", zIndex: 1 }}
                >
                  <h2 className={`text-center  ${classes.main_blue_head}  m-0`}>
                    {courselandingPageResponse.faculty_fold_title}
                  </h2>
                  <div>
                    <div className="row px-lg-0 px-3">
                    
                  <div className="col-12 p-0 py-3 px-2 ">
                      {courselandingPageResponse.professors.data.length >= 2 ? (  
                      
                      <Slider {...settings} ref={sliderRef}>
                          {professors.map((professor, i) => (
                            <ProfessorCard key={i} professor={professor} />
                          ))}
                        </Slider>
                    ) : ( 
                     <ProfessorCard key={0} professor={professors[0]} />
                    
                    )}
                  </div>
                    </div>
                  </div>
                </div>
      </div>


      <div className={`${classes.badgebackground} `}>
        <div
          className={`col-lg-12 col-md-12 col-12 mx-auto ${classes.equalPadding}`}
        >
          <h2 className={`text-center ${classes.main_blue_head} py-4 m-0`}>
            {courselandingPageResponse.bdc_title}
          </h2>
          <p className={`text-center ${classes.subheading} `}>
            {courselandingPageResponse.bdc_description}
          </p>

          <div>
            <div className="row align-items-center">
              {courselandingPageResponse.certificate.map((cert, index) => {
                const certif_Length = courselandingPageResponse.certificate.length
                return (
                  <div
                    key={index}
                    className={`${certif_Length === 3 ? "col-lg-4" : "col-lg-6 gap-3"} ${(certif_Length === 2 && index === 0) ? "d-flex justify-content-end" : (certif_Length === 2 && index === 1) ? "d-flex justify-content-start" : ""} col-12 p-2 text-center mt-3`}
                  >
                    <div className="image-container">
                      <Image
                        height={200}
                        width={200}
                        sizes="100vw"
                        className="img-fluid"
                        alt="card"
                        style={{ objectFit: "cover" }}
                        src={`${props.apiUrl}${cert.certificate_image.data.attributes.url}`}
                      />
                    </div>
                    <div className="mt-3 p-2 px-3">
                      <p className={classes.black_text}>
                        {cert.certificate_title}
                      </p>
                    </div>
                  </div>
                );
              })}
              <style jsx>{`
                        .image-container {
                          height: 280px;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                        }
                      `}</style>
            </div>
            <p className={`px-3 px-lg- 0 mt-2 ${classes.note_color, classes.bdc_note_mobile}`} style={{ fontStyle: "italic", fontFamily: "sans-serif" }}>
              {courselandingPageResponse.bdc_note}
            </p>
          </div>

        </div>

        <div className={`${classes.light_bg} p-0 `}>
          <div className={`mx-auto ${classes.equalPadding}  py-4`}>

            <div className=" d-flex justify-content-center">
              <button
                onClick={scrollToTop}
                type="button"
                className={`${classes.land_btn} btn btn-primary my-3 `}
              >

              {courselandingPageResponse.achievments_btn_title}
              </button>
            </div>
          </div>


          <Image
            className="img-fluid w-100 mt-4"
            src={banner}
            alt="banner_image"
          />
        </div>

      </div>


      <BottomFold data={props.bottomFoldData}></BottomFold>
      <div className={`mt-5 ${classes.showBanner}`}></div>

      <div className={`${classes.cookie_banner} ${classes.showBanner}`}>
        <button
          type="button"
          onClick={scrollToTop}
          className={`${classes.btnStyle} mx-auto`}
        >
          {courselandingPageResponse.header_and_sticy_cta}
        </button>
        <p className={`p-0 m-0 text-white pt-2 ${classes.stickyButtonText}`}>
           {courselandingPageResponse.course_data.last_date_apply_title}{" "}{courselandingPageResponse.course_data.end_date}
        </p>
      </div>
       {/*payment modal */}
      <PaymentModal courselandingPageResponse={courselandingPageResponse}  handleClosePaymentModal={handleClosePaymentModal} showPaymentModal={showPaymentModal} />
      <FinancingOptionModal clrp={courselandingPageResponse} pdfs={pdfsArray} handleClosePaymentModal={handleClosePaymentModal} showFinancOptionsModal={showFinancOptionsModal} selectedIndices={selectedIndices}/>
     
     
    </>
  );
}

export const getStaticPaths = async (context) => {
  const APIUrl = process.env.API_BASE_URL;
  const res = await fetch(`${APIUrl}/api/course-landingpages?populate=deep,3`);
  const response = await res.json();
 
  const paths = response.data.map((i) => {
   
    return {
      params: {
        courseid: i.attributes.course_id,
      },
    };
  });
  return {
    paths,
    fallback: false,
  };
};

export async function getStaticProps(context) {
  const meetupId = context.params.courseid;
  const APIUrl = process.env.API_BASE_URL;
  const Baseurl = process.env.NEXT_PUBLIC_BASE_URL;

  const [tracklandingpage, bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/course-landingpages?filters[course_id][$eq]=${meetupId}&populate=deep,5`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      apiData: tracklandingpage,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData,
      baseurl: Baseurl,
      meetupId:
        tracklandingpage.data[0].attributes.course_id,
    },
    revalidate: 120,
  };
}



const PaymentModal=({courselandingPageResponse, handleClosePaymentModal, showPaymentModal})=>{
  return(
    <div id="myModal" className={`${classes.fin_modal} d-${showPaymentModal ? "flex":"none"}`}>
        <div  className={`${classes.modal_content}`} style={{maxWidth:showPaymentModal?"900px":"0"}}>
       
       <div className={classes.payment_plan_div}>
        <div className="d-flex justify-content-between">
        
          <h2 className=" ">{courselandingPageResponse.Payment_plan_title}</h2>
              <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
        </div>
            <div className={classes.paymodal_body}>
              <div className={` `}>
                <div className={`${classes.programfee}`}>{courselandingPageResponse.Programme_fee_title}</div>
                <div className={classes.admin_price}>{courselandingPageResponse.Programme_fee}</div>
                <div className={classes.gst_text}>{courselandingPageResponse.Payment_plan_description}</div>

                {courselandingPageResponse.payment_plan.map((pay, index) =>{
                  return (
                    <div key={index} className={classes.payment_table_wrpr}>
                      <h6>{pay.PaymentScheduleTitle}</h6>
                      <table className={`${classes.table_style} table`}>
                        <thead>
                          <tr>
                            <th scope="col" className={` ${classes.bg_gray}`}>
                              {pay.payment_date_title}
                            </th>
                            <th scope="col" className={` ${classes.bg_gray}`}>
                              {pay.amount_due_title}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                        {pay.Particulars.map((par, index)=>{
                          return(
                            <tr key={index}>
                            <td>{par.Date}</td>
                            <td>{par.Fee}</td>
                          </tr> )}) }
                        </tbody>
                      </table>
                    </div>
                  )
                })
                }

              </div>
            </div>
      </div> 

        </div>

      </div>
  )
}

const FinancingOptionModal = ({clrp, pdfs, showFinancOptionsModal, selectedIndices, handleClosePaymentModal }) => {
  return (
    <div id="myModal" className={`${classes.fin_modal} d-${showFinancOptionsModal ? "flex" : "none"}`} >
      <div className={classes.modal_content}>

        <div className="financing_optns_div">
          <div className="d-flex justify-content-between">
            <h2>{clrp.financing_options.financing_options_title}</h2>
            <span className={classes.closebtn} onClick={handleClosePaymentModal}>&times;</span>
          </div>
          <div id="modal_content" className={classes.paymodal_body}>
            <div className={`${classes.contentext} container-md pt-4 pb-3 pb-md-4`}>
              <div className={`${classes.contentext_child} mx-auto`} >
              <div
                dangerouslySetInnerHTML={{
                  __html: clrp.financing_options.financing_options_desc,
                }}
              ></div>

              </div>
              <div className={"container-md p-0 m-0 d-flex justify-content-center"}>
                <div className="" style={{ maxWidth: "1100px" }}>

                  <div className={` `}>
                    <div className={`${classes1.iframe_main}`}>


                      <div className='row mx-auto d-flex justify-content-between ' style={{ maxWidth: "1000px", minHeight: "80vh" }}>

                        {pdfs.map((pdf, index) => (
                          <div key={index} className={`${selectedIndices.includes(index) ? "px-2" : "px-0"} pt-md-4 pb-4 ${index == 0 ? "pt-0" : ""} pb-0 col-md-4 col-sm-6 `} style={{ minWidth: "300px" }}>
                            <div className={`${classes1.pdf_viewer_main}`} id={"adobe-dc-view-" + pdf.fileName} style={{ width: '100%' }}></div>

                          </div>
                        ))}
                      </div>


                    </div>
                  </div>
                </div>
              </div>
              <div className="finance_contentext_child__nwUCn mx-auto"><p className="mb-0">{clrp.financing_options.footer_text} <a href={`mailto:${clrp.financing_options.contact_email}`} className='text-decoration-none'>{clrp.financing_options.contact_email}.</a></p></div>
            </div>

          </div>
        </div>

      </div>

    </div>
  )
}

const FinanceLinks=({handleShowPaymentModal, courselandingPageResponse})=>{
return(
  <>
     <a className={`${classes.financing_options_link}`} id={"payment_modal"}  onClick={(e) => handleShowPaymentModal(e)}>{courselandingPageResponse.course_data.view_paymet_plan}</a><br/>
      <a className={`${classes.financing_options_link}`} id={"financing_options"}  onClick={(e) => handleShowPaymentModal(e)}>{courselandingPageResponse.course_data.enterprise_pricing}</a>
  </>
)
}