export const MIN_NAME_LENGTH = 3;

export const BrochureFileNameFormatter = (meetupId) => {
    return meetupId
      .replace(/-/g, "_") 
      .replace(/^./, (char) => char.toUpperCase());
  };

export const createQueryString = (query) => {
    return Object.entries(query)
      .filter(([key, value]) => value !== undefined && value !== null && value !== '' && key !== 'learning_track_id')
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
  };

  export const capitalizeFirstLetter = (str) => {
    return str ? str.charAt(0).toUpperCase() + str.slice(1).toLowerCase() : "";
  };
  

export const TopicsComponent = ({ Topics }) => {
  return (
    <span>
      {Topics && Topics.length === 2 ? (
        <span>
          {Topics[0].attributes.topic_name} + {Topics[1].attributes.topic_name}
        </span>
      ) : (
        Topics.map((topic, index) => (
          <span key={index}>
            {topic.attributes.topic_name}
            {index === Topics.length - 2 ? " & " : index < Topics.length - 2 ? " + " : ""}
          </span>
        ))
      )}

    </span>
  )
}