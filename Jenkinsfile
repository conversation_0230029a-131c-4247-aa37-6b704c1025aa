pipeline {
    agent {
        label 'ISB_Storefront_Label'
    }
    environment {
        SERVICE_NAME = 'Storefront_ISB'
        RECIPIENTS = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,rah<PERSON><PERSON><PERSON>@quantana.in'
    }
   
    stages {
        stage('Backend') {
            steps {
               sh '''
                #!/bin/sh
                cd /usr/isb_storefront_server/
                git stash
                git pull origin main
                npm install --force
                npm run build
                pm2 restart 0
                '''
            }
        }
    }
    post {
      always {
        emailext body: "${currentBuild.currentResult}: Job ${env.JOB_NAME} build ${env.BUILD_NUMBER}\n More info at: ${env.BUILD_URL}",
                recipientProviders: [developers(), upstreamDevelopers(), culprits()],
                subject: "Jenkins Build ${currentBuild.currentResult}: Job ${env.JOB_NAME}",
                attachLog: true,
                to: "$RECIPIENTS"
      }
    }
}
