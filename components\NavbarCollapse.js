'use client'
import React, { useEffect, useState } from "react";
import { Nav } from "react-bootstrap";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCaretDown } from "@fortawesome/free-solid-svg-icons";
import classes from "../pages/index.module.css";
import { useRouter } from "next/router";
import { capitalizeFirstLetter } from "../utils";


const NavbarCollapse = (props) => {
  const { qpms } = props;
  const router = useRouter();


  const [navData, setProgrammes] = useState(null);
  const [loginButtonFlag, setLoginButtonFlag] = useState(false);
  useEffect(() => {
    fetchData()
    const pathName = window.location.pathname
    if(pathName.startsWith('/v2/')){
      setLoginButtonFlag(true)
    }else{
      setLoginButtonFlag(false)
    }
  }, [])

 // Send navData to parent component whenever it changes
 useEffect(() => {
  if (navData && props.onNavDataLoaded) {
    props.onNavDataLoaded(navData);
  }
}, [navData, props.onNavDataLoaded]);

  const fetchData = async () => {
    const url = process.env.NEXT_PUBLIC_API_BASE_URL;
    const res = await fetch(`${url}/api/navbars?populate=deep,4`);
    const data = await res.json();
    setProgrammes(data?.data[0].attributes);


  };
const [itemClicked,setItemClicked]=useState(false)
const handleDropdownItemClicked =()=>{
   
    setItemClicked(true)
    props.toggleDropdown()
   
}
const handleNavItemClicked = () => {
  props.toggleDropdown()
}
const [isMobile, setIsMobile] = useState(false);

const [elementClicked, setPathClicked] = useState({ perspective: false, about: false });
const pathname = router.pathname;

useEffect(() => {
  if (pathname.includes("perspectives")) {
    setPathClicked({ perspective: true, about: false }); 
  } else if (pathname.includes("about-page")) {
    setPathClicked({ perspective: false, about: true }); 
  } else {
    setPathClicked({ perspective: false, about: false }); 
  }
}, [pathname]);

useEffect(() => {
  const checkIfMobile = () => {
    setIsMobile(window.innerWidth <= 768);
  };
  
  // Check initially
  checkIfMobile();
  
  // Add event listener
  window.addEventListener('resize', checkIfMobile);
  
  // Cleanup
  return () => window.removeEventListener('resize', checkIfMobile);
}, []);

const handleDropdownHover = () => {
  setItemClicked(false); // Resets dropdown state when hovered again
};

const programDropdown=()=>{
  const itemsPerColumn = 4;
  const programs = navData?.programs_tab_title.programs || [];

  return(
    <div className={`${classes.dropdown}`} onMouseEnter={handleDropdownHover}>
    <div className={`pointer ${classes.para}`}>
      <div className={classes.dropbtn}>
        {navData && navData.programs_tab_title.programs_title} <FontAwesomeIcon icon={faCaretDown} className="ms-1" />
      </div>
      <div className={`${classes.dropdowncontent} d-${itemClicked ? 'none' : ''}`}>
        <div className={classes.dropdownInner}>
          {[0, 1, 2].map((columnIndex) => {
            const columnItems = programs.slice(columnIndex * itemsPerColumn, (columnIndex + 1) * itemsPerColumn);
            // Only render column if it has items
            return columnItems.length > 0 ? (
              <div key={columnIndex} className={classes.dropdownColumn}>
                {columnItems.map((item, index) => (
                  <Link 
                    key={index} 
                    href={`${item.url}${qpms}`}
                    onClick={() =>handleDropdownItemClicked()}
                    className={classes.dropdownItem}
                  >
                    {item.program_name}
                  </Link>
                ))}
              </div>
            ) : null;
          })}
        </div>
      </div>
    </div>
  </div>
  )
}
  const programdropdownMobile = () => {
    return (
       
        <div className={`${classes.dropdownmobile}`} onMouseEnter={() => setItemClicked(false)}>
          <div className={`pointer ${classes.para}`}>
            <div className={classes.dropbtnmobile}>
              {navData && navData.programs_tab_title.programs_title} <FontAwesomeIcon icon={faCaretDown} className="ms-1" />
            </div>
          </div>
          <div className={`${classes.dropdowncontentmobile} d-${itemClicked ? 'none' : ''}`}>
            {navData && navData.programs_tab_title.programs.map((i, index) => {
              return (
                <Link key={index} href={`${i.url}${qpms ? qpms : ""}`} onClick={() => handleDropdownItemClicked()} className={classes.dropdownItem}>
                  {i.program_name}
                </Link>
              );
            })}
            
          </div>
        </div>
       
    )
  }
  const currentQuery = router.asPath.includes("?") ? router.asPath.split("?")[1] : "";
  const processedQuery = () => {
    if (!currentQuery) return "";
    
    // Create a URLSearchParams object from the current query
    const params = new URLSearchParams(currentQuery);
    
    // Remove 'type' parameter entirely, regardless of its value
    params.delete('type');
    params.delete('topic');
    
    // Convert back to string and return
    const queryString = params.toString();
    return queryString ? queryString : "";
  };

  return (
   <>
     {navData && <>
     <Nav className={`${isMobile?"text-right": "text-right gap-md-3 gap-0 align-items-md-center align-items-start"} ${classes.navbarmain}`} id="myNavItem">
        <Nav.Link as="span">
        {isMobile ? programdropdownMobile() : programDropdown()}
        </Nav.Link> 
         <span className="d-none d-md-inline">|</span>
        <Nav.Link as="span">
           <Link  href={navData ? navData.academic_solutions_tab.href:""} className={classes.dropdown} onClick={handleNavItemClicked}>
            <div className={`${classes.dropdownItem}`}>{navData && navData.academic_solutions_tab.title}</div>
          </Link>
        </Nav.Link>  <span className="d-none d-md-inline">|</span>
        <Nav.Link as="span">
        <Link replace  href={`/perspectives${processedQuery() ? `?${processedQuery()}` : ""}`} className={classes.dropdown} onClick={handleNavItemClicked}>
            <div  className={`${classes.dropdownItem} ${elementClicked.perspective ? classes.navelementStyl:""}`} >{navData && navData.edge_title}</div>
          </Link>
        </Nav.Link>  <span className="d-none d-md-inline">|</span>
        <Nav.Link as="span">
          <div className={classes.dropdown}>
            <Link className="text-decoration-none"  href={`/about-page${qpms}`} onClick={handleNavItemClicked}>  
            <div className={`${classes.dropdownItem} ${elementClicked.about ? classes.navelementStyl:""}`} >{navData && navData.about_title}  </div>
            </Link>
                        
          </div>
        </Nav.Link>




        {/* {loginButtonFlag && <Nav.Link as="span" className="p-0 d-flex align-items-center ps-lg-2">
          <div >
            <Link className="text-decoration-none"
            href={process.env.NEXT_PUBLIC_BASE_URL==="https://online.isb.edu" ? process.env.NEXT_PUBLIC_ISBO_PROD_URL :process.env.NEXT_PUBLIC_ISBO_STAGING_URL}> <div className={classes.dropbtn}><button className={classes.login_button}>Login</button> </div></Link>
                       
          </div>
        </Nav.Link>} */}
        {elementClicked.perspective && <div  className={`${classes.navrighttitle}   d-none d-md-flex`}>{navData && navData.edge_title
      ? capitalizeFirstLetter(navData.edge_title)
      : ""}</div>}

      </Nav>
      </>
      }
   </>
  );
};


export default NavbarCollapse;
