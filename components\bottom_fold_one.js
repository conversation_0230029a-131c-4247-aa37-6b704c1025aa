import { useRouter } from "next/router";
import Link from "next/link";
import Image from "next/image";
import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import "@fortawesome/fontawesome-svg-core/styles.css";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";

import kellog from "../assets/kellogg_logo.svg";
import wharton from "../assets/Wharton_logo.svg";

import london from "../assets/London_logo.svg";
import fletcher from "../assets/Fletcher_logo.svg";

import facebook_Icon from "../assets/Facebook_Icon.svg";
import twitter_Icon from "../assets/X_Icon.svg";
import linkedin from "../assets/LinkedIn_Icon.svg";
import youtube from "../assets/YouTube_Icon.svg";
import aacsb from "../assets/aacsb_logo.svg";
import amba from "../assets/amba_logo.svg";
import equis from "../assets/equis_logo.svg";
import classes from "./bottom_fold_one.module.css";
import useCollapse from "react-collapsed";
import Head from "next/head";

export default function BottomFold(props) {
  const router = useRouter();
  let programData = props?.navData?.programs_tab_title.programs;
  let navData = props?.navData;
  const {  query } = router;
  let trackpage = props?.trackpage;

  
  const socialMedia = props.data.data.attributes.social_handles;

  const qpms =
  query.utm_source != undefined
    ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}`
    : ``;


  const socialMediaArray = [
    {
      name: 'YouTube',
      url: socialMedia.youtube_url,
      icon: youtube
    },
    {
      name: 'Twitter',
      url: socialMedia.twitter_url,
      icon: twitter_Icon
    },
    {
      name: 'LinkedIn',
      url: socialMedia.linkedin_url,
      icon: linkedin
    },
    {
      name: 'Facebook',
      url: socialMedia.facebook_url,
      icon: facebook_Icon
    }    
  ];
  
  const C = (
    <FontAwesomeIcon
      icon={faMinus}
      className="fa-solid fa-minus"
      style={{ color: "white" }}
    />
  );
  const E = (
    <FontAwesomeIcon
      icon={faPlus}
      className="fa-solid fa-plus"
      style={{ color: "white" }}
    />
  );

  const data = props.data.data.attributes;

  const Collapse = (props) => {
    const { getCollapseProps, getToggleProps, isExpanded } = useCollapse();
    return (
      <>
        <div>
          <button
            className={`${classes.faqQuestion} ${trackpage ? "text-start":""} d-flex justify-content-between text-white m-0 mb-2`}
            {...getToggleProps()}
          >
            {props.faq_question}
            {isExpanded ? C : E}
          </button>
          <div {...getCollapseProps()}>
            <div
              className={`${classes.faqAnswer} p-0 m-0 text-white ${classes.liStyle}`}
              dangerouslySetInnerHTML={{
                __html: props.faq_answer,
              }}
            ></div>
          </div>
        </div>
        <div className={`mb-3 p-0 ${classes.foottitle}`}></div>
      </>
    );
  };
 

  function generateFAQSchema(faqs) {
    const faqData = faqs.map((item) => ({
      "@type": "Question",
      name: item.faq_question,
      acceptedAnswer: {
        "@type": "Answer",
        text: item.faq_answer
      }
    }));
  
    const schema = {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      mainEntity: faqData,
    };
    
    return schema;
  }
  const [itemClicked,setItemClicked]=useState(false)
  const handleDropdownItemClicked =()=>{
     
      setItemClicked(!itemClicked)
      // props.toggleDropdown()
     
  }
  
  const acedemicData = {title:navData?.academic_solutions_tab?.title, url:navData?.academic_solutions_tab?.href}

  return (
    <>
     <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(generateFAQSchema(data.faq)),
          }}
        />
      </Head>
      {props.faqsection==false || router.pathname == `/cs-tq` || router.pathname == `/coming-soon` || router.pathname == `/perspectives` || router.pathname == `/lp/[learning_track_id]` || router.pathname=="/lp/course/[courseid]"  ? (
        <div></div>
      ) : (
        <div id="FAQs" className={`${classes.footerrow} row`}>
          {/* <div className="col-md-1"></div> */}
          <div
            className={`col-md-12 ${classes.equalPadding} mx-auto px-lg-0 px-3`} style={{maxWidth: props.trackpage ?"var(--isb-container-max-width)" :""}}
          >
            <div className="row">
              <div className={"col-lg-12 left-footer px-3 px-md-2"}>
                <div className="d-flex justify-content-between flex-row">
                  <b className={classes.allfaqHeading}>Top FAQs</b>
                  {router.pathname == `/landing-page` ||
                  router.pathname == `/coming-soon` ||
                  router.pathname == `/lp/[learning_track_id]`  ? (
                    <div></div>
                  ) : (
                    <Link className={classes.allfaqHeading} href="/faq-page">
                      <p className="text-white">
                        <b>All FAQs</b>
                        <svg
                          width="30"
                          height="12"
                          viewBox="0 0 15 13"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.6225 12.8462L7.54201 11.7797L11.9903 7.33147H0.469696V5.78791H11.9903L7.54201 1.35369L8.6225 0.273194L14.909 6.55969L8.6225 12.8462Z"
                            fill="white"
                          />
                        </svg>{" "}
                      </p>
                    </Link>
                  )}
                </div>
                {data.faq.map((item) => {
                  return <Collapse key={item.id} {...item} />;
                })}
              </div>
            </div>
            <div className="row">
              <div className="col-12 px-3 px-md-2">
                <div className="d-flex justify-content-between flex-row">
                  <div className={`mt-2 ${classes.allfaqHeading}`}>
                    Indian School of Business <br></br> Hyderabad | Mohali
                  </div>
                  <a href={`mailto:${data.contact_us_email}`} className={`mt-2 ${classes.contactUsCard}`}>
                    <b>Contact Us</b><br></br> 
                    <svg
                      fill="#ffffff"
                      width="18"
                      height="18"
                      viewBox="0 0 512 512"
                      xmlns="http://www.w3.org/2000/svg"
                    > 
                      <title>mail</title>
                      <path d="M64 128Q64 113 73 105 81 96 96 96L416 96Q431 ************** 113 448 128L448 144 256 272 64 144 64 128ZM256 328L448 200 448 384Q448 416 416 416L96 416Q64 416 64 384L64 200 256 328Z" />
                    </svg>
                    {data.contact_us_email}
                  </a>
                </div>
              </div>
            </div>
            {router.pathname == `/landing-page` ||
            router.pathname == `/coming-soon` ||
            router.pathname == `/lp/[learning_track_id]`  || 
            // router.pathname == `/landingpage` 
            router.pathname == `/lp/[learning_track_id]/thankyou` ? (
              <div></div>
            ) : (
              <div className="col-lg-12 left-footer d-flex justify-content-between px-1">
                <div className="d-flex gap-2 mt-2">
                  {/* Facebook */}
                  <a
                    aria-label="facebook"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={data.social_handles.facebook_url}
                  >
                    {" "}
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10Z"
                        fill="white"
                      />
                      <path
                        d="M15 10C15 7.25 12.75 5 10 5C7.25 5 5 7.25 5 10C5 12.5 6.8125 14.5625 9.1875 14.9375V11.4375H7.9375V10H9.1875V8.875C9.1875 7.625 9.9375 6.9375 11.0625 6.9375C11.625 6.9375 12.1875 7.0625 12.1875 7.0625V8.3125H11.5625C10.9375 8.3125 10.75 8.6875 10.75 9.0625V10H12.125L11.875 11.4375H10.6875V15C13.1875 14.625 15 12.5 15 10Z"
                        fill="#057092"
                      />
                    </svg>
                  </a>
                  {/* Twitter */}
                  <a
                    aria-label="twitter"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={data.social_handles.twitter_url}
                  >
                   <svg width="20" height="20" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 15C0 23.2843 6.71573 30 15 30C23.2843 30 30 23.2843 30 15C30 6.71573 23.2843 0 15 0C6.71573 0 0 6.71573 0 15Z" fill="white"/>
<path d="M20.3594 8H9.64062C8.73453 8 8 8.73453 8 9.64062V20.3594C8 21.2655 8.73453 22 9.64062 22H20.3594C21.2655 22 22 21.2655 22 20.3594V9.64062C22 8.73453 21.2655 8 20.3594 8Z" fill="white"/>
<path d="M18.391 8.97656H20.3449L16.0761 13.8555L21.098 20.4947H17.1659L14.0862 16.4681L10.5622 20.4947H8.6071L13.173 15.2761L8.35547 8.97656H12.3874L15.1712 12.657L18.391 8.97656ZM17.7052 19.3252H18.7879L11.7991 10.0847H10.6372L17.7052 19.3252Z" fill="#057092"/>
</svg>
                  </a>
                  {/* Youtube */}
                  <a
                    aria-label="youtube"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={data.social_handles.youtube_url}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10Z"
                        fill="white"
                      />
                      <path
                        d="M14.75 7.5625C14.625 7.125 14.3125 6.8125 13.875 6.6875C13.125 6.5 9.9375 6.5 9.9375 6.5C9.9375 6.5 6.8125 6.5 6 6.6875C5.5625 6.8125 5.25 7.125 5.125 7.5625C5 8.375 5 10 5 10C5 10 5 11.625 5.1875 12.4375C5.3125 12.875 5.625 13.1875 6.0625 13.3125C6.8125 13.5 10 13.5 10 13.5C10 13.5 13.125 13.5 13.9375 13.3125C14.375 13.1875 14.6875 12.875 14.8125 12.4375C15 11.625 15 10 15 10C15 10 15 8.375 14.75 7.5625ZM9 11.5V8.5L11.625 10L9 11.5Z"
                        fill="#057092"
                      />
                    </svg>
                  </a>
                  {/* LinkedIn */}
                  <a
                    aria-label="linkedin"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={data.social_handles.linkedin_url}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10Z"
                        fill="white"
                      />
                      <path
                        d="M7.25 15H5.125V8.3125H7.25V15ZM6.1875 7.375C5.5 7.375 5 6.875 5 6.1875C5 5.5 5.5625 5 6.1875 5C6.875 5 7.375 5.5 7.375 6.1875C7.375 6.875 6.875 7.375 6.1875 7.375ZM15 15H12.875V11.375C12.875 10.3125 12.4375 10 11.8125 10C11.1875 10 10.5625 10.5 10.5625 11.4375V15H8.4375V8.3125H10.4375V9.25C10.625 8.8125 11.375 8.125 12.4375 8.125C13.625 8.125 14.875 8.8125 14.875 10.875V15H15Z"
                        fill="#057092"
                      />
                    </svg>
                  </a>
                  {/* Instagram */}
                  <a
                    aria-label="instagram"
                    target="_blank"
                    rel="noopener noreferrer"
                    href={data.social_handles.instagram_url}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 10C0 15.5228 4.47715 20 10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10Z"
                        fill="white"
                      />
                      <path
                        d="M10 5.75C11.375 5.75 11.5625 5.75 12.125 5.75C12.625 5.75 12.875 5.875 13.0625 5.9375C13.3125 6.0625 13.5 6.125 13.6875 6.3125C13.875 6.5 14 6.6875 14.0625 6.9375C14.125 7.125 14.1875 7.375 14.25 7.875C14.25 8.4375 14.25 8.5625 14.25 10C14.25 11.4375 14.25 11.5625 14.25 12.125C14.25 12.625 14.125 12.875 14.0625 13.0625C13.9375 13.3125 13.875 13.5 13.6875 13.6875C13.5 13.875 13.3125 14 13.0625 14.0625C12.875 14.125 12.625 14.1875 12.125 14.25C11.5625 14.25 11.4375 14.25 10 14.25C8.5625 14.25 8.4375 14.25 7.875 14.25C7.375 14.25 7.125 14.125 6.9375 14.0625C6.6875 13.9375 6.5 13.875 6.3125 13.6875C6.125 13.5 6 13.3125 5.9375 13.0625C5.875 12.875 5.8125 12.625 5.75 12.125C5.75 11.5625 5.75 11.4375 5.75 10C5.75 8.5625 5.75 8.4375 5.75 7.875C5.75 7.375 5.875 7.125 5.9375 6.9375C6.0625 6.6875 6.125 6.5 6.3125 6.3125C6.5 6.125 6.6875 6 6.9375 5.9375C7.125 5.875 7.375 5.8125 7.875 5.75C8.4375 5.75 8.625 5.75 10 5.75ZM10 4.8125C8.5625 4.8125 8.4375 4.8125 7.875 4.8125C7.3125 4.8125 6.9375 4.9375 6.625 5.0625C6.3125 5.1875 6 5.375 5.6875 5.6875C5.375 6 5.25 6.25 5.0625 6.625C4.9375 6.9375 4.875 7.3125 4.8125 7.875C4.8125 8.4375 4.8125 8.625 4.8125 10C4.8125 11.4375 4.8125 11.5625 4.8125 12.125C4.8125 12.6875 4.9375 13.0625 5.0625 13.375C5.1875 13.6875 5.375 14 5.6875 14.3125C6 14.625 6.25 14.75 6.625 14.9375C6.9375 15.0625 7.3125 15.125 7.875 15.1875C8.4375 15.1875 8.625 15.1875 10 15.1875C11.375 15.1875 11.5625 15.1875 12.125 15.1875C12.6875 15.1875 13.0625 15.0625 13.375 14.9375C13.6875 14.8125 14 14.625 14.3125 14.3125C14.625 14 14.75 13.75 14.9375 13.375C15.0625 13.0625 15.125 12.6875 15.1875 12.125C15.1875 11.5625 15.1875 11.375 15.1875 10C15.1875 8.625 15.1875 8.4375 15.1875 7.875C15.1875 7.3125 15.0625 6.9375 14.9375 6.625C14.8125 6.3125 14.625 6 14.3125 5.6875C14 5.375 13.75 5.25 13.375 5.0625C13.0625 4.9375 12.6875 4.875 12.125 4.8125C11.5625 4.8125 11.4375 4.8125 10 4.8125Z"
                        fill="#057092"
                      />
                      <path
                        d="M10 7.3125C8.5 7.3125 7.3125 8.5 7.3125 10C7.3125 11.5 8.5 12.6875 10 12.6875C11.5 12.6875 12.6875 11.5 12.6875 10C12.6875 8.5 11.5 7.3125 10 7.3125ZM10 11.75C9.0625 11.75 8.25 11 8.25 10C8.25 9.0625 9 8.25 10 8.25C10.9375 8.25 11.75 9 11.75 10C11.75 10.9375 10.9375 11.75 10 11.75Z"
                        fill="#057092"
                      />
                      <path
                        d="M12.75 7.875C13.0952 7.875 13.375 7.59518 13.375 7.25C13.375 6.90482 13.0952 6.625 12.75 6.625C12.4048 6.625 12.125 6.90482 12.125 7.25C12.125 7.59518 12.4048 7.875 12.75 7.875Z"
                        fill="#057092"
                      />
                    </svg>
                  </a>
                  <div></div>
                </div>
              </div>
            )}
          </div>
          {/* <div className="col-md-1"></div> */}
        </div>
      )}
      <div className={`${classes.footerrowWhite} mx-auto`}>
        {router.pathname == `/landing-page` ||
        router.pathname == `/coming-soon` ||
        router.pathname == `/cs-tq` || 
        router.pathname == `/lp/course/[courseid]`
        || router.pathname == `/me_thankyou` ? (
          <div className={`row ${classes.equalPadding} mx-auto text-center`}>
            <div
              className={`col-lg-3 col-md-6 col-5 p-0 ${classes.sponsorBorder}`}
            >
              <div
                className={`${classes.sponsorFont} ${classes.sponsorTextAlign}`}
              >
                Founding Associate Schools
              </div>
              <div className="row d-flex justify-content-center">
                <a className="col-md-6 col-5 mt-4 p-0">
                  <Image
                    className="img-fluid"
                    src={kellog}
                    alt="Kellog Logo"
                    height={36}
                  />
                </a>
                <a className="col-md-6 col-5 mt-3 p-0">
                  <Image
                    className="img-fluid"
                    src={wharton}
                    alt="Wharton Logo"
                    height={63}
                  />
                </a>
              </div>
            </div>
            <div
              className={`col-lg-3 col-md-6 col-5 ${classes.sponsorBorder} p-0`}
            >
              <div
                className={`${classes.sponsorFont} ${classes.sponsorTextAlign} px-lg-4 px-0`}
              >
                Associate Schools
              </div>
              <div className="row d-flex justify-content-center">
                <a className="col-lg-6 col-md-6 col-5 mt-3 p-0">
                  <Image
                    className="img-fluid"
                    src={london}
                    alt="London logo"
                    height={48}
                  />
                </a>
             
                <a className="col-lg-6 col-md-6 col-12 mt-3 p-0" height={98}>
                  <Image
                    className="img-fluid"
                    src={fletcher}
                    alt="Fletcher Logo"
                  />
                </a>
              </div>
            </div>
            <div className="col-lg-4 col-md-6 col-12 p-0">
              <div
                className={`${classes.sponsorFont} ${classes.sponsorTextAlign} px-lg-4 px-0 pt-lg-0 pt-3`}
              >
                Accreditation{" "}
              </div>
              <div className="row d-flex justify-content-center p-0">
                <a className="col-lg-4 col-md-4 col-5 mt-3 p-0">
                  <Image
                    className="img-fluid"
                    src={aacsb}
                    alt="AACSB Logo"
                    height={65}
                  />
                </a>
                <a className="col-lg-4 col-md-4 col-5 mt-3 p-0">
                  <Image
                    className="img-fluid"
                    src={amba}
                    alt="AMBA Logo"
                    height={65}
                  />
                </a>
                <a className="col-lg-4 col-md-4 col-5 mt-3 p-0">
                  <Image
                    className="img-fluid"
                    src={equis}
                    alt="Equis"
                    height={95}
                  />
                </a>
              </div>
            </div>
            <div className={`${classes.footerFinal} mt-3`}></div>
            <div className="d-flex justify-content-between p-0 mt-1">
              <p className={classes.bottomLink}>ISB.EDU © Copyright 2025</p>
              {router.pathname !== "/lp/[learning_track_id]" && 
              router.pathname !== "/me_thankyou" && 
              router.pathname !== "/lp/course/[courseid]" ? (
              <div className={`d-flex gap-3`}>
                {data.other_links.map((item) => {
                  return (
                    <a style={{ textDecoration: "None" }} key={item.id}>
                      <p className={`${classes.bottomLink} text-dark`}>
                        <b>{item.title}</b>
                      </p>
                    </a>
                  );
                })}
               
              </div>
               ) : null}
            </div>
          </div>
        ) : (
          <div className={`row ${classes.equalPadding} mx-auto text-center gap-md-0 gap-4 `} style={{maxWidth: props.trackpage ?"var(--isb-container-max-width)" :""}}>
            <div
              className={`col-lg-4 col-md-6 col-12 p-0 ${classes.sponsorBorder}`}
            >
              <div
                className={`${classes.sponsorFont} ${classes.sponsorTextAlign}`}
              >
                Founding Associate Schools
              </div>
              <div className="row d-flex justify-content-center">
                <a
                  className="col-md-6 col-5 mt-4 p-0"
                  target="_blank"
                  rel="noopener noreferrer"
                  href={data.schools.kellog_url}
                >
                  <Image
                    className="img-fluid"
                    src={kellog}
                    alt="Kellog Logo"
                    height={36}
                  />
                </a>
                <a
                  className="col-md-6 col-5 mt-3 p-0"
                  target="_blank"
                  rel="noopener noreferrer"
                  href={data.schools.wahrton_url}
                >
                  <Image
                    className="img-fluid"
                    src={wharton}
                    alt="Wharton Logo"
                    height={63}
                  />
                </a>
              </div>
            </div>
            <div
              className={`col-lg-4 col-md-6 col-12 ${classes.sponsorBorder} p-0 pb-4`}
            >
              <div
                className={`${classes.sponsorFont} ${classes.sponsorTextAlign} px-lg-4 px-0`}
              >
                Associate Schools
              </div>
              <div className="row d-flex justify-content-around">
                <a
                  className="col-lg-6 col-md-6 col-5 mt-3 p-0"
                  target="_blank"
                  rel="noopener noreferrer"
                  href={data.schools.london_business_url}
                >
                  <Image
                    className="img-fluid"
                    src={london}
                    alt="London logo"
                    height={48}
                  />
                </a>
                <a
                  className="col-lg-6 col-md-6 col-5 mt-3 p-0"
                  target="_blank"
                  rel="noopener noreferrer"
                  href={data.schools.fletcher_url}
                  height={98}
                >
                  <Image
                    className="img-fluid"
                    src={fletcher}
                    alt="Fletcher Logo"
                  />
                </a>
              </div>
            </div>
            <div className="col-lg-4 col-md-6 col-12 p-0">
              <div
                className={`${classes.sponsorFont} ${classes.sponsorTextAlign} px-lg-4 px-0 pt-lg-0 pt-3`}
              >
                Accreditation{" "}
              </div>
              <div className="row p-0 d-flex justify-content-around">
                <a
                  className="col-lg-4 col-md-4 col-5 mt-3 p-0"
                  target="_blank"
                  rel="noopener noreferrer"
                  href={data.schools.aacsb_url}
                >
                  <Image
                    className="img-fluid"
                    src={aacsb}
                    alt="AACSB Logo"
                    height={65}
                  />
                </a>
                <a
                  className="col-lg-4 col-md-4 col-5 mt-3 p-0"
                  target="_blank"
                  rel="noopener noreferrer"
                  href={data.schools.amba_url}
                >
                  <Image
                    className="img-fluid"
                    src={amba}
                    alt="AMBA Logo"
                    height={65}
                  />
                </a>
                <a
                  className="col-lg-4 col-md-4 col-5 mt-3 p-0"
                  target="_blank"
                  rel="noopener noreferrer"
                  href={data.schools.equis_url}
                >
                  <Image
                    className="img-fluid"
                    src={equis}
                    alt="Equis"
                    height={95}
                  />
                </a>
              </div>
            </div>
            <div className={`${classes.footerFinal} mt-3`}></div>


            {/* new footer elements */}
           {/* {programData&&  <div className="px-0">
              <div className={`${classes.programList} row`}>
                <div className="col-md col-12 d-flex justify-content-md-center">
                  <div className="text-start">
                    <p className={classes.programListHeader}>
                      PROGRAMS
                    </p>
                    {programData?.map((item, index) => {
                      return (
                        <Link
                            key={index}
                            href={`${item.url}${qpms}`}
                            onClick={() =>handleDropdownItemClicked()}
                          >
                            <p>{item.program_name}</p>
                          </Link>
                      )})}
                  </div>
                </div>
                <div className="col-md col-12 d-flex justify-content-md-center">
                <div className="row w-75 my-4 my-md-0">
                  <div className="col-md-12 col-6 mb-md-0 mb-4">
                <Link className={`${classes.programListHeader} pointer`} href={`/about-page${qpms}`}><p  className={classes.programListHeader}>ABOUT US</p></Link>
                  </div>
                    <div className="col-md-12 col-6 mb-md-0 mb-4">
                      <a href={`mailto:${data.contact_us_email}`}>
                        <p className={classes.programListHeader} >CONTACT US</p>
                      </a>
                    </div>
                    <div className="col mt-md-4">
                        <p className={classes.programListHeader}>{acedemicData.title.toUpperCase()}</p>
                        <a href={acedemicData.url}>
                        <p>{acedemicData.title.charAt(0).toUpperCase() + acedemicData.title.slice(1).toLowerCase()}</p>
                      </a>
                      <Link rel="canonical" href="/learning-tracks">
                        <p>Learning Tracks</p>
                      </Link>
                      <Link rel="canonical" href="/certificates-and-badges">
                      <p>Certificates & Badges</p>
                      </Link>
                    </div>
                </div>
                </div>
                <div className="col-md col-12 d-flex justify-content-md-center">
                <div className="row w-75">
                  <div className="col-md-12 col-6">
                    <p className={classes.programListHeader}>RESOURCES</p>
                    <Link rel="canonical" href="/lxp-page">
                      <p>LXP Page</p>
                      </Link>

                    <Link rel="canonical" href="/faq-page">
                      <p>FAQ Page</p>
                      </Link>
                  </div>
                  <div className="col-md-12 col-6">
                    <p className={classes.programListHeader}>Follow Us</p>

                    <div className={classes.socialIcons}>
                      {socialMediaArray.map((item, index) => (
                        <a 
                          key={index} 
                          href={item.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className={classes.socialIcon}
                        >
                          <Image
                            src={item.icon}
                            alt={item.name}
                            width={item.name === 'Twitter' ? 35 : 50}
                            height={item.name === 'Twitter' ? 50 : 50}
                          />
                        </a>
                      ))}
                    </div>
                    
                    
                  </div>
              
                </div>
                </div>
              </div>
              <div className={`${classes.footerFinal} mt-3`}></div>
            </div>} */}


            {/* sitemap div */}
            <div className="d-flex justify-content-between p-0 mt-1">
              <p className={classes.bottomLink}>ISB.EDU © Copyright 2025</p>

              {router.pathname !== "/lp/course/[courseid]" && 
              <div className={`d-flex gap-3`}>
                {data.other_links.map((item) => {
                  return (
                    <a
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ textDecoration: "None" }}
                      key={item.id}
                      href={`${item.url}`}
                    >
                      <p className={`${classes.bottomLink} text-dark`}>
                        <b>{item.title}</b>
                      </p>
                    </a>
                  );
                })}
              </div>}

            </div>
          </div>
        )}
      </div>
    </>
  );
}
