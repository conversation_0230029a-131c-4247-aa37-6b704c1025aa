import React, { useEffect, useState } from "react";
import "bootstrap/dist/css/bootstrap.min.css";
import Navbar from "react-bootstrap/Navbar";
import Link from "next/link";
import Image from "next/image";
import appLogo from "../assets/ISB_Online Logo.png";
import classes from "./index.module.css";
import "../styles/globals.css";
import "bootstrap-icons/font/bootstrap-icons.css";
import { config } from "@fortawesome/fontawesome-svg-core";
import "@fortawesome/fontawesome-svg-core/styles.css";
import { useRouter } from "next/router";
import { NextSeo } from "next-seo";
import backToTop from "../assets/backtotop.svg";
import Cookies from "js-cookie";
import NavbarCollapse from "../components/NavbarCollapse";
import Head from "next/head";

config.autoAddCss = false;
// Global input validation patterns
const VALIDATION_PATTERNS = {
  default: /^[A-Za-z0-9@.,_\- ]*$/,
  name: /^[A-Za-z ]*$/,  // Only letters and spaces for name fields
  email: /^[A-Za-z0-9@._\-\+]*$/  // Correctly allow "+" in emails
};

// Global input validation function
const validateInput = (event) => {
  const input = event.target;
  
  // Skip validation for specific input types or those with data-skip-validation attribute
  if (input.type === 'password' || 
      input.type === 'number' || 
      input.type === 'tel' || 
      input.hasAttribute('data-skip-validation')) {
    return;
  }

  // Determine field type
  const isNameField = input.id === 'name' || 
                     input.name === 'name' || 
                     input.getAttribute('data-input-type') === 'name';
                     
  const isEmailField = input.type === 'email' || 
                      input.id === 'email' || 
                      input.name === 'email' || 
                      input.getAttribute('data-input-type') === 'email';

  // Get the appropriate pattern
  let pattern;
  if (isNameField) {
    pattern = VALIDATION_PATTERNS.name;
  } else if (isEmailField) {
    pattern = VALIDATION_PATTERNS.email;
  } else {
    pattern = VALIDATION_PATTERNS.default;
  }

  // Get the full value to validate, either from paste event or current input value
  const valueToValidate = event.type === 'paste' ? 
    (event.clipboardData || window.clipboardData).getData('text') : 
    input.value;

  // Check if the entire string matches the pattern
  if (!pattern.test(valueToValidate)) {
    event.preventDefault();
    
    // Clean the invalid characters from the entire string
    let cleanedValue;
    if (isNameField) {
      cleanedValue = valueToValidate.replace(/[^A-Za-z ]/g, '');
    } else if (isEmailField) {
      cleanedValue = valueToValidate.replace(/[^A-Za-z0-9@._\-\+]/g, ''); // Fixed regex to allow "+"
    } else {
      cleanedValue = valueToValidate.replace(/[^A-Za-z0-9@.,_\- ]/g, '');
    }
    
    // Update the input value with the cleaned string
    requestAnimationFrame(() => {
      input.value = cleanedValue;
    });
    return false;
  }
  return true;
};

// Function to set up the validation listeners
const setupInputValidation = () => {
  // Handle both input and paste events
  document.addEventListener('input', validateInput, true);
  document.addEventListener('paste', validateInput, true);

  // Handle composition events for IME inputs
  document.addEventListener('compositionend', (event) => {
    validateInput({ type: 'input', target: event.target });
  }, true);
};

function MyApp({ Component, pageProps, data }) {
  const router = useRouter();
  const { asPath, query } = router;
  const baseURL = process.env.NEXT_PUBLIC_BASE_URL || "https://online.isb.edu";
  const LearningTrackData = pageProps.homePageContent?.data.attributes.learning_tracks.data
  const courseData = pageProps.courseData
    ? pageProps.courseData.data[0]?.attributes
    : pageProps.faqData
    ? pageProps.faqData.data.attributes
    : pageProps.learningTrackData
    ? pageProps.learningTrackData.data[0].attributes
    : pageProps.homePageContent
    ? pageProps.homePageContent.data.attributes
    : pageProps.landingPage
    ? pageProps.landingPage.data.attributes
    : pageProps.tqData
    ? pageProps.tqData
    : pageProps.posts
    ? pageProps.posts.data.attributes
    : pageProps.apiData && pageProps.apiData.data.attributes;
    const perspectivesMetaData = pageProps.perspectivesMetaData?.data?.attributes;

  useEffect(() => {
    setupInputValidation();

  }, []);

  //GET TITLE FOR META TAG
  const title =
    router.asPath === `/lxp-page`
      ? `${courseData.meta_tags.title}`
      : router.asPath === `/certificates-and-badges`
      ? `${courseData.meta_tags.title}`
      : router.asPath === `/faq-page`
      ? `${courseData.meta_tags.title}`
      : router.pathname === `/[learning_track_id]/[course]`
      ? `${courseData.meta_tag.title}`
      : router.pathname === `/[learning_track_id]`
      ? `${courseData.meta_tags.title}`
      : router.pathname === `/v2/[learning_track_id]`
      ? `${courseData.meta_tags.title}`
      : router.pathname === `/[courseid]`
      ? `${courseData.meta_tags.title}`
      : router.pathname === `/course/[courseid]`
      ? `${courseData.meta_tag.title}`
      : router.pathname === `/learning-tracks`
      ? `Online Business Learning Tracks and Courses | ISB Online`
      : router.pathname === `/about-page`
      ? `About Us`
      : router.pathname === `/landing-page`
      ? `${courseData.title}`
      : router.pathname.includes(`/perspectives`)
      ? `Perspectives` 
      : router.pathname === `/partner-programmes`
      ? `${pageProps.pageData.data.attributes.meta_tags ? pageProps.pageData.data.attributes.meta_tags.title : ''}`
      : router.pathname === `/finance`
      ? `Financing Options`
      : router.pathname === `/sitemap`
      ? `Site Map - Indian School of Business`
      : router.asPath === `/` && `${courseData.meta_tags.title}`;


  // GET DESCRIPTION FOR META TAG
  const description =
    router.asPath === `/faq-page`
      ? `${courseData.meta_tags.description}`
      : router.asPath === `/lxp-page`
      ? `${courseData.meta_tags.description}`
      : router.asPath === `/certificates-and-badges`
      ? `${courseData.meta_tags.description}`
      : router.pathname === `/[learning_track_id]/[course]`
      ? `${courseData.meta_tag.description}`
      : router.pathname === `/course/[courseid]`
      ? `${courseData.meta_tag.description}`
      : router.pathname === `/v2/[learning_track_id]`
      ? `${courseData.meta_tags.description}`
      : router.pathname === `/[learning_track_id]`
      ? `${courseData.meta_tags.description}`
      : router.pathname === `/[courseid]`
      ? `${courseData.meta_tag.description}`
      : router.pathname === `/landing-page`
      ? `${courseData.description}`
      : router.pathname === `/perspectives`
      ? `${courseData?.meta_tags?.description}`
      : router.pathname.includes(`/perspectives`)
      ? perspectivesMetaData?.meta_tags?.description || `${courseData?.meta_tags?.description}`
      : router.pathname === `/learning-tracks`
      ? `Immerse yourself in a unique learning experience and gain mastery in the specific subject areas that align with your career aspirations.`
      : router.pathname === `/about-page`
      ? `ISB Online was created as an extension of the ISB way of learning, with the aim of democratising education.`
      : router.pathname === `/partner-programmes`
      ? `${pageProps.pageData.data.attributes.meta_tags ? pageProps.pageData.data.attributes.meta_tags.description:""}`
      : router.pathname === `/sitemap`
      ? `Site Map`
      : router.pathname === `/finance`
      ? `Financing Options - Indian School of Business`
      : router.asPath === `/` && `${courseData.meta_tags.description}`;


  // GET Keywords FOR META TAG
  const keyword =
    router.asPath === `/faq-page`
      ? `${courseData.meta_tags.keywords}`
      : router.asPath === `/lxp-page`
      ? `${courseData.meta_tags.keywords}`
      : router.asPath === `/certificates-and-badges`
      ? `${courseData.meta_tags.keywords}`
      : router.pathname === `/[learning_track_id]/[course]`
      ? `${courseData.meta_tag.keywords}`
      : router.pathname === `/[learning_track_id]`
      ? `${courseData.meta_tags.keywords}`
      : router.pathname === `/[courseid]`
      ? `${courseData.meta_tag.keywords}`
      : router.pathname === `/course/[courseid]`
      ? `${courseData.meta_tag.keywords}`
      : router.pathname === `/v2/[learning_track_id]`
      ? `${courseData.meta_tags.keywords}`
      : router.pathname === `/perspectives`
      ? `${courseData?.meta_tags?.keywords}`
      : router.pathname.includes(`/perspectives`)
      ? perspectivesMetaData?.meta_tags?.keywords || `${courseData?.meta_tags?.keywords}`
      : router.pathname === `/learning-tracks`
      ? `ISB, ISB Online, Digital Course, Online Course, Learning Tracks`
      : router.pathname === `/about-page`
      ? `ISB, ISB Online, Digital Course, Online Course, Learning Tracks`
      : router.pathname === `/sitemap`
      ? `Site Map - Indian School of Business`
      : router.pathname === `/finance`
      ? `Financing Options - Indian School of Business`
      : router.pathname === `/partner-programmes`
      ? `${pageProps.pageData.data.attributes.meta_tags ? pageProps.pageData.data.attributes.meta_tags.keywords : ""}`
      : router.asPath === `/` && `${courseData.meta_tags.keywords}`;


  // GET Robots FOR META TAG
  const robots =
    router.asPath === `/faq-page`
      ? `${courseData.meta_tags.robots}`
      : router.asPath === `/lxp-page`
      ? `${courseData.meta_tags.robots}`
      : router.asPath === `/certificates-and-badges`
      ? `${courseData.meta_tags.robots}`
      : router.pathname === `/[learning_track_id]/[course]`
      ? `${courseData.meta_tag.robots}`
      : router.pathname === `/v2/[learning_track_id]`
      ? `${courseData.meta_tags.robots}`
      : router.pathname === `/[learning_track_id]`
      ? `${courseData.meta_tags.robots}`
      : router.pathname === `/[courseid]`
      ? `${courseData.meta_tag.robots}`
      : router.pathname === `/perspectives`
      ? `${courseData?.meta_tags?.robots}`
      : router.pathname.includes(`/perspectives`)
      ? perspectivesMetaData?.meta_tags?.robots || `${courseData?.meta_tags?.robots}`
      : router.pathname === `/learning-tracks`
      ? `ISB, ISB Online, Digital Course, Online Course, Learning Tracks`
      : router.pathname === `/about-page`
      ? `ISB, ISB Online, Digital Course, Online Course, Learning Tracks`
      : router.pathname === `/sitemap`
      ? `Site Map`
      : router.pathname === `/finance`
      ? `Financing Options`
      : router.pathname === `/partner-programmes`
      ? `${pageProps.pageData.data.attributes.meta_tags ? pageProps.pageData.data.attributes.meta_tags.robots : ""}`
      : router.asPath === `/` && `${courseData.meta_tags.robots}`;

  // GET ImageURL FOR META TAG
  const imageUrl =
    router.pathname === `/[learning_track_id]/[course]`
      ? `${pageProps.apiUrl}${courseData.course_short_image.data.attributes.formats.thumbnail.url}`
      : router.pathname === `/[learning_track_id]`
      ? `${pageProps.apiUrl}${courseData.learning_track_short_image.data.attributes.formats.thumbnail.url}`
      : router.pathname === `/perspectives`
      ? courseData?.meta_tags?.image?.data?.attributes?.url ? `${pageProps.apiUrl}${courseData.meta_tags.image.data.attributes.url}` : `${process.env.NEXT_PUBLIC_BASE_URL}${appLogo.src}`
      : router.pathname.includes(`/perspectives`)
      ? perspectivesMetaData?.meta_tags?.image?.data?.attributes?.url ? `${pageProps.apiUrl}${perspectivesMetaData.meta_tags.image.data.attributes.url}` : (courseData?.meta_tags?.image?.data?.attributes?.url ? `${pageProps.apiUrl}${courseData.meta_tags.image.data.attributes.url}` : `${process.env.NEXT_PUBLIC_BASE_URL}${appLogo.src}`)
      : router.pathname === `/partner-programmes`
      ? `${pageProps.apiUrl}${pageProps.pageData.data.attributes.meta_tags.image.data.attributes.url}`
      : router.pathname === `/finance`
      ? pageProps.pageData?.data?.attributes?.meta_tags?.image?.data?.attributes?.url ? `${pageProps.apiUrl}${pageProps.pageData.data.attributes.meta_tags.image.data.attributes.url}` : `${process.env.NEXT_PUBLIC_BASE_URL}${appLogo.src}`
      : router.pathname === `/`
      ? pageProps.homePageContent && `${pageProps.apiUrl}${pageProps.homePageContent.data.attributes.meta_tags.image.data?.attributes.url}`
      :  `${process.env.NEXT_PUBLIC_BASE_URL}${appLogo.src}`;


  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    window.addEventListener("scroll", () => {
      if (window.pageYOffset > 300) {
        setShowButton(true);
      } else {
        setShowButton(false);
      }
    });
  }, []);


  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "auto",  
    });
  };


  const [showBanner, setShowBanner] = useState(false);


  useEffect(() => {
    const hasConsent = Cookies.get("cookieConsent");
    setShowBanner(!hasConsent);
  }, []);

const [elementClicked, setPathClicked] = useState(false);
const pathname = router.pathname;

useEffect(() => {
  if (pathname.includes("perspectives")) {
    setPathClicked(true)
  }else{
    setPathClicked(false)

  }
  
}, [pathname]);
  function handleAccept() {
    Cookies.set("cookieConsent", true, { expires: 365 });
    setShowBanner(false);
  }
  const qpms =
    query.utm_source != undefined
      ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}&utm_device=${query.utm_device}`
      : ``;


      const pagesWithCustomFont = ['/perspectives', '/perspectives/article/[slug]', '/perspectives/infographic/[slug]'];

      const [isDropdownOpen, setDropdownOpen] = useState(false);
      const [navbarData, setNavbarData] = useState(null);

      const toggleDropdown = () => {
        setDropdownOpen(false);
      };
     
      const handleNavDataLoaded = (data) => {
        setNavbarData(data);
      };
      const schemaData = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": `${baseURL}` + router.asPath,
        "potentialAction": {
          "@type": "SearchAction",
          "target": `${baseURL}/search?q={search_term_string}`,
          "query-input": "required name=search_term_string"
        },
        "hasPart": [
          {
            "@type": "SiteNavigationElement",
            "position": 1,
            "name": "Home",
            "url": baseURL,  // Home should link to the base URL
          },
          {
            "@type": "SiteNavigationElement",
            "position": 2,
            "name": "About",
            "url": `${baseURL}/about-page`,  // Adjust if your About page has a different URL
          },
          // Spread operator to append dynamically generated learning tracks
          ...(Array.isArray(LearningTrackData)
            ? LearningTrackData.map((track, index) => ({
                "@type": "SiteNavigationElement",
                "position": index + 3, // Start at 3 after Home & About
                "name": track.attributes?.learning_track_name || "",
                "url": `${baseURL}/${track.attributes?.learning_track_id || ""}`,
              }))
            : []),
        ]
      
      };
  return (
    <>
      <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
        />
      </Head>
      <NextSeo
        title={title}
        canonical={`${process.env.NEXT_PUBLIC_BASE_URL}` + router.asPath}
        description={description}
        openGraph={{
          type: `website`,
          url: `${process.env.NEXT_PUBLIC_BASE_URL}` + router.asPath,
          title: `${title}`,
          description: `${description}`,
          locale: "en-IN",
          images: [
            {
              url: `${imageUrl}`,
              alt: "image.png",
            },
          ],
          site_name: `ISB Online`,
        }}
        twitter={{
          handle: "@handle",
          site: "@site",
          cardType: `${title}`,
          title: `${title}`,
          description: `${description}`,
          image: `${imageUrl}`,
        }}
        additionalMetaTags={[
          {
            name: "keywords",
            content: `${keyword}`,
          },
          {
            name: "robots",
            content: `${robots}`,
          },
        ]}
        // additionalJsonLd={{
        //   "@context": "https://schema.org",
        //   "@type": "WebSite",
        //   "url": `${process.env.NEXT_PUBLIC_BASE_URL}${router.asPath}`,
        //   "potentialAction": {
        //     "@type": "SearchAction",
        //     "target": `${process.env.NEXT_PUBLIC_BASE_URL}/search?q={search_term_string}`,
        //     "query-input": "required name=search_term_string",
        //   },
        //   "hasPart": [
        //     {
        //       "@type": "SiteNavigationElement",
        //       "position": 1,
        //       "name": `${title}`,
        //       "url": process.env.NEXT_PUBLIC_BASE_URL,
        //     },
        //   ],}}
        additionalJsonLd={schemaData}
      />
      <>
        {router.pathname === "/lp/[learning_track_id]/thankyou" ||router.pathname === "/lpv2/[learning_track_id]/thankyou" ||router.pathname === "/lpv2/[learning_track_id]" || router.pathname === "/lp/[learning_track_id]" || router.pathname==="/finance" || router.pathname === "/lp/course/[courseid]"? null : (
          <Navbar
            sticky="top"
            className={`${classes.navelems}`}
            bg="white"
            expand="lg"
            id="myNavbar"
            expanded={isDropdownOpen}
           
          >
            {router.pathname == `/landing-page` ||
            router.pathname == `/coming-soon` ||
            router.pathname == `/cs-tq`
            ? (
              <Navbar.Brand>
                <Image className={classes.navbar_logo} width={170} src={appLogo} alt="ISB Logo" />
              </Navbar.Brand>
            ) : (
              <Link href={`/${qpms}`}>
                <Navbar.Brand>
                  <Image className={classes.navbar_logo} width={170} src={appLogo} alt="ISB Logo" />
                </Navbar.Brand>
              </Link>
            )}


            {router.pathname == `/landing-page` ||
            router.pathname == `/coming-soon` ||
            router.pathname == `/cs-tq` ||
            router.pathname == "/landingpage"  ||
            router.pathname == `/me_thankyou` ? (
              <div></div>
            ) : (
              <>
              {router.pathname.includes(`/perspectives`) &&
                <Navbar.Brand className={`${classes.perspective_title} d-flex d-md-none`}>
                  {navbarData?.edge_title || "Perspectives"}
                </Navbar.Brand>
                }
              <Navbar.Toggle aria-controls="basic-navbar-nav" onClick={() =>setDropdownOpen(!isDropdownOpen)}/>
              </>
            )}
            {router.pathname == `/landing-page` ||
            router.pathname == `/coming-soon` ||
            router.pathname == `/cs-tq` ||
            router.pathname == "/landingpage" ||
            router.pathname == `/me_thankyou`? (
              <div></div>
            ) : (
              <Navbar.Collapse
                className={`${classes.navelemsmain} d-base-flex justify-content-center`}
                id="basic-navbar-nav"
              >
                      <NavbarCollapse qpms={qpms} toggleDropdown={toggleDropdown} onNavDataLoaded={handleNavDataLoaded}/>
              </Navbar.Collapse>
            )}


          </Navbar>
        )}


        
        {/* <div className="bg-light border-2 border-top border-light"></div> */}
        <div className={pagesWithCustomFont.includes(router.pathname) ? 'custom-font' : ''}>
        <Component {...pageProps} />
        </div>
        {showButton && (
          <div
            className={`${classes.equalPadding} mx-auto ${classes.alignRight}`}
          >
            <Image
            alt=""
              height={48}
              className={classes.back_to_top}
              onClick={scrollToTop}
              src={backToTop}
            ></Image>
          </div>
        )}
        {showBanner && (
          <div className={`row ${classes.cookie_banner} px-lg-5 px-3 py-3`}>
            <div className="col-lg-9 col-md-12 col-12 p-lg-0">
              <p
                style={{ fontSize: "14px" }}
                className="text-white text-start m-0"
              >
                We use Cookies on this site to enhance your experience and
                improve our marketing efforts. To learn more click on{" "}
                <a
                  target="_blank"
                  rel="noopener noreferrer"
                  href="https://www.isb.edu/en/legal/privacy-cookies-policy.html"
                >
                  About Cookies
                </a>
              </p>
            </div>
            <div className="col-lg-2 col-0"></div>
            <button
              className={`col-lg-1 col-3 mt-lg-0 mt-3 ${classes.cookieButton}`}
              onClick={handleAccept}
            >
              Accept
            </button>
          </div>
        )}
      </>
    </>
  );
}
export default MyApp;
