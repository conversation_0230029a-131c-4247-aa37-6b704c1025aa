import React from 'react';
import { Worker, Viewer } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';

// Import styles
import '@react-pdf-viewer/full-screen/lib/styles/index.css';

const PDFViewer = () => {

 const pdfDocs = [
  {
    url : "./Axis Bank.pdf"
  },
  {
    url : "./Bank of Baroda Education Loan for ISB.pdf"
  },
  {
    url : "./HDFC Credila.pdf"
  },
  {
    url : "./idfc.pdf"
  },
  {
    url : "./Liquiloans-finance-ISB-Oct2022.pdf"
  },
  {
    url : "./Propelld Financing Option.pdf"
  },
 ]
 
  const renderToolbar = (ToolbarProps) => (
    
        <>
          
        </>
     
);

  return (
    <div className='row gap-4 justify-content-center'>
{pdfDocs.map((pdf, index) => {
  return(
      <div
      key={index}
    style={{
        border: '1px solid rgb(169 169 169 / 15%)',
        height: '75vh',
        padding:"0px",
        width:'500px',
    }}
>
      <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js">
     
    
    <Viewer fileUrl={pdf.url}  plugins={[defaultLayoutPlugin(renderToolbar)]}/>
    
      </Worker>
</div>
  )})    }
  </div>
  );
};
export default PDFViewer;