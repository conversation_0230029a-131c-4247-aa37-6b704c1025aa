.topicsDropdownContainer {
    background-color: var(--isb-edge-body-background-color);
    width: 100%;
    position: relative;
    z-index: 1000;
    padding-top: 16px;
  
  }
  
  .topicsNavContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
  }
  
  .navTabs {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
    justify-content: flex-start;
  }
  
  .navTab {
    padding: 15px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    font-family: var(--isb-edge-font-family-inter);
    color: #192890;
    position: relative;
    transition: color 0.2s ease;
    text-align: center;
  }
  
  .navTab:hover {
    color: #245BFF;
    text-decoration: underline;
    cursor: pointer;
    text-decoration-thickness: 2px;
    text-underline-offset: 4px;
  }
  
  .navLink {
    color: inherit;
    text-decoration: none;
  }
  
  .topicsTab {
    color: #192890;
    font-weight: 600;
    position: relative;
  }
  
  .topicsTab::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    /* background-color: #192890; */
  }
  .topicsTab:hover{
    text-decoration: underline;
    color: #245BFF;
    cursor: pointer;
    text-decoration-thickness: 2px;
    text-underline-offset: 4px;
  }
  .downArrow {
    font-size: 8px;
    margin-left: 2px;
    vertical-align: middle;
    position: relative;
    top: 1px;
    text-decoration: none !important;
    display: inline-block;
  }
  
  .topicsTab:hover .downArrow {
    text-decoration: none;
  }
  
  .dropdownContent {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100vw;
    background-color: #E6EBED;
    z-index: 1001;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e0e0e0;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.1s ease, transform 0.1s ease;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }
  
  .dropdownOpen {
    opacity: 1;
    transform: translateY(0);
  }
  
  .dropdownClosed {
    opacity: 0;
    transform: translateY(-0px);
  }
  
  .topicsGrid {
    padding: 25px 25px 25px 0px;
    background-color: #E6EBED;
    z-index: 1001;
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
  }
  
  .topicsGridLayout {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    max-width: 1000px;
    /* margin: 0 auto; */
    gap: 40px;
  }
  
  .topicColumn {
    display: flex;
    flex-direction: column;
    flex: 0 1 auto;
    padding: 0 20px 0 1px;
    min-width: 200px;
  }
  
  .emptyColumn {
    visibility: hidden;
    width: 0;
    padding: 0;
    margin: 0;
  }
  
  .topicItem {
    padding: 5px 0;
    cursor: pointer;
    color: black;
    transition: color 0.2s;
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
    font-family: var(--isb-edge-font-family-inter);
  }
  
  .topicItem:hover {
    color: #192890;
  }
  
  .viewAllTopics {
    color: #245BFF;
    text-decoration: underline;
    text-decoration-thickness: 1px;
    text-underline-offset: 4px;
    margin-top: 5px;
  }
  
  .topicItem:hover.viewAllTopics {
    color: #245BFF;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @media (max-width: 992px) {
    .topicsGridLayout {
      flex-wrap: wrap;
    }
    
    .topicColumn {
      flex-basis: 33.33%;
    }
  }
  
  @media (max-width: 768px) {
    .topicsGrid {
      padding: 10px 0 10px 15px;
    }
    .topicsGridLayout {
      gap: 0;
    }
    .navTabs{
      padding: 0px 16px;
      justify-content: flex-start;
      gap: 4px;
    }
    .dropdownContent{
      max-height: 50vh;
      overflow: auto;
    }
    
    .topicColumn {
      flex-basis: 50%;
    }
    
    .navTab {
      padding: 10px 15px;
      font-size: 14px;
    }
  }
  
  @media (max-width: 480px) {
    .topicColumn {
      flex-basis: 100%;
    }
    
    .navTab {
      padding: 8px 10px 8px 0;
      font-size: 13px;
    }
  }