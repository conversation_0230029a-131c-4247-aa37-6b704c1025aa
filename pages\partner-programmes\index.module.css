/* Partner Programmes Styles */
.partnerProgrammesWrapper {
    padding: 3rem 0 0;
    background-color: #EDF1F3;
  }
  
  /* Header section styles */
  .headerSection {
    margin-bottom: 3rem;
    padding: 0 0 1.5rem 0;
  }
  .breadCrumb{
    align-items: center;
    display: flex;
    color: #000000;
  }
  .breadCrumb p{
    color: #000000;
    font-family: var(--isb-edge-font-family-inter-variable);
    font-size: 12px;
    letter-spacing: 0.1px;
    font-weight: 400;
  }
  .mainTitle {
    color: var(--isb-edge-blue-font-color);
    font-size: 2.5rem;
    font-weight: 700;
    padding-bottom: 1.5rem;
    max-width: var(--isb-container-max-width);
    margin: 0 auto;
    font-family: var(--isb-edge-font-family-Reckless);
}
.horizontalLine{
    border-top: 2px solid #e6e6e6;
    border-bottom: 2px solid #e6e6e6;
    margin-bottom: 0;

  }
  .mainContent{
    background-color: #fff;
    max-width: var(--isb-container-max-width);
    margin: 0 auto;
    padding: 2.5rem;
  }
  .mainDescription {
    font-size: 28px;
    line-height: 39px;
    color: #000000;
    margin-bottom: 2rem;
    font-family: var(--isb-edge-font-family-inter-variable);
    font-variation-settings: 'wght' 400, 'slnt' 0;
  }
  
  .headerImage {
    width: 100%;
    height: auto;
  }

  .programmeCard {
    padding: 1.5rem 0 0 1.5rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    background-color: #EDF1F3;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }
 
  
  .cardTitle {
    color: var(--isb-edge-blue-font-color);
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: var(--isb-edge-font-family-Reckless);
  }
  
  .cardDescription {
    font-size: 20px;
    line-height: 1.5;
    color: #585865;
    margin-bottom: 1.5rem;
    flex-grow: 1;
    font-weight: 400;
    font-family: var(--isb-edge-font-family-inter-variable);
  }
  
  .exploreLink {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #0039a6;
    text-decoration: none;
    margin-bottom: 1rem;
  }
  
  .exploreLinkWrapper {
    display: inline-flex;
    align-items: center;
    position: relative;
    border-bottom: 4px solid #AAF055;
  }
  
  .exploreLinkText {
    margin-right: 0.5rem;
    font-size: 17px;
    position: relative;
    z-index: 1;
    color: var(--isb-edge-blue-font-color);
    text-decoration: none;
    line-height: 0.7;
  }
  /* .exploreLinkText::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 100%;
    height: 3px;
    background-color:#AAF055;  
    z-index: -1;
  } */
  
  .arrowIcon {
    margin-left: 0;
    width: 12px;
    height: 12px;
    position: relative;
    top: 2px;
  }
  
  .exploreLink:hover {
    color: #002d80;
  }
  
 .cardTitle, .cardDescription{
    padding-right: 24px;
 }
  
  .cardImageContainer {
    margin-top: auto;
    max-height: 187px;
    overflow: hidden;
  }
  
  .cardImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
 
  
  /* Responsive adjustments */
  @media (max-width: 992px) {
    .mainTitle {
        font-size: 2rem;
        padding-left: 16px;
    }
    
    .cardTitle {
      font-size: 1.3rem;
    }
    .mainDescription{
      margin-top: 2rem;
    }
    .headerSection {
      margin-bottom: 0rem ;
      padding-bottom: 0rem;
  }
  .partnerProgrammesWrapper {
    padding: 1.5rem 0 0;
  }
  }
  
  @media (max-width: 768px) {
    .mainDescription {
      font-size: 20px;
    }
    .mainContent{
      padding: 1.8rem;
    }
    .partnerProgrammesWrapper {
        padding: 1.5rem 0 0;
    }
    .mainDescription {
        margin-top: 1.5rem;
        text-align: start;
    }
    .headerSection {
        margin-bottom: 0rem ;
        padding-bottom: 0rem;
    }
    .breadcrumb{
        padding-left: 0px;
    }
    .mainTitle {
        font-size: 1.5rem;
        padding-left: 0px;
    }
    .headerSection {
      text-align: center;
    }
    .headerImage {
      margin-top: 1rem;
    }
    
    .programmeCard {
      margin-bottom: 1.5rem;
    }
  }
  