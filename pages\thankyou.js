import Link from "next/link";
import Image from "next/image";
import classes from "./thankyou.module.css";
import image from "../assets/heart.svg";
import BottomFold from "../components/bottom_fold_one";


export default function thankyouPage(props){
  const tqData = props.tqData.data.attributes
    return(
        <>
            <div className={`${classes.linearGradient}`}>
              <div className={`${classes.equalPadding} mx-auto py-lg-5 py-4`}>
                <Image
                className="mx-auto d-block"
                  height={55}
                  src={image}
                  alt="thankyou_image"
                  />
                  <p className={`text-center mt-4 ${classes.whiteHeading}`}>{tqData.tq_title}</p>
                  <p className="text-center px-3 m-0" style={{color:"white"}}>{tqData.tq_description}</p>
              </div>
            </div>
            <div className="px-lg-0 px-3">
              <p className={`${classes.blackHeading} text-center mt-4`}>{tqData.free_course_title}</p>
              <div className={`row ${classes.equalPadding} mx-auto mb-5`}>
              {tqData.courses.data.map((item, i) => {
              return (
                <div
                  key={item.id}
                  className={`${ 
                    i === 0 || i === 2 ? "px-lg-2" : "px-lg-1"
                  } px-0 col-lg-3 mb-0 d-flex align-items-stretch col-md-6 col-sm-12 my-lg-0 my-md-3 my-3`}
                >
                  <div className={`card ${classes.boxshadow}`}>
                    <Image
                      width="0"
                      height="0"
                      sizes="100vw"
                      src={
                        props.apiUrl +
                        item.attributes.course_short_image.data
                          .attributes.formats.small.url
                      }
                      className={`w-100 h-auto card-img-top ${classes.image_border}`}
                      alt={item.attributes.course_short_image.data
                        .attributes.alternativeText}
                    />
                    <div
                      className={`card-body d-flex flex-column ${classes.buttonPos}`}
                    >
                      <h5 className={`card-title ${classes.learnTrackTitle}`}>
                        {item.attributes.course_title}
                      </h5>
                      <p className={`pb-5 card-text`}>
                        {item.attributes.course_short_description}
                      </p>
                      <div className={`mb-3 ${classes.buttonPosition}`}>
                        <Link rel="canonical"
                          href={`${item.attributes.learning_track_id}/${item.attributes.courseid}`}
                          className={`btn btn-primary mt-auto align-self-start ${classes.viewbtn}`}
                        >
                          Learn More <i className="fa fa-long-arrow-right"></i>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
              </div>
            </div>
            <div className={`${classes.fold1bg} col-12`}>
              <div className={`${classes.equalPadding} mx-auto ${classes.imagePadding} px-3 pb-5`}>
                <p className={`${classes.whiteHeading} text-center`}>{tqData.bottom_fold_title}</p>
                <p className="text-center text-white">{tqData.bottom_fold_description}</p>
                <div className="text-center">
                  <Link rel="canonical" href="/learning_tracks">
                  <button className={`${classes.showBtn} mt-3`} type="submit">
                    {`   Apply Now   `}          
                  </button>
                  </Link>
                </div>
              </div>
            </div>
            <BottomFold data = {props.bottomFoldData}></BottomFold>
        </>
    )
}

export async function getStaticProps(context) {
    const { req, query, res, asPath, pathname, params } = context;
    const APIUrl = process.env.API_BASE_URL;
    const [tqData,bottomFoldData] = await Promise.all([
      fetch(
        `${APIUrl}/api/thank-you-page?populate[courses][populate][0]=course_short_image`
      ).then((r) => r.json()),
      fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
    ]);
    return {
      props: {
        tqData: tqData,
        apiUrl: APIUrl,
        bottomFoldData:bottomFoldData
      },
    };
  }