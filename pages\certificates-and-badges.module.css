
.equalPadding{
    max-width: 1000px;
  }

  .description{
    text-align: left;
  }

  .fontEighteenBlue{
    font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 27px;
  color: #057092;
  }

  .fontEighteenBlack{
  font-style: normal;
  font-weight: 400;
  font-size: 17px;
  line-height: 27px;
  color: black;
  }

  .imageShadow{
    filter: drop-shadow(0px 4px 20px rgba(0, 0, 0, 0.2));
  }

  .viewbtn
{
	background-color: #057092 !important;
    border:none;
    padding: 10px 36px 10px 36px;
    font-weight: 600;
    flex-wrap: wrap;
    margin-right: 8px;
    margin-left: 8px;
    border-radius: 0px!important;
}
  
.bluetTitle{
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 27px;
  color: #057092;
  }

  .breadCrumb p{
    font-size: 12px;
    color: #057092;
  }
  
  .breadCrumblast{
    font-size: 12px;
    color: white;
    text-decoration: none !important;
  }
  
  .breadCrumb p:hover {
    font-size: 12px;
    color: #057092;
    text-decoration: underline;
  }
  
  .hideCrumb{
    max-width: 1000px;
  }

  
  @media screen and (min-width:0px) and (max-width: 499px) {

    .hideCrumb{
      display: none;
    }
  }
  
  @media screen and (min-width:500px) and (max-width: 976px){
  
    .hideCrumb{
      display: none;
    }
  }
  
   