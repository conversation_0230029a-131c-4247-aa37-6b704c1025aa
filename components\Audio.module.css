.playPauseButton {
    border: none;
    background-color: transparent;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
    will-change: transform;
}

.progressContainer {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
    position: relative;
}

.progressBar {
    flex: 1;
    height: 5px;
    background: #404040;
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    -webkit-appearance: none;
    margin: 0;
    padding: 0;
}

.controlButton {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    transition: transform 0.2s ease-in-out;
    will-change: transform;
}

.controlButton:hover {
    transform: scale(1.1);
}

.playPauseButton:hover {
    transform: scale(1.1);
    
}

.playButtonContainer {
    display: flex;
    justify-content: center;
    /* margin-bottom: 20px; */
}

.progressBar::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    margin-top: -4px;
}

.progressBar::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    margin-top: -4px;
}

.progressBar:hover::-webkit-slider-thumb {
    transform: scale(1.1);
}

.progressBar:hover::-moz-range-thumb {
    transform: scale(1.1);
}

/* Progress bar fill color */
.progressBar::-webkit-slider-runnable-track {
    background: linear-gradient(to right, white var(--progress-value, 0%), #404040 var(--progress-value, 0%));
    border-radius: 2px;
    height: 5px;
}

.progressBar::-moz-range-track {
    background: #404040;
    border-radius: 2px;
    height: 5px;
}

.progressBar::-moz-range-progress {
    background: white;
    border-radius: 2px;
    height: 5px;
}

.playerProgress {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #b3b3b3;
}

@media (max-width: 768px) {
    .playPauseButton{
    border: 1px solid white;
    border-radius: 50%;
    width: 42px;
    height: 40px;
    }
}