import React, { useEffect, useState } from 'react'
import classes from "../pages/v2/[learning_track_id]/index.module.css"
import { PhoneInputField } from './LeadForm';
 

const GroupEnrolment = ({handleClosePaymentModal, baseURL, tags, URL, styledDescription, landingPageResponse }) => {
  const [buttonDisabled, setButtonDisabled] = useState(true);
  const [formErrors, setFormErrors] = useState({});
  const [countryCodeEntry,setCountryCodeEntry] = useState()
  const [coutrycode_exclude,setCoutrycode_exclude] = useState()
 
  

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    number: '',
   organisation: '',
   no_of_participants: null,
   is_group_lead :true

  });
  
 
  
  const isEnglishText = (text) => {
    return /^[A-Za-z0-9\s.,&'-]*$/.test(text);
  };

  const handleChange = (e) => {

    const { name, value } = e.target;
    let updatedValue = value;
    let updatedValueforNoofParticipants = value.replace(/\D/g, "");
   
    if (name === 'organisation') {
      if (!isEnglishText(value)) {
        setFormErrors((prevErrors) => ({
          ...prevErrors,
          [name]: 'Please enter text in English only',
        }));
        return;
      }
    }

    setFormData((prevData) => ({
      ...prevData,
      [name]:  name === "no_of_participants" ? updatedValueforNoofParticipants : updatedValue,
    }));
    setFormErrors((prevErrors) => ({
      ...prevErrors,
      [name]: name === "no_of_participants" ? null :'',
    }));
  };
   

  const handleChange1=(e)=>{

    if(Number(e.slice(0,2)) ===91){
    setCountryCodeEntry(e.slice(0,2))
    let sliced_mobile = e.slice(2,12)
        setCoutrycode_exclude(sliced_mobile)
    
    }else if(Number(e.slice(0,3)) ===971){
    setCountryCodeEntry(e.slice(0,3))
    let sliced_mobile = e.slice(3,13)
    setCoutrycode_exclude(sliced_mobile)
    
    }else if(Number(e.slice(0,2)) ===65 || Number(e.slice(0,2)) ===61){
    setCountryCodeEntry(e.slice(0,2))
    let sliced_mobile = e.slice(2,13)
    setCoutrycode_exclude(sliced_mobile)
    
    }
    
    else{
    setCountryCodeEntry(e)
    }
        setFormErrors((prevErrors) => ({
          ...prevErrors,
          "number": '',
        }));
      
      setFormData((prevData) => ({
        ...prevData,
        "number": e,
      }));
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        "number": '',
      }));
    }

  const isValidEmail = (email) => {
    return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email);
  };

  useEffect(() => {
    const allFieldsFilled = Object.values(formData).every(value => value !== '' && value !== null);
    setButtonDisabled(!allFieldsFilled);
  }, [formData]);
  

  const [completePath, setCompletePath] = useState('');
    
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname.split('/').slice(1).join('/');  
      const queryParams = window.location.search.slice(1); 
      let combinedUrl = `${baseURL}/${currentPath}`;
          if (queryParams) {
      combinedUrl += `?${queryParams}`;
    }
      setCompletePath(combinedUrl);
    }
  }, []);
 
  const isValidMobileNumber = (number) => {
    let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    return /^[0-9]{0,15}$/.test(cleanedNumber);
  };
  const submitContact = async (event) => {
    event.preventDefault();
    const errors = {};
  
    if (!formData.name) {
      errors.name = 'Name is required';
    }else if(formData.name.length < 3){
      errors.name = 'Name should be atleast 3 characters';
    }
    
    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!isValidEmail(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    if (!formData.organisation) {
      errors.organisation = 'Organisation is required';
    }else if(formData.organisation.length < 3){
      errors.organisation = 'Organisation should be atleast 3 characters';
    }
    if (!formData.no_of_participants) {
      errors.no_of_participants = 'No of participants is required';
    }
    if(formData.no_of_participants < 1){
      errors.no_of_participants = 'No of participants should be atleast 1';
    }
     
    if (!formData.number) {
      errors.number = 'Mobile number is required';
    } 
   if(countryCodeEntry){
     if (countryCodeEntry.slice(0,2) ==91 && (coutrycode_exclude).length<10) {
      errors.number = 'Please enter valid mobile number'; 
    }else if((countryCodeEntry.slice(0,2) ==65 || countryCodeEntry.slice(0,2) ==61) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter valid mobile number'; 
    }
    else if((countryCodeEntry.slice(0,3) ==971) && (coutrycode_exclude.length>9 || coutrycode_exclude.length<8 ) ){
      errors.number = 'Please enter a valid mobile number '; 
    }
  }
    else if (!isValidMobileNumber(event.target.number.value)) {
      errors.number = 'Please enter a valid mobile number';
    }
  
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setButtonDisabled(false);
      return;
    }
  
        let cleanedNumber = event.target.number.value
      .replace(/^\+\d+/, "")
      .replace(/[-\s]/g, "");
    const getCountryCode = (phoneNumber) =>
      phoneNumber.match(/^\+(\d{1,3})/)?.[1];
    const countryCode = getCountryCode(event.target.number.value);
  


    const json = {
      first_name: formData.name,
      email: formData.email.toLowerCase(),
      country_code: countryCode,
      mobile: cleanedNumber,
      Company: formData.organisation,
      Number_of_Participants__c: formData.no_of_participants,
      Group_Lead__c:true,
      url: completePath,
      lead_form_submitted: true,
      program_id:
        process.env.NEXT_PUBLIC_BASE_URL === 'https://online.isb.edu'
          ? landingPageResponse.prod_program_id
          : landingPageResponse.staging_program_id,
      // tags: tags,
    };

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_LANDINGPAGE_SFDC_URL}/backend/free_lesson/user_create`,
      {
        body: JSON.stringify(json),
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
      }
    );

    const data = await response.json();
    const userId = data.user_id;

    if (response.status === 200) {
      handleClosePaymentModal(true)
      setFormData({
        name: '',
        email: '',
        number: '',
        role: 'Select',
        agree: true,
        organisation:"",
        no_of_participants: null,
        is_group_lead :true

      })
 
    }
   
  };

  
  return (
    <div style={{maxWidth:"600px"}} className='mt-4'>
    <div
            dangerouslySetInnerHTML={{ __html: styledDescription }}
          />
      {/* <div dangerouslySetInnerHTML={{_html:description}} className='mb-2'/> */}
      <div>
      <form onSubmit={submitContact} className="my-0 " style={{maxWidth:"100%", overflow:"hidden"}}>
        <div className="row">
          <div className="form-group col py-1">
            <label
              className={`control-label ${classes.formLabelText} fw-bold`}
              htmlFor="name"
            >
              {landingPageResponse.signup_form.Name_lable}
              <span className={`${classes.formLabelTextcolor}`}>*</span>
            </label>
            <input
              className={`form-control ${classes.forminput} border-0 `}
              placeholder='Enter your name here'
              type="text"
              id="name"
              name="name"
              lang='en'
              data-lpignore="true"
              autoComplete='off'
              value={formData.name}
              onChange={handleChange}
              onKeyPress={(event) => {
                if (!/^[A-Za-z. ]$/.test(event.key)) {
                  event.preventDefault();
                }
              }}
            />
              {formErrors.name && <div  className={`${classes.error_tooltip} mb-2`}>{formErrors.name}</div>}
          </div>
        </div>


        <div className='row'>

          <div className="form-group col-md col-12 py-2 pe-1 me-1">
            <label
              className={`control-label ${classes.formLabelText} fw-bold`}
              htmlFor="email"
            >
              {landingPageResponse.signup_form.Email_lable}
              <span className={`${classes.formLabelTextcolor}`}>*</span>
            </label>
            <input
              className={`form-control ${classes.forminput} border-0`}
              placeholder='Enter your email here'
              type="text"
              id="email"
              autoComplete='off'
              name="email"
              value={formData.email}
              onChange={handleChange}
            />
            {formErrors.email && <div className={`${classes.error_tooltip}`}>{formErrors.email}</div>}

          </div>
          <div className='col-md col-12 ps-1'>
            <PhoneInputField landingPageResponse={landingPageResponse} formData={formData} handleChange1={handleChange1} formErrors={formErrors} />
          </div>
        </div>

        <div className="row">
           
            <div className="form-group col-md col-12 pe-1 py-1 me-1">
              <label
                className={`control-label ${classes.formLabelText} fw-bold`}
                htmlFor="organisation"
              >
                {landingPageResponse.signup_form.Company_Name_label}
                <span className={`${classes.formLabelTextcolor}`}>*</span>
              </label>
              <input
                className={`form-control ${classes.forminput} border-0 `}
                type="text"
                placeholder='Enter your organization name here'
                id="organisation"
                name="organisation"
                autoComplete='off'
                value={formData.organisation}
                onChange={handleChange}
                onKeyPress={(event) => {
                  if (!/^[A-Za-z. ]$/.test(event.key)) {
                    event.preventDefault();
                  }
                }}
              />
              {formErrors.organisation && <div className={`${classes.error_tooltip} mb-5`}>{formErrors.organisation}</div>}
            </div>
          
            <div className="form-group col-md col-12 ps-1 py-1">
              <label
                className={`control-label ${classes.formLabelText} fw-bold`}
                htmlFor="no_of_participants"
              >
                {landingPageResponse.signup_form.Number_of_Participants}
                <span className={`${classes.formLabelTextcolor}`}>*</span>
              </label>
            <input
              className={`form-control ${classes.forminput} border-0 `}
              placeholder='Enter number of participants'
              maxLength="5"
              type="number"
              min={1}
              id="no_of_participants"
              name="no_of_participants"
              value={formData.no_of_participants}
              onChange={handleChange}
              onKeyPress={(event) => {
                if (!/^[0-9]$/.test(event.key)) {
                  event.preventDefault();
                }
              }}
              onPaste={(event) => {
                 const pastedData = event.clipboardData.getData("text");
                  if (!/^\d+$/.test(pastedData)) {
                    event.preventDefault();
                  }
                }}
              />
              {formErrors.no_of_participants && <div className={`${classes.error_tooltip} mb-5`}>{formErrors.no_of_participants}</div>}
            </div>
          </div>
         

          <div className={`mt-1 pt-1 ${classes.disablePointer} ps-1`}>
          <label style={{ fontSize: "13px" }} htmlFor="agree">
           
            {landingPageResponse.group_enrolment.disclaimer_text}
          </label>
        </div>


        <div className="text-center">
          <button
            disabled={buttonDisabled}
            className={`${ classes.land_btn} ${ "my-3" } text-white `}
            type="submit"
          >
            {landingPageResponse.group_enrolment.button_text}
          </button>
        </div>
      </form>
      </div>

    </div> 
  )
}

export default GroupEnrolment