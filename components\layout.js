import useSWR from 'swr'
import classes from "./layout.module.css";
import Link from "next/link";
import useCollapse from "react-collapsed";
import { config } from "@fortawesome/fontawesome-svg-core";

const fetcher = async () => {
    const response = await fetch("https://isbstrapi.quantana.top/api/bottom-fold?populate=*")
    const data = await response.json()
    return data
}

export default function Layout() {
 
    const { data, error } = useSWR("https://isbstrapi.quantana.top/api/bottom-fold?populate=*", fetcher);
    if (error) return 'ERROR'
    if (data) <h1>Loading...</h1>;

    const Collapse = (props) => {
        const { getCollapseProps, getToggleProps, isExpanded } = useCollapse();
        const [lessons, setLessons] = useState([props.faq_answer]);
        return (
          <>
            <div>
              <h5
                className={`${classes.faqQuestion} d-flex justify-content-between text-white m-0 mb-2`}
                {...getToggleProps()}
              >
                {props.faq_question}
                {isExpanded ? C : E}
              </h5>
              <div {...getCollapseProps()}>
                <div
                  className={`${classes.faqAnswer} p-0 m-0 text-white ${classes.liStyle}`}
                  dangerouslySetInnerHTML={{
                    __html: props.faq_answer,
                  }}
                ></div>
              </div>
            </div>
            <div className={`mb-3 p-0 ${classes.foottitle}`}></div>
          </>
        );
      };

    return (
      <></>
    )
  }