import classes from "./blog.module.css";
import Image from "next/image";
import Link from "next/link";

import BottomFold from "../components/bottom_fold_one";
import B11 from "../assets/desktopgirl Large.webp"
import B12 from "../assets/B22.jpg"
import { NextSeo } from 'next-seo';


export default function blog(props) {

  return (
    <>
          <NextSeo
            title="Immersive Learning"
            description="Learner Centricity and Immersive Social Learning: How ISB Online Empowers Learners to Succeed"
        />

      <div>

        <div div className={`${classes.fold1bg}`}>
          <div className={`row flex-row-reverse col-lg-12  mx-auto ${classes.equalPadding}`}>
            <Image
              width="0"
              height="0"
              sizes="100vw"
              className="img-fluid col-lg-6 col-md-12 p-0 py-lg-4"
              src={B11}
            alt="desktop girl large"
            />

            <div className="col-lg-6 px-lg-0 px-3 py-lg-0 py-4 col-md-12 align-items-center d-flex justify-content-center pe-lg-4">
              <div>
                <h1 className={`text-white ${classes.heading}`}>
                  Insights
                </h1>
                <h2 className={`my-3 ${classes.description}`}>Learner Centricity and Immersive Social Learning: How ISB Online Empowers Learners to Succeed</h2>
              </div>
            </div>
          </div>

        </div>
        <div className={`row flex-row-reverse col-lg-12 mt-4 mx-auto ${classes.equalPadding} py-0 pb-lg-2 py-md-0`}>
          <p className="p-lg-0 px-3">
          Traditional classroom learning has been the norm for generations, but with technological advancements and changes in the workforce, the way we learn has evolved. Today, learners are seeking more engaging and interactive learning experiences that allow them to connect with their peers and instructors, and to apply their learning to real-world scenarios.           </p>

          <p className="p-lg-0 px-3">
          ISB Online believes that its focus on learner-centricity and immersive social learning are powerful change agents for learners. Learner-centricity is a common thread that runs through each course through various learning strategies – microlearning, reflection, engagement, interaction, and a gamified point system. The immersive learning approach, on the other hand, allows learners to be fully immersed in their learning environment, making it feel more like a real-life scenario. With immersive learning, learners can interact with their environment and their peers, allowing for a more meaningful and memorable learning experience.          </p>
        </div>

        <div className={`row flex-row-reverse col-lg-12 m-0 mx-auto ${classes.equalPadding} py-lg-5 py-1`}>
          <Image
            className="col-lg-5 col-md-12 col-12 p-0 m-0"
            // height={250}
            src={B12}
          alt="Learning progress image"
          />
          <div className="col-lg-7 px-lg-0 px-3 py-lg-0 col-md-12 col-12 d-flex justify-content-center">
            <p className="pe-lg-4 pt-lg-0 pt-4 m-0">
            ISB Online recognizes the importance of immersive learning and has incorporated this approach into its courses and programmes. For example, in the &nbsp;
             <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}/management-essentials`}>Management Essentials</Link> track, learners use simulated scenarios to learn how to manage high-performance teams, negotiate effectively, and manage projects. Learners can manipulate data and test their decision-making skills, allowing them to understand the impact of their decisions in a risk-free environment. 
             <br/><br/>Similarly, in the <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}/leadership-essentials`}>Leadership</Link> track, learners use simulations to learn how to lead and manage teams effectively.          
            </p>
          </div>


        </div>
 

        <div className={`row flex-row-reverse col-lg-12 m-3 mx-auto ${classes.equalPadding} py-0 pb-lg-1 py-md-0`}>
         
          <p className="p-lg-0 px-3">
          Through this immersive learning focus, the following benefits are available for all learners to take advantage of:
          </p>

          <div>
            <ul>
              <li>
              Enhanced Retention              
              </li>
              <li>
              Improved Engagement
              </li>
              <li>
              Better Decision Making
              </li>
              <li>
              Safe Learning Environment
              </li>
            </ul>
          </div>
          <p className="p-lg-0 px-3">
          ISB Online&apos;s learner-centricity and immersive learning approaches are indeed a testament to its commitment to providing a world-class education that meets the needs of learners in today&apos;s digital age.          </p>
        </div>

        <div className="text-center">
          <Link rel="canonical" href="/learning-tracks">
            <button className={`${classes.showBtn} mb-5`} type="submit">
              Explore Learning Tracks
            </button>
          </Link>
        </div>

        <BottomFold data={props.bottomFoldData}></BottomFold>
      </div>
    </>
  );
}
export async function getStaticProps(context) {
  const APIUrl = process.env.API_BASE_URL;
  const [tqData, bottomFoldData] = await Promise.all([
    fetch(`${APIUrl}/api/thankyou-ref`).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      tqData: tqData,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData
    },
  };
}