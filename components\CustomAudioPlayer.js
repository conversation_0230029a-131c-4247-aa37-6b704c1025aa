import React, { useState, useRef, useEffect, memo } from 'react';
import styles from './Audio.module.css';
import backwardicon from '../assets/Backward10.svg';
import forwardicon from '../assets/Forward 10.svg';
import Image from 'next/image';

const PlayIcon = memo(() => (
  <svg width="28" height="28" viewBox="0 0 24 24" fill="white">
    <path d="M8 5v14l11-7z"/>
  </svg>
));

const PauseIcon = memo(() => (
  <svg width="28" height="28" viewBox="0 0 24 24" fill="white">
    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
  </svg>
));

const Rewind10Icon = memo(() => (
  <Image
    src={backwardicon}
    alt="Rewind 10 seconds"
    width={20}
    height={20}
  />
));

const Forward10Icon = memo(() => (
  <Image
    src={forwardicon}
    alt="forward 10 seconds"
    width={20}
    height={20}
  />
));

PlayIcon.displayName = 'PlayIcon';
PauseIcon.displayName = 'PauseIcon';
Rewind10Icon.displayName = 'Rewind10Icon';
Forward10Icon.displayName = 'Forward10Icon';

export const CustomAudioPlayer = ({ audioUrl }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [progress, setProgress] = useState(0);
  const audioRef = useRef(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration || 0);
    };

    const handleTimeUpdate = () => {
      if (audio.duration) {
        setCurrentTime(audio.currentTime);
        setProgress((audio.currentTime / audio.duration) * 100);
      }
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      setProgress(0);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);

    if (audio.readyState >= 2) {
      setDuration(audio.duration || 0);
    }

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
    };
  }, []);

  const togglePlayPause = () => {
    if (!audioRef.current) return;
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e) => {
    const value = parseFloat(e.target.value);
    const newTime = (value / 100) * duration;
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
      setProgress(value);
    }
  };

  const handleForward = () => {
    if (!audioRef.current) return;
    const newTime = Math.min(audioRef.current.currentTime + 10, duration);
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
    setProgress((newTime / duration) * 100);
  };

  const handleBackward = () => {
    if (!audioRef.current) return;
    const newTime = Math.max(audioRef.current.currentTime - 10, 0);
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
    setProgress((newTime / duration) * 100);
  };

  const formatTime = (time) => {
    if (!time || isNaN(time)) return "00:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={styles.audioPlayer}>
      <div className={styles.playerContent}>
        <div className={styles.playerControls}>
          <div className={styles.playButtonContainer}>
            <button
              className={styles.playPauseButton}
              onClick={togglePlayPause}
              aria-label={isPlaying ? 'Pause' : 'Play'}
            >
              {isPlaying ? <PauseIcon /> : <PlayIcon />}
            </button>
          </div>
            <div className='d-flex justify-content-between mb-2'>
              <button className={styles.controlButton} onClick={handleBackward} aria-label="Back 10 Seconds">
                <Rewind10Icon />
              </button>
              <button className={styles.controlButton} onClick={handleForward} aria-label="Forward 10 Seconds">
                <Forward10Icon />
              </button>
            </div>
          <div className={styles.progressContainer}>
              <input
                type="range"
                min="0"
                max="100"
                value={progress}
                className={styles.progressBar}
                onChange={handleSeek}
                style={{ '--progress-value': `${progress}%` }}
              />
           
          </div>
          <div className={styles.playerProgress}>
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>
        <audio
          ref={audioRef}
          src={audioUrl || "https://commondatastorage.googleapis.com/codeskulptor-demos/DDR_assets/Kangaroo_MusiQue_-_The_Neverwritten_Role_Playing_Game.mp3"}
        />
      </div>
    </div>
  );
};