import { Modal } from "react-bootstrap";
import { LeadForm } from "../components/LeadForm";
import classes from "../pages/v2/[learning_track_id]/index.module.css";
import { GetLocation } from "./GeoLocation";
import { useEffect, useState } from "react";

export const LeadFormModal = ({
  getLoc,
  landingPageResponse,
  submitContact,
  formData,
  formErrors,
  handleChange,
  handleChange1,
  buttonDisabled,
  handleCloseModal,
  apiUrl,
  checkboxHandler,
  leadModal,
}) => {
  const [currentLocation, setCurrentLocation] = useState({ city:'' , state: '', country: ''});


  useEffect(() => {
    if (leadModal) {
      const fetchLocation = async () => {
        try {
          const loc = await GetLocation();
          setCurrentLocation(loc);
          getLoc(loc)
        } catch (error) {
          console.error("Error fetching location:", error);  
        }
      };
      fetchLocation();
    }
  }, [leadModal]);
  

  return (
    <Modal
      show={leadModal}
      onHide={handleCloseModal}
      className="d-flex rounded-0"
      backdrop="static"
      dialogClassName={classes.modalstyle}
      aria-labelledby="example-custom-modal-styling-title"
    >
      <Modal.Header closeButton className="py-2">
        <p className={classes.blueBoldText}>Get Started on ISB Online</p>
      </Modal.Header>
      <Modal.Body
        className={`overflow-auto px-1 pt-0 d-flex align-items-center pb-lg-0 pb-2 ${classes.modal_body}`}
      >
        <LeadForm
          trackpage={true}
          currentLocation={currentLocation}
          myclass={"col-lg-12 pt-0 p-3 h-100 overflow-auto"}
          checkboxHandler={checkboxHandler}
          buttonDisabled={buttonDisabled}
          handleChange1={handleChange1}
          handleChange={handleChange}
          formErrors={formErrors}
          formData={formData}
          submitContact={submitContact}
          landingPageResponse={landingPageResponse}
        />
      </Modal.Body>
    </Modal>
  );
};
