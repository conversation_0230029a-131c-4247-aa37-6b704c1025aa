import Image from 'next/image';
import React from 'react';
import Link from 'next/link';
import styles from './index.module.css';
import BottomFold from '../../components/bottom_fold_one';

const Partnerprogrammes = ({pageData, apiUrl, bottomFoldData}) => {
  const cardData = pageData.data.attributes.programcard;
  const pageDetails = pageData.data.attributes;

  return (<>
    <div className={styles.partnerProgrammesWrapper}>
     
      <div className='ps-4 ms-1 ps-xxl-0 ms-xxl-0'>
      <div className={`${styles.breadcrumb} d-none d-xl-flex ${styles.mainTitle} mx-auto align-items-center`}>
          <div className={`d-flex ${styles.breadCrumb} px-0`}>
            <Link style={{ textDecoration: "none" }} href="/">
              <p className="m-0">ISB Online</p>
            </Link>
            <svg
              style={{ fill: "#00000" }}
              width="24"
              height="18"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_3275_9282)">
                <path d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z" />
              </g>
              <defs>
                <clipPath id="clip0_3275_9282">
                  <rect width="24" height="24" fill="#057092" />
                </clipPath>
              </defs>
            </svg>
      
            <p className={`m-0 ${styles.breadCrumblast}`}>
            Partner Programmes
            </p>
          </div>
        </div>
       
        <h1 className={styles.mainTitle}>{pageDetails.title}</h1>
    </div>
      <div className={styles.horizontalLine}>
        <div className={styles.mainContent}>
          {/* Header Section */}
          <div className={styles.headerSection}>
            {/* Title at the top with horizontal line */}

            {/* Content row with description and image */}
            <div className="row align-items-center d-none d-xl-flex">
              <div className="col-lg-6">
                <p className={styles.mainDescription}>
                  {pageDetails.description}
                </p>
              </div>
              <div className="col-lg-6">
                {pageDetails.image && <Image
                  width="0"
                  height="0"
                  sizes="100vw"
                  className={`img-fluid ${styles.headerImage}`}
                  src={apiUrl + pageDetails.image.data.attributes.url}
                  priority={true}
                  alt={pageDetails.image.data.attributes.alternativeText}
                />}
              </div>
            </div>
            <div className="row align-items-center d-block d-xl-none">
            
            <div className="col-xl-6">
              {pageDetails.image && <Image
                width="0"
                height="0"
                sizes="100vw"
                className={`img-fluid ${styles.headerImage}`}
                src={apiUrl + pageDetails.image.data.attributes.url}
                priority={true}
                alt={pageDetails.image.data.attributes.alternativeText}
              />}
            </div>

            <div className="col-xl-6">
              <p className={styles.mainDescription}>
                {pageDetails.description}
              </p>
            </div>
          </div>
          </div>

          {/* Programme Cards Grid */}
          <div className={styles.programmeGrid}>
            <div className="row g-4">
              {cardData.map((card, index) => {
                return (
                  <div key={index} className="col-lg-4 col-md-6 col-sm-12">
                    <div className={styles.programmeCard}>
                      <h2 className={styles.cardTitle}>{card.title}</h2>
                      <p className={styles.cardDescription}>{card.description}</p>

                      <div className={styles.exploreLink}>
                        <div className={styles.exploreLinkWrapper}>
                          <a href={card.link} rel="noopener noreferrer" target='_blank' className={styles.exploreLinkText}>{pageDetails.explore_title}</a>
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className={styles.arrowIcon} viewBox="0 0 16 16">
                            <path d="M7.5 1.75a.75.75 0 0 1 .75-.75h5.59c.414 0 .75.336.75.75v5.59a.75.75 0 0 1-1.5 0V3.56L2.22 14.44a.75.75 0 1 1-1.06-1.06L12.03 2.5H8.25a.75.75 0 0 1-.75-.75z" />
                          </svg>
                        </div>
                      </div>

                      {card.image && card.image.data && (
                        <div className={styles.cardImageContainer}>
                          <Image
                            width="0"
                            height="0"
                            sizes="100vw"
                            className={styles.cardImage}
                            src={apiUrl + card.image.data.attributes.url}
                            priority={true}
                            alt={card.image.data.attributes.alternativeText || card.title}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
    <BottomFold faqsection={false} data={bottomFoldData}></BottomFold>
  </>
  )
}

export default Partnerprogrammes


export async function getStaticProps() {
  const APIUrl = process.env.API_BASE_URL;
  const [pageData, bottomFoldData] = await Promise.all([
    fetch(`${APIUrl}/api/partner-program?populate=deep,4`).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
     
  ]);
  return {
    props: {
      pageData: pageData,
      bottomFoldData: bottomFoldData,
      apiUrl: APIUrl,
    },
    revalidate: 240
  };
}