import Image from "next/image";
import { CertificateModal } from "./CertificateModal";
import { useEffect, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { CaseletNextArrow, CaseletPrevArrow } from "./FacultySlider";


export const WhatYouGain = ({flag, classes, title, certificateData, apiUrl, bdcnote }) => {
  const [certificateModal, setCertificateModal] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [imgData, setImgData] = useState([]);
  const [completionData, setCompletionData] = useState();
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    setIsClient(true);
    const fetchData = async () => {
      try {
        const response = await fetch(`${apiUrl}/api/learning-track-completion?populate[module_badge][populate]=*&populate[course_certificate][populate]=*&populate[master_certificate][populate]=*`);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }
        const data = await response.json();
        setCompletionData(data.data.attributes);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();

    const handleResize = () => {
      setIsMobile(window.innerWidth <= 991);
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Check the initial window size

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [apiUrl]);

  const openCertificate = (img, index) => {
    if (index !== 2) {
      setImgData(img);
      setCertificateModal(true);
    }
  };

  const handleCloseModal = () => {
    setCertificateModal(false);
  };

  if (!isClient) {
    return null;
  }
  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 400,
    // autoplay: true,
    // autoplaySpeed: 8000,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
    nextArrow: <CaseletNextArrow paddinngLeft={"0px"}/>,
    prevArrow: <CaseletPrevArrow paddinngLeft={"0px"}/>,
  };
  return (
    <>
      {completionData && (
        <div className={`justify-content-center ${flag ? "pt-4" : 'bg-white shadow-sm pt-4'} pb-lg-4 p-2 pb-5 `}>
          <h2 className={` ${flag ? classes.main_blue_head : classes.sideHeads} text-center`}>
            {title}
          </h2>
          <div className="row px-5">
            {isMobile ? (
              <Slider {...sliderSettings} className="px-0">
                {certificateData.map((img, index) => {
                  const certificateUrl = img.Certificate_Image ? (img.Certificate_Image.data.attributes.url) : img.certificate_image.data.attributes.url;
                  return(
                  <div key={index} className=" text-center mt-3">
                    <div className={classes.image_container}>
                      <Image
                        height={200}
                        priority={true}
                        width={200}
                        onClick={() => openCertificate(img)}
                        sizes="(max-width: 640px) 100vw, (max-width: 1280px) 50vw, 25vw"
                        className={`img-fluid ${index !==2 ? "pointer" :"c-default"}`}
                        style={{ objectFit: "cover" }}
                        src={
                          apiUrl + ( certificateUrl)
                        }
                        alt={
                          completionData.master_certificate.certificate_image.data
                            .attributes.alternativeText
                        }
                      />
                    </div>
                    <div className="mt-3">
                      <h3 className={classes.fontEighteenBlue}>
                        {img.title || img.certificate_title}
                      </h3>
                      <p className={classes.grey_text}>
                        {img.description || img.certificate_short_description}
                      </p>
                    </div>
                  </div>
                )})}
              </Slider>
            ) : (
              certificateData.map((img, index) => {
                const certificateUrl = img.Certificate_Image ? (img.Certificate_Image?.data?.attributes?.url) : img.certificate_image.data.attributes.url;
                return(
                <div key={index} className="col-lg-4 col-12 p-2 text-center mt-3">
                  <div className={classes.image_container}>
                   {certificateUrl&&  <Image
                      height={200}
                      priority={true}
                      width={200}
                      onClick={() => openCertificate(img, index)}
                      sizes="(max-width: 640px) 100vw, (max-width: 1280px) 50vw, 25vw"
                      className={`img-fluid ${index !==2 ? "pointer shadow-sm" :"c-default"}`}
                      style={{ objectFit: "cover" }}
                      src={
                        apiUrl + (certificateUrl)
                      }
                      alt={
                        completionData.master_certificate.certificate_image.data
                          .attributes.alternativeText
                      }
                    />}
                  </div>
                  <div className="mt-3">
                    <h3 className={classes.fontEighteenBlue}>
                      {img.title || img.certificate_title}
                    </h3>
                    <p className={classes.grey_text}>
                      {img.description || img.certificate_short_description}
                    </p>
                  </div>
                </div>
              )})
            )}
            <p className={`px-3 px-lg- 0 fs-6 mt-5 mt-lg-2 ${classes.note_color  ,classes.bdc_note_mobile}`} style={{fontStyle:"italic",fontFamily: "sans-serif"}}>
                        {bdcnote}
                      </p>
            <CertificateModal
              apiUrl={apiUrl}
              handleCloseModal={handleCloseModal}
              certificateModal={certificateModal}
              imgData={imgData}
            />
          </div>
        </div>
      )}
    </>
  );
};
