// components/CardContainer.js
import React from 'react';
import Card from './Card';
import styles from './CardContainer.module.css';

const CardContainer = ({ cards, lpv2flag }) => {


    
  return (

 
    <div className={styles.cardContainer}>
      {cards.map((card, index) => (

        <Card lpv2flag={lpv2flag} key={index} content={card.content} backgroundImage={card.backgroundImage} />
       
      ))}
    </div>
 
  );
};

export default CardContainer;
