'use client'
import classes from './ThankYouSection.module.css'
import download from "../assets/Download.svg";
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { StartYourApplicationBtn } from './StartYourApplicationBtn';
import CryptoJS from 'crypto-js';

export const ThankYouSection = ({landingpageflag, landingPageResponse,trackFoldData,baseURL,andQpms,qpms,learnTrackData, sfthankyouflag, apiUrl,handleDownload,meetupId }) => {

    const [thankyouData, setThankyouData] = useState()
    const [isClient, setIsClient] = useState(false)
    const [loading, setLoading] = useState(false);
    const [fullUrl, setFullUrl] = useState('');

    useEffect(() => {
        setIsClient(true)


        if (typeof window !== 'undefined') {
          const cookies = document.cookie
            .split(';')
            .map((cookie) => cookie.trim())
            .reduce((acc, cookie) => {
              const [key, value] = cookie.split('=');
              acc[key] = decodeURIComponent(value);
              return acc;
            }, {});
    
          const {
            leadform_name,
            leadform_email,
            leadform_mobile,
            leadform_ProgramId,
            leadform_role,
            leadform_country_code,
            leadform_id,
            leadform_location,
            leadform_years_of_experience
          } = cookies;
    
          const countryCode = localStorage.getItem('countryCode');
    
          const formData = {
            name: leadform_name,
            email: leadform_email,
            role: leadform_role,
            mobile: leadform_mobile,
            checkBox: true,
            countryCode: leadform_country_code || countryCode,
            programId: leadform_ProgramId,
            location : leadform_location,
            years_of_experience : leadform_years_of_experience,
            leadform_id : leadform_id

          };
    
          const secretKey = process.env.NEXT_PUBLIC_CRYPTO_KEY; // Use a secure secret key
          const encryptedParams = CryptoJS.AES.encrypt(
            JSON.stringify(formData),
            secretKey
          ).toString();
    
          const allValuesValid = Object.values(formData).every(
            (value) => value !== undefined && value !== 'undefined' && value !== null && value !== ''
          );
    
          if (allValuesValid) {
            // const params = new URLSearchParams(formData); 
           
            const fullUrlWithParams = `?params=${encodeURIComponent(encryptedParams)}`;
            setFullUrl(fullUrlWithParams);  
          } else {
            setFullUrl('');  
          }
      
        const decryptParams = (encryptedString) => {
          const bytes = CryptoJS.AES.decrypt(encryptedString, secretKey);
          const decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
          return decryptedData;
        };
        const decryptedData = decryptParams(encryptedParams);
    
      }



        const fetchData = async () => {
          try {
            const response = await fetch(`${apiUrl}/api/thankyousection?populate=*`);
            if (!response.ok) {
              throw new Error(`API request failed with status ${response.status}`);
            }
            const data = await response.json();
            setThankyouData(data.data.attributes)
          } catch (error) {
            console.error('Error fetching data:', error);
          }
        };
    
        fetchData();
      }, [apiUrl]);
      if(!isClient){
        return null
      }
      const handleDownloadWithLoader = async () => {
        try {
            setLoading(true);  
            await handleDownload();  
        } finally {
            setLoading(false);  
        }
    };
 

    return (
       <>{thankyouData && <div className={`${classes.thankyoumain} col-12`} style={{marginTop:landingpageflag ? "80px":""}}>
            <div className={classes.check_mark} />
            <div className={classes.thankyoutitle}>
                <h1>{thankyouData.title}</h1>
                <h2>{thankyouData.sub_heading}</h2>
            </div>

            {loading &&  <div className={`${classes.equalPadding} position-relative`}>
        <div className={`spinner-border  isb-text-color position-absolute`} style={{width:"20px", height:'20px'}} role="status"/>
      </div>} 
      
            {thankyouData.options_section.map((i)=>(
                <div key={i.id} className={classes.text_icon_section}>
                <div className={classes.icon}>
                    <Image src={download} alt='download_icon'></Image>
                </div>
                
                <div className={classes.textSection}>

                  <h6
                    className={`${classes.heading} ${loading ? classes.disabled_title : ''}`}
                    onClick={!loading ? handleDownloadWithLoader : null} // Disable click while loading
                  >
                    {i.title}
                  </h6>

                    <span className={classes.description}>
                        {i.description}
                    </span>
                </div>
            </div>))}
             
            <StartYourApplicationBtn
            fullUrl={fullUrl}
                learnTrackData={learnTrackData}
                landingPageResponse={landingPageResponse}
                qpms={qpms}
                andQpms={andQpms}
                baseURL={baseURL}
                trackFoldData={trackFoldData}
                classes={classes}
            /> 
        </div>
        }
        </>
    )
}