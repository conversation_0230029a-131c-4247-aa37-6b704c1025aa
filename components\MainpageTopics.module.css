/* components/MainpageTopics.module.css */
.sliderContainer {
  display: flex;
  justify-content: center;
  width: 100%;
  margin: 10px auto 30px;
  position: relative;
}

.sliderWrapper {
  position: relative;
  max-width: 100%;
  margin: 0 auto;
  overflow: visible !important; /* Allow dropdown to be visible */
  box-sizing: border-box;
}

/* Show buttons on hover in desktop */
.sliderWrapper:hover .navButton {
  opacity: 1;
}

.sliderOverflowFix {
  width: 100%;
  overflow: visible !important; /* Changed to visible */
  position: relative;
  z-index: 1; /* Base z-index */
  min-height: 390px; /* Added increased height for more dropdown space */
}

.slider {
  width: 100%;
  overflow: visible !important; /* Must remain visible for dropdown */
}

.slider :global(.slick-list) {
  padding: 20px 0 !important; /* Add padding for the dropdown */
  margin: 0 !important; /* Reset margins */
  /* min-height: 450px; */
  overflow: visible !important; /* Critical for dropdown visibility */
}

.slider :global(.slick-track) {
  display: flex;
  margin-left: 0;
  margin-right: 0;
  overflow: visible !important;
  justify-content: flex-start !important; /* Align items to start */
}

.slider :global(.slick-slide) {
  /* width: 250px !important;  */
  height: auto !important;
  padding: 0 15px; /* Smaller padding for tighter layout */
  box-sizing: border-box;
  flex: 0 0 auto !important; /* Prevent flex stretching */
}

@media (max-width: 768px) {
  .sliderOverflowFix {
    min-height: 310px;  
  }
  .slider :global(.slick-slide) {
    padding: 0 5px;
  }
  
  .slider :global(.slick-list) {
    overflow: hidden !important;
    padding: 10px 0 !important;
  }
}

.slider :global(.slick-slide > div) {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  overflow: visible !important; /* Ensure dropdown visibility */
}

.slider :global(.slick-slide.slick-active) {
  opacity: 1;
  visibility: visible;
    
}

.slider :global(.slick-dots) {
  bottom: -30px;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.slideContainer {
  padding: 10px 0;
  height: 320px;
  position: relative;
  overflow: visible !important; /* Critical for dropdown visibility */
  display: flex;
  justify-content: center;
  width: 100%;
}

.navButton {
  position: absolute;
  top: 35%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #fff;
  border: none;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
  /* Hide by default on desktop */
  opacity: 0;
  /* Ensure buttons are always on top and not cut off */
  position: absolute;
}

 

.prevButton {
  left: -1px;
}

.nextButton {
  right: -1px;
}

.card {
  width: 210px;
  /* height: 280px; */
  transition: all 0.3s ease;
  cursor: pointer;
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  display: flex;
  flex-direction: column;
  overflow: visible !important; /* Critical for dropdown */
  padding: 0;
  border-radius: 10px;
}

.cardImageContainer {
  width: 210px;
  height: 202px;
  position: relative;
  flex-shrink: 0;
  align-items: end;
  background-color: #182891;
}

.cardImageContainer img{
  width: 210px;
  height: 202px;
  object-fit: cover;
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.5s ease;
}

.card:hover .cardImageContainer img{
  left: 10px;
  bottom: 12px;
}

/* Styles for dots navigation in mobile view */
.mobileDots {
  bottom: -30px;
  width: 100%;
  display: flex !important;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.mobileDots li {
  margin: 0 4px;
}

.mobileDots li button {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ccc;
  opacity: 0.5;
  padding: 0;
}

.mobileDots li button:before {
  content: '';
  display: none;
}

.mobileDots li.slick-active button {
  opacity: 1;
  background: #0041E9;
}

/* Mobile container class */
.mobileContainer {
  overflow: visible !important;
  width: 100% !important;
  padding: 0 10px;
  box-sizing: border-box;
}

.containerOverrideHidden {
  overflow: hidden !important;
}

.loadingContainer {
  text-align: center;
  padding: 40px 0;
  color: #182891;
  font-family: var(--isb-edge-font-family-inter);
  font-size: 16px;
  width: var(--isb-edge-width-1200);
  margin: 10px auto 30px;
}

.containerOverrideHidden {
  overflow: visible !important;
}

/* Styling for default Slick arrows */
.slider :global(.slick-prev),
.slider :global(.slick-next) {
  display: none !important; /* Hide by default, show only on mobile */
}

/* Mobile view styles */
@media (max-width: 768px) {
  /* Hide custom navigation buttons on mobile */
  .navButton {
    display: none !important;
  }
  
  .sliderContainer {
    margin: 10px auto 20px;
    width: 100%;
  }
  
  .sliderWrapper {
    width: 100%;
  }
  
  .card {
    width: calc(100% - 30px);
    position: relative;
    left: 0;
    transform: none !important;
    margin: 0 auto;
    transition: none !important;
  }
  
  .cardImageContainer {
    width: 100%;
    /* height: auto; */
    aspect-ratio: 1/1;
    overflow: hidden;
    position: relative;
  }
  
  .cardImageContainer img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover;
    position: absolute;
    left: 0;
    bottom: 0;
    transform: none;
    transition: none;
  }
  
  .topicCardTitle {
    font-size: 16px;
  }
  
  .dropdownContent {
    font-size: 12px;
  }
  
  /* Style default Slick arrows for mobile */
  .slider :global(.slick-prev),
  .slider :global(.slick-next) {
    display: block !important;
    width: 24px;
    height: 24px;
    z-index: 10;
    top: 46%;
    /* background-color: white; */
    /* border-radius: 50%; */
    /* box-shadow: 0 2px 5px rgba(0,0,0,0.2); */
  }
  
  .slider :global(.slick-prev) {
    left: -12px;
  }
  
  .slider :global(.slick-next) {
    right: -12px;
  }
  
  .slider :global(.slick-prev:before),
  .slider :global(.slick-next:before) {
    color: #182891;
    font-size: 14px;
    opacity: 0.8;
  }
  
  /* Style for disabled slick arrows */
  .slider :global(.slick-disabled),
  .slider :global(.slick-disabled:before),
  .slider :global(.slick-disabled:hover) {
    opacity: 0.5 !important;
    cursor: default !important;
    pointer-events: none !important;
    /* color: #cccccc !important; */
  }
  
  /* Custom arrow style for better control */
  .slickPrevArrow,
  .slickNextArrow {
    position: absolute;
    display: block !important;
    width: 24px;
    height: 24px;
    z-index: 10;
    background: transparent;
    border: none;
    cursor: pointer;
  }
  
  .slickPrevArrow:before,
  .slickNextArrow:before {
    font-family: 'slick';
    font-size: 14px;
    line-height: 1;
    color: #182891;
    opacity: 0.8;
  }
  
  .slickPrevArrow {
    left: -12px;
  }
  
  .slickPrevArrow:before {
    content: '←';
  }
  
  .slickNextArrow {
    right: -12px;
  }
  
  .slickNextArrow:before {
    content: '→';
  }
  
  /* Disabled state */
  .arrowDisabled {
    opacity: 0.8 !important;
    cursor: default !important;
    pointer-events: none !important;
  }
  
  .arrowDisabled:before {
    color: #cccccc !important; /* Changed to gray when disabled */
  }
  
  /* Adjust padding and margin */
  .sliderContainer {
    /* padding-bottom: 20px; */
    margin-bottom: 0;
    overflow: visible !important;
  }
  
  /* Adjust arrow positions for mobile */
  .prevButton {
    left: -10px;
  }
  
  .nextButton {
    right: -10px;
  }
  
  /* Disable hover effects */
  .card:hover .cardImageContainer img {
    left: 0;
    bottom: 0;
  }
  
  .card:hover {
    transform: none !important;
  }
  
  .card:hover .cardContent {
    padding-top: 15px;
  }
  
  /* Hide all paragraph content except the title */
  .cardContent div {
    display: none;
  }
  
  /* Hide "View All" link */
  .cardContent a[href="/perspectives/alltopics"] {
    display: none;
  }
  
  /* Adjust card height for mobile */
  .slideContainer {
    height: auto;
    min-height: 260px;
    width: 90% !important;
  }
  
  /* Full width card for mobile */
  .card {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* Full width image container for mobile */
  .cardImageContainer {
    width: 100% !important;
    transform: none !important;
  }
  
  .cardImageContainer img {
    width: 100% !important;
    height: 100% !important;
    transform: none !important;
  }
  
  /* Slider adjustments for full width */
  .slider :global(.slick-slide) {
    padding: 0 !important;
  }
  
  /* Remove any transitions and transforms on mobile to prevent distortion */
  .card, .cardContent, .cardImageContainer, .cardImageContainer img {
    transition: none !important;
    transform: none !important;
    animation: none !important;
  }
}
.cardImage {
  object-fit: cover;
}

.cardContent {
  padding: 15px 10px 0 0;
  transition: all 0.3s ease;
  flex-shrink: 0;
  opacity: 0.8; /* Slightly transparent */
}
.card:hover .cardContent {
  padding-top: 2px;
}
.cardContent h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-family: var(--isb-edge-font-family-Reckless);
  font-size: 24px;
  font-weight: 600;
  line-height: 100%;
  letter-spacing: 0;
  color: #182891;
  transition: all 0.3s ease;
}

.cardContent p {
  margin: 0;
  font-size: 13px;
  color: #FFFFFF;
  font-family: var(--isb-edge-font-family-inter);
  padding-left: 10px;
  opacity: 0; /* Hide paragraphs by default */
  max-height: 0; /* Collapse paragraphs */
  transition: all 0.3s ease;
  line-height: 1.2;
}

.cardContent .dropdownDivider {
  border-bottom: 1px dashed #cccccc;
  margin: 5px auto;
  width: 90%;
  opacity: 0;
}
/* New detached dropdown styling */
.detachedDropdown {
  position: fixed; /* Changed to fixed to break out of all stacking contexts */
  z-index: 99999; /* Super high z-index */
  pointer-events: auto;
  box-sizing: border-box;
  width: 240px;
  left: 50%;
  transform: translateX(-50%);
}

.cardList {
  background-color: #f3f3f3;
  padding: 10px 0;
  margin: 0;
  list-style-type: none;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); /* Stronger shadow for depth */
  animation: fadeIn 0.2s ease-in-out;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cardList li {
  padding: 8px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.cardList li:last-child {
  border-bottom: none;
}

.cardList li a {
  color: #333;
  text-decoration: none;
  transition: color 0.2s ease;
}

 
/* Rotated chevron for left navigation button */
.rotatedChevron {
  transform: rotate(180deg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.card:hover {
  transform: translateX(-50%) scale(1.1);
  z-index: 9998; /* High z-index */
  /* box-shadow: 0 10px 20px rgba(0,0,0,0.15); */
  overflow: visible !important;
}

.card:hover .cardContent {
  transform: translateY(-5px);
  height: auto; /* Expand to full height on hover */
  max-height: 230px; /* Slightly reduced maximum height */
  opacity: 1; /* Full opacity */
  background-color: var(--isb-edge-blue-font-color);
}
.card:hover .cardContent .dropdownDivider {
  opacity: 1;
}
.card:hover .cardContent h3 {
  color: white;
  padding-left: 10px;
  transition: all 0.3s ease;
  font-family: var(--isb-edge-font-family-Reckless);
}
.card:hover .cardContent p {
  opacity: 1;  
  max-height: 40px;  
  margin: 0 10px;
  padding-bottom: 2px;  
}
.dropdownContent{
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  -webkit-box-orient: vertical;
  margin-bottom: 2px;
}
.card:hover .topicCardTitle {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  -webkit-box-orient: vertical;
}
