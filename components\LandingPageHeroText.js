import React from 'react';
import { StarRating } from './StoriesSlider';

const LandingPageHeroText = ({ isMobile, classes, title, starColor, starText, rating }) => {
  return (
    <div className={''}>
      <div className={`${classes.programTitle} p-3 m-0 text-center`}>
        {title.includes('<br/>') ? (
          (() => {
            const parts = title.split('<br/>').map((part) => part.trim()); // Split by <br/> and trim whitespace
            return (
              <div className={''}>
                <span className={classes.firstLine}>{parts[0]}</span>
                {parts.length > 1 && (
                  <>
                    <br />
                    <span className={classes.secondLine}>{parts[1]}</span>
                  </>
                )}
              </div>
            );
          })()
        ) : (
          <div className={`${classes.programTitle} p-3 m-0 text-center`}>{title}</div>
        )}

        {/* Uncomment if you need the star rating and other elements */}
        {/* <div className="mt-md-5 mt-0 d-flex gap-2 justify-content-center align-items-center bg-dark bg-opacity-50 mx-md-3 mx-0">
          <p className="text-warning mb-0">{rating}</p>
          <div className="d-flex align-items-top">
            <StarRating rating={rating} starColor={starColor} bsClass={''} />
          </div>
          {isMobile ? (
            <p className="text-white mb-0">
              <small>{starText}</small>
            </p>
          ) : (
            <p className="text-white mb-0">{starText}</p>
          )}
        </div> */}
      </div>
    </div>
  );
};

export default LandingPageHeroText;
