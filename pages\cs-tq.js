import Image from "next/image";
import classes from "./thankyou.module.css";
import image from "../assets/heart.svg";
import BottomFold from "../components/bottom_fold_one";


export default function thankyouPage(props){
  const tqData = props.tqData.data.attributes
    return(
        <>
            <div className={`${classes.linearGradient}`}>
              <div className={`${classes.equalPadding} mx-auto py-lg-5 py-4`}>
                <Image
                className="mx-auto d-block"
                  height={55}
                  src={image}
                  alt="thankyou_image"
                  />
                  <p className={`text-center mt-4 ${classes.whiteHeading}`}>{tqData.tq_title}</p>
                  <p className="text-center px-3 m-0" style={{color:"white"}}>{tqData.tq_description}</p>
              </div>
            </div>
           
            <div className={`${classes.fold1bg} col-12`}>
              <div className={`${classes.equalPadding} mx-auto ${classes.imagePad} px-3 pb-5`}>
                <p className={`${classes.whiteHeading} text-center`}>{tqData.bottom_fold_title}</p>
            
              </div>
            </div>
            <BottomFold data = {props.bottomFoldData}></BottomFold>
        </>
    )
}

export async function getStaticProps(context) {
    const { req, query, res, asPath, pathname, params } = context;
    const APIUrl = process.env.API_BASE_URL;
    const [tqData,bottomFoldData] = await Promise.all([
      fetch(
        `${APIUrl}/api/cs-thankyou`
      ).then((r) => r.json()),
      fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
    ]);

    return {
      props: {
        tqData: tqData,
        apiUrl: APIUrl,
        bottomFoldData:bottomFoldData
      },
    };
  }
