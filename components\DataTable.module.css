.tableContainer {
  width: 100%;
}

.tableDescription {
  margin-bottom: 1rem;
}

.tableDescription p {
  font-family: var(--isb-edge-font-family-inter-variable);
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

.tableFooter {
  margin-top: 1.5rem;
  font-family: var(--isb-edge-font-family-inter-variable);
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

.tableFooter a {
  color: #0d89de;
  text-decoration: none;
}

.tableFooter a:hover {
  text-decoration: underline;
}

.dataTable {
  font-family: var(--isb-edge-font-family-inter-variable);
  width: 100%;
  border-collapse: collapse;
}

.dataTable th {
  background-color: #f8f9fa;
  font-weight: 600;
  text-align: left;
  padding: 12px;
  border: 1px solid #dee2e6;
}

.dataTable td {
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  vertical-align: middle;
}

.dataTable tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.emailLink {
  color: #0d89de;
  text-decoration: none;
}

.emailLink:hover {
  text-decoration: underline;
}

.loadingContainer {
  text-align: center;
  padding: 2rem 0;
}

.errorMessage {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.emptyMessage {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .dataTable {
    font-size: 0.9rem;
  }
  
  .dataTable th,
  .dataTable td {
    padding: 8px;
  }
}
