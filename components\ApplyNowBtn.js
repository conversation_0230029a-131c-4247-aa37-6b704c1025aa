import React, { useEffect, useState } from 'react'

export const ApplyNowBtn = ({fullUrl, learnTrackData,classes,bannerFullData,qpms,andQpms,baseURL}) => {
  const [queryParams, setQueryParams] = useState('');
   
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const qParams = window.location.search.slice(1); 
      setQueryParams(`?${qParams}`);
    }
  }, []);

  return (
    <div className="col-md-4 col-sm-3 col-2  ps-0 pb-0 align-items-center d-flex justify-content-end">
                  {process.env.NEXT_PUBLIC_BASE_URL === `https://online.isb.edu` ? (learnTrackData.learning_track_url_prod != null ? <a className=" pb-0 text-decoration-none"
                    href={`${learnTrackData.learning_track_url_prod}?url=${baseURL
                      }@!${learnTrackData.learning_track_id}${qpms != `` ? queryParams  : ``
                      }${fullUrl}`}
                  >
                    <span className={`${''} ${classes.StickyapllyNowBtn}`}>
                      {bannerFullData.apply_now_title}
                    </span>
                  </a> : <div></div>) : (learnTrackData.learning_track_url_staging != null ? <a className=" pb-0 text-decoration-none"
                    href={`${learnTrackData.learning_track_url_staging}?url=${baseURL
                      }@!${learnTrackData.learning_track_id}${qpms != `` ? queryParams  : ``
                      }${fullUrl}`}
                  >
                    <button className={`${''} ${classes.StickyapllyNowBtn}`}>
                      {bannerFullData.apply_now_title}
                    </button>
                  </a> : <div></div>)}
                </div>
  )
}
