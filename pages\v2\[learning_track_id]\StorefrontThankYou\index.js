import { useRouter } from "next/router";
import classes from "../index.module.css";
import Moment from "moment";
import thankyouclass from '../../../../components/ThankYouSection.module.css'
import download from "../../../../assets/Download.svg";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import { useEffect, useState } from "react";
import CaseLetComponent from "../../../../components/CaseLetComponent";
import ProgramPageHeroSection from "../../../../components/ProgramPageHeroSection";
import { ProgramDetailsCard } from "../../../../components/ProgramDetailsCard";
import { StartYourApplicationBtn } from "../../../../components/StartYourApplicationBtn";
import CryptoJS from 'crypto-js';
import Image from 'next/image';
import { BrochureFileNameFormatter } from "../../../../utils";

function LearningTracks(props) {
  Moment.locale("en");
  
  const router = useRouter();
  const { query } = useRouter();
  const learnTrackData = props.learningTrackData.data[0].attributes;
  const thankyouData = props.thankyouData.data.attributes;
  const caselets = learnTrackData.learning_track_caselets.data;
  const trackFoldData = props.trackFold.data.attributes;
  const landingPageResponse = props.tracklandingpage.data[0].attributes

  const qpms = query.utm_source != undefined
      ? `utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`
      : ``;
  const queQpms = `?${qpms}`;
  const andQpms = `&${qpms}`;
  const [loading, setLoading] = useState(false);
  const [fullUrl, setFullUrl] = useState('');


const { showModal } = router.query;

useEffect(() => {

  if (typeof window !== 'undefined') {
    const cookies = document.cookie
      .split(';')
      .map((cookie) => cookie.trim())
      .reduce((acc, cookie) => {
        const [key, value] = cookie.split('=');
        acc[key] = decodeURIComponent(value);
        return acc;
      }, {});

    const {
      leadform_name,
      leadform_email,
      leadform_mobile,
      leadform_ProgramId,
      leadform_role,
      leadform_country_code,
      leadform_id,
      leadform_location,
      leadform_years_of_experience
    } = cookies;

    const countryCode = localStorage.getItem('countryCode');

    const formData = {
      name: leadform_name,
      email: leadform_email,
      role: leadform_role,
      mobile: leadform_mobile,
      checkBox: true,
      countryCode: leadform_country_code || countryCode,
      programId: leadform_ProgramId,
      location : leadform_location,
      leadform_id:leadform_id,
      years_of_experience : leadform_years_of_experience

    };

    const secretKey = process.env.NEXT_PUBLIC_CRYPTO_KEY; // Use a secure secret key
    const encryptedParams = CryptoJS.AES.encrypt(
      JSON.stringify(formData),
      secretKey
    ).toString();

    const allValuesValid = Object.values(formData).every(
      (value) => value !== undefined && value !== 'undefined' && value !== null && value !== ''
    );

    if (allValuesValid) {
      // const params = new URLSearchParams(formData); 
     
      const fullUrlWithParams = `?params=${encodeURIComponent(encryptedParams)}`;
      setFullUrl(fullUrlWithParams);  
    } else {
      setFullUrl('');  
    }

  const decryptParams = (encryptedString) => {
    const bytes = CryptoJS.AES.decrypt(encryptedString, secretKey);
    const decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    return decryptedData;
  };
  const decryptedData = decryptParams(encryptedParams);

}


  if (showModal !== 'true') {
    router.replace(`/v2/${props.meetupId}`); 
  }

}, [showModal, props.meetupId]);

if (showModal !== 'true') {
  return null;
}
const handleClosePaymentModal=()=>{
  router.push({pathname: `/v2/${props.meetupId}`})
}

const handleDownload = async () => {
  try {
    const response = await fetch(
      props.apiUrl + landingPageResponse.brochure.data.attributes.url
    );
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    const formattedName = BrochureFileNameFormatter(props.meetupId);
    link.download = `${formattedName +".pdf"}`;
    link.click();
  } catch (error) {
    console.error("Error downloading file:", error);
  }
};
const handleDownloadWithLoader = async () => {
  try {
      setLoading(true);  
      await handleDownload();  
  } finally {
      setLoading(false);  
  }
};


  return (
    <>
      <div>
      <ProgramPageHeroSection
      landingPageResponse={landingPageResponse}
      classes={classes}
      apiUrl={props.apiUrl}
      learnTrackData={learnTrackData}
      baseURL={props.baseURL}
      baseurl={props.baseurl}
      qpms ={qpms }
      meetupId={props.meetupId}
      utm_source={query.utm_source}
      utm_medium={query.utm_medium}
      utm_campaign={query.utm_campaign}
      utm_term={query.utm_term}
      utm_content={query.utm_content}
      andQpms ={andQpms }
      trackFoldData={trackFoldData}
      />    

        <div className={`row mt-lg-5 mt-3 justify-content-between col-lg-12 mx-auto px-lg-0 px-sm-3 px-md-3 px-3   ${classes.equalPadding}  ${classes.caseletStyle}`}>
        <div className={`col-lg-8 p-0 col-md-12`}>

        <CaseLetComponent
        qpms={qpms}
        andQpms={andQpms}
        trackFoldData={trackFoldData}
         baseURL={props.baseURL} 
         lxpStagingUrl={props.lxpStagingUrl}
         lxpProdUrl={props.lxpProdUrl}
          apiUrl={props.apiUrl}
          learnTrackData={learnTrackData}
          classes={classes} 
          caselets={caselets}/>         

        </div>

          <ProgramDetailsCard
            apiUrl={props.apiUrl}
          landingPageResponse={landingPageResponse}
            trackFoldData={trackFoldData}
            classes={classes}
            learnTrackData={learnTrackData}
            baseURL={props.baseURL}
            qpms={qpms}
            andQpms={andQpms}
                        
          />        
        </div>

   <div id="myModal" className={`${classes.fin_modal} d-${"flex"}`} >
      <div className={classes.modal_content}>
          <div className='bg-white'>
            <span className={classes.closebtn} 
            onClick={handleClosePaymentModal}
            >&times;</span>

            <div className={`${thankyouclass.thankyoumain} col-12`} >
            <div className={thankyouclass.check_mark} />
            <div className={thankyouclass.thankyoutitle}>
                <h1>{thankyouData.title}</h1>
                <h2>{thankyouData.sub_heading}</h2>
            </div>

            {loading &&  <div className={`${thankyouclass.equalPadding} position-relative`}>
        <div className={`spinner-border  isb-text-color position-absolute`} style={{width:"20px", height:'20px'}} role="status"/>
      </div>} 
      
            {thankyouData.options_section.map((i)=>(
                <div key={i.id} className={thankyouclass.text_icon_section}>
                <div className={thankyouclass.icon}>
                    <Image  src={download} alt='download_icon'></Image>
                </div>
                
                <div className={thankyouclass.textSection}>

                  <h6
                    className={`${thankyouclass.heading} ${loading ? thankyouclass.disabled_title : ''}`}
                    onClick={!loading ? handleDownloadWithLoader : null} // Disable click while loading
                  >
                    {i.title}
                  </h6>

                    <span className={thankyouclass.description}>
                        {i.description}
                    </span>
                </div>
            </div>))}
             
            <StartYourApplicationBtn
            fullUrl={fullUrl}
                learnTrackData={learnTrackData}
                landingPageResponse={landingPageResponse}
                qpms={qpms}
                andQpms={andQpms}
                baseURL={props.baseURL}
                trackFoldData={trackFoldData}
                classes={thankyouclass}
            /> 
        </div>
          </div>  
      </div>
    </div>
      </div>
    </>
  );
}
export default LearningTracks;

export const getStaticPaths = async (context) => {
  const APIUrl = process.env.API_BASE_URL;
  const res = await fetch(`${APIUrl}/api/new-track-pages`);
  const response = await res.json();
  const paths = response.data.map((learningTrack) => {
    return {
      params: {
        learning_track_id:
          learningTrack.attributes.learning_track_id.toString(),
      },
    };
  });
  return {
    paths,
    fallback: false,
  };
};


export async function getStaticProps(context) {
  const meetupId = context.params.learning_track_id;
  const APIUrl = process.env.API_BASE_URL;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
  const lxpStagingUrl = process.env.ISBO_STAGING_URL;
  const lxpProdUrl = process.env.ISBO_PROD_URL;
  
  const [
    tracklandingpage,
    learningTrack,
    trackFold,
    thankyouData,
  ] = await Promise.all([
    fetch(`${APIUrl}/api/landing-pagev2s?filters[learning_track_id][$eq]=${meetupId}&populate=deep,3`).then((r) => r.json()),
    fetch(`${APIUrl}/api/new-track-pages?filters[learning_track_id][$eq]=${meetupId}&populate=deep,4`).then((r) => r.json()),
    fetch(`${APIUrl}/api/new-track-page-fold?populate=*`).then((r) => r.json()),
    fetch(`${APIUrl}/api/thankyousection?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      learningTrackData: learningTrack,
      trackFold: trackFold,
      tracklandingpage:tracklandingpage,
      thankyouData:thankyouData,

      meetupId:meetupId,  
      apiUrl: APIUrl,
      baseURL: baseUrl,
      lxpStagingUrl: lxpStagingUrl,
      lxpProdUrl: lxpProdUrl,
    },
    revalidate: 240,
  };
}


 






 

