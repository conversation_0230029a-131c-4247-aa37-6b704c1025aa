import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useEffect, useRef, useState } from 'react'
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Image from 'next/image';
import { faRotateRight } from '@fortawesome/free-solid-svg-icons';

const CaseLetComponent = ({fullUrl,qpms,trackFoldData, andQpms, baseURL, lxpStagingUrl, lxpProdUrl, apiUrl, learnTrackData, classes, caselets}) => {
    const [currentSlide, setCurrentSlide] = useState(0);
    const sliderRef = useRef(null);

    const settings = {
        dots: true,
        infinite: true,
        speed: 400,
        autoplay: true,
        autoplaySpeed: 8000,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        beforeChange: (oldIndex, newIndex) => setCurrentSlide(newIndex),
      };
      const [queryParams, setQueryParams] = useState('');
   
      useEffect(() => {
        if (typeof window !== 'undefined') {
          const qParams = window.location.search.slice(1); 
          setQueryParams(`?${qParams}`);
        }
      }, []);


  return (
  <>
    {caselets.length != 0 && 
        <div
          className={`me-lg-5 mb-0 me-sm-0 p-0 p-md-0 p-lg-4 ${classes.sliderContainer} purpleslidercontainer `}
        >
          <Slider {...settings} ref={sliderRef} className={` p-0 ${''}`}>
            {caselets.map((caselet) => {
              return (
                <div key={caselet.id} className={classes.container}>
                  <Image
                    priority={true}
                    width="0"
                    height="0"
                    sizes="50vw"
                    src={`${apiUrl}${caselet.attributes.caselet_image.data.attributes.url}`}
                    className="img-fluid w-100 h-auto"
                    alt={
                      caselet.attributes.caselet_image.data.attributes
                        .alternativeText
                    }
                  />
                  <div className={classes.centered}>
                    {caselet.attributes.caselet_description}
                  </div>
                </div>
              );
            })}
          </Slider>


          <div
            className={`d-flex justify-content-between`}
            style={{ paddingTop: "36px" }}
          >
            {process.env.NEXT_PUBLIC_BASE_URL ===
              `https://online.isb.edu` ? (
              <>
                {learnTrackData.enable_free_exp_prod ? (
                  <div className="text-center">
                    <a
                      href={`${lxpProdUrl}${learnTrackData.free_exp_uri_prod
                        }/${learnTrackData.learning_track_id}?url=${baseURL
                        }@!${learnTrackData.learning_track_id}${qpms != `` ? queryParams  : ``
                        }_${fullUrl}`}
                        style={{ textDecoration: "none", color:"white" }}
                    >
                      <button
                        className={classes.exp_free_lesson_cta}
                      >
                        {trackFoldData.caselet_btn_title}
                        <svg
                          width="30"
                          height="12"
                          viewBox="0 0 15 13"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.6225 12.8462L7.54201 11.7797L11.9903 7.33147H0.469696V5.78791H11.9903L7.54201 1.35369L8.6225 0.273194L14.909 6.55969L8.6225 12.8462Z"
                            fill="white"
                          />
                        </svg>
                      </button>
                    </a>
                  </div>
                ) : (
                  ""
                )}
              </>
            ) : (
              <>
                {learnTrackData.enable_free_exp_staging ? (
                  <div className="text-center">
                      <button
                        className={classes.exp_free_lesson_cta}

                      >
                    <a
                      href={`${lxpStagingUrl}${learnTrackData.free_exp_uri_staging
                        }/${learnTrackData.learning_track_id}?url=${baseURL
                        }@!${learnTrackData.learning_track_id}${qpms != `` ? queryParams  : ``
                        }_${fullUrl}`}
                        style={{ textDecoration: "none", color:"white" }}
                    >
                        {trackFoldData.caselet_btn_title}
                        <svg
                          width="30"
                          height="12"
                          viewBox="0 0 15 13"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.6225 12.8462L7.54201 11.7797L11.9903 7.33147H0.469696V5.78791H11.9903L7.54201 1.35369L8.6225 0.273194L14.909 6.55969L8.6225 12.8462Z"
                            fill="white"
                          />
                        </svg>
                    </a>
                      </button>
                  </div>
                ) : (
                  ""
                )}
              </>
            )}
            <div 
            className={`${classes.resp_btn_caursl}`}
            // className={`${''}`}
            >
              <button
                className={`${classes.viewbtn} ${currentSlide === 0? "d-none":""} btn`}
                onClick={() => sliderRef.current.slickPrev()}
                style={{
                  backgroundColor: "var(--isb-blue-color) !important",
                  color: "white",
                  border: "none",
                  padding: "10px 25px",
                  marginRight: "10px",
                }}
              >
                <svg
                  width="15"
                  height="14"
                  viewBox="0 0 15 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.3775 0.846191L7.45799 1.91265L3.00973 6.36091H14.5303V7.90447L3.00973 7.90447L7.45799 12.3387L6.3775 13.4192L0.0910013 7.13269L6.3775 0.846191Z"
                    fill="white"
                  />
                </svg>
              </button>
              <button
                className={`${classes.viewbtn} ${currentSlide === caselets.length - 1 ? 'd-none' : ''} btn`}
                onClick={() => sliderRef.current.slickNext()}
                style={{
                  backgroundColor: "var(--isb-blue-color) !important",
                  color: "white",
                  border: "none",
                  padding: "10px 25px",
                }}
              >
                <svg
                  width="15"
                  height="13"
                  viewBox="0 0 15 13"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.6225 12.8462L7.54201 11.7797L11.9903 7.33147H0.469696V5.78791H11.9903L7.54201 1.35369L8.6225 0.273194L14.909 6.55969L8.6225 12.8462Z"
                    fill="white"
                  />
                </svg>
              </button>
              <button
                className={`${classes.viewbtn} ${currentSlide === caselets.length - 1 ? "":"d-none"} btn`}
                onClick={() => sliderRef.current.slickNext()}
                style={{
                  backgroundColor: "var(--isb-blue-color) !important",
                  color: "white",
                  border: "none",
                  padding: "10px 25px",
                }}
              >
                <FontAwesomeIcon icon={faRotateRight} />
              </button>
            </div>
          </div>
        </div>
       }
       </>
  )
}

export default CaseLetComponent

