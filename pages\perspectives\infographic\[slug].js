import React, { useEffect, useState, useMemo } from 'react';
import Image from 'next/image';
import Head from 'next/head';
import styles from './infographic.module.css';
import { useRouter } from 'next/router';
import Breadcrumb from '../../../components/Breadcrumb/Breadcrumb';
import Link from 'next/link';
import TrackPageBottomFold from '../../../components/TrackPageBottomFold';
import FacultySection from '../../../components/FacultySection';
import { RecommendedForYou, ProgeammesForYou, ShareArticle, TableOfContents, TopicsComponentForSlug } from '../../../components/PerspectiveComponents';
import playicon from '../../../assets/playicon.svg';
import dynamic from 'next/dynamic';
import LeadForm from '../../../components/EdgeLeadForm/index';
import TopicsDropdown from '../../../components/TopicsDropdown';

// const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });

export default function InfographicPage({ articlesData, apiUrl, baseurl, leadFormData }) {



  const router = useRouter();
  const { query } = router;
  const { slug } = router.query;
  const [activeSection, setActiveSection] = useState('');
  const [scrollProgress, setScrollProgress] = useState(0);
  const apiPosts = articlesData.data[0].attributes?.Recommended_post.data || [];

  const Headline_text = articlesData.data[0].attributes.Headline_text;

  const post_first_section = articlesData.data[0].attributes.info_first_section;
  const Topics = articlesData.data[0].attributes.topics.data;
  const Post_type = articlesData.data[0].attributes.info_first_section.post_type;

  const infographic_body = articlesData.data[0].attributes.info_body;

  const professor_section = articlesData.data[0].attributes.professor?.data?.attributes;

  const track_details = articlesData.data[0]?.attributes?.new_track_pages?.data;

  const shareArticle = articlesData.data[0].attributes.Share_section;

  const [showFullSummary, setShowFullSummary] = useState(false);
  const [navData, setProgrammes] = useState(null);

  // Calculate reading time
  const calculateReadingTime = (content) => {
    if (!content) return 0;
    // Remove HTML tags and trim whitespace
    const plainText = content.replace(/<[^>]*>/g, '');
    // Count words (split by whitespace)
    const wordCount = plainText.trim().split(/\s+/).length;
    // Average reading speed (words per minute)

    const wordsPerMinute = 200;


    // Calculate reading time in minutes
    return wordCount / wordsPerMinute;
  };

  // Calculate total reading time from all sections
  const totalReadingTime = useMemo(() => {
    let totalTime = 0;



    if (infographic_body && infographic_body.length > 0) {
      infographic_body.forEach(section => {
        if (section.description) {
          totalTime += calculateReadingTime(section.description);
        }
      });
    }
    const sectionTime ={
       totalTime: Math.ceil(totalTime),
       title: articlesData.data[0].attributes.post_id
    }

    return totalTime;
  }, [infographic_body]);



  const readTimePermin = Math.ceil(totalReadingTime);


  const currentQuery = router.asPath.includes("?") ? router.asPath.split("?")[1] : "";


  const qpms =
    query.utm_source != undefined
      ? `?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}`
      : ``;


  const transformHTML = (htmlString) => {
    if (!htmlString) return "";
    return htmlString.replace(/<img([^>]+)src="([^"]+)"([^>]*)>/g, (match, beforeSrc, src, afterSrc) => {
      const fullSrc = `${apiUrl}${src}`;
      return `<Image src="${fullSrc}" alt="Image" width=100% height=auto layout="responsive" sizes=${'100vw'}/>`;

    });
  };

  const combinedArray = [
    ...apiPosts.map(post => ({
      id: post.id,
      attributes: {
        ...post.attributes.info_first_section,
        post_id: post.attributes.post_id
      }
    })),
  ];


  useEffect(() => {
    const updateProgress = () => {
      const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
      const windowHeight = scrollHeight - clientHeight;
      const progress = (scrollTop / windowHeight) * 100;
      setScrollProgress(progress);
    };

    const observerCallback = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setActiveSection(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, {
      rootMargin: '-20% 0px -35% 0px'
    });

    document.querySelectorAll('section[id]').forEach((section) => {
      observer.observe(section);
    });

    window.addEventListener('scroll', updateProgress);
    updateProgress();

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', updateProgress);
    };
  }, []);
  useEffect(() => {
    fetchData()

  }, [])





  const truncateText = (text, limit) => {
    if (!text) return '';
    if (text.length <= limit) return text;
    return text.slice(0, limit - 3) + '...';
  };
  const breadcrumbItems = [
    { label: 'Perspectives', href: '/perspectives' },
    { label: 'Infographic', href: '/perspectives/alltopics?type=Infographic' },
    { label: truncateText(articlesData.data[0].attributes?.info_first_section?.post_name, 68), href: '#'}

    // {
    //   label: truncateText(articlesData.data[0].attributes.post_first_section.post_name, 68),
    //   href: '#'
    // }
  ];


  if (router.isFallback) {
    return <div>Loading...</div>;
  }


  const toggleSummary = () => {
    setShowFullSummary(!showFullSummary);
  };

  const truncatedSummary = post_first_section.summary?.length > 150
    ? `${post_first_section.summary.slice(0, 150)}...`
    : post_first_section.summary;

  const fetchData = async () => {
    const url = process.env.NEXT_PUBLIC_API_BASE_URL;
    const res = await fetch(`${url}/api/navbars?populate=deep,4`);
    const data = await res.json();
    setProgrammes(data?.data[0].attributes);

  };
  const navbarData = navData && navData;

  if (!articlesData) {
    return null;
  }


  return (
    <>
      {/* <Head>
        <title>{`${articlesData.data[0].attributes.post_first_section.post_name} | ISB Executive Education`}</title>
      </Head> */}


      <div style={{ position: 'relative', zIndex: 1000, width: '100%' }}>
        <TopicsDropdown />
      </div>

      <div style={{ backgroundColor: "#F4F8FA" }}>
      <div className='container'>
        <Breadcrumb items={breadcrumbItems} headlinetext={Headline_text} />
      </div>
        <article className={styles.article}>
          <p className={styles.posteddate}>{post_first_section.posted_date}</p>
          <div className={styles.container}>
            <div className={styles.mainContent}>
              <header className={styles.header}>
              {post_first_section.slug_image_required && <div className={styles.headerImage} >

                  <Image
                    src={`${apiUrl}${post_first_section.slug_image.data.attributes.url}`}
                    alt={post_first_section.slug_image.data.attributes.alternativeText}
                    width={0}
                    height={0}
                    sizes='100vw'
                    priority
                    className={styles.image}
                  />
                </div> }


                <div className={styles.headerContent}>

                  <div className={`${styles.summarymain} d-flex justify-content-between align-items-flex-end mt-3`}>
                    {/* <p className={styles.subtext}>{post_first_section.subtext}</p> */} 
                    <TopicsComponentForSlug Topics={Topics} Post_type={Post_type}/>
                    <div className="d-flex justify-content-end">
                      <div className={`${styles.categoryIcon} `}>
                        <Image width={20} height={20} src={`${apiUrl}${post_first_section.category_icon.data.attributes.url}`} alt="Category Icon" />
                      </div>
                    </div>
                  </div>



                  <h1>{post_first_section.post_name}</h1>
                  {post_first_section.summary && <p className={styles.description}>
                    <b>Summary.</b>{' '}
                    {post_first_section.summary?.length > 150 ? (
                      <>
                        {showFullSummary ? post_first_section.summary : truncatedSummary}{' '}

                        <span
                          onClick={toggleSummary}
                          className={`${styles.readMoreBtn} pointer text-decoration-underline`}
                        >
                          {showFullSummary ? 'Read Less' : 'Read More'}
                        </span>
                      </>
                    ) : (
                      post_first_section.summary
                    )}
                  </p>}
                  <div className={styles.articleMeta}>

                    {/* <span>{readTimePermin}</span> */}
                  </div>
                </div>
              </header>
              <div className={styles.content}>
                {infographic_body.map((section, index) => {

                  return (
                    <section
                      key={index}
                      id={`section-${index}`}
                      className={styles.section}
                    >
                      <h2>{section.title}</h2>

                      <div className={styles.descriptiondiv} dangerouslySetInnerHTML={{
                        __html: transformHTML(section.description)
                      }} />
                    {section.pdf_section && 
                      <div className={`${styles.pdf_section}  p-4  row mx-0`}>

                      {section.pdf_section.image && section.pdf_section.image.data && <div className='col-4'>
                          <Image
                            src={`${apiUrl}${section.pdf_section.image.data.attributes.url}`}
                            alt={section.title}
                            width={164}
                            height={166}
                            priority
                            className={`${styles.infographic_image} w-100 h-auto`}
                          />
                        </div>}

                        <div className="col d-flex flex-column" style={{ minHeight: '200px' }}>
                          <div className="flex-grow-1">
                            <h2 className="fw-600 text-gray-900 text-black mb-2" style={{ fontSize: '1.2rem' }}>
                              {section.pdf_section.title}
                            </h2>
                            {section.pdf_section.description && (
                              <p className="text-black mb-0" style={{ fontSize: '1rem' }}>
                                {section.pdf_section.description.slice(0, 135)}
                                {section.pdf_section.description.length > 135 ? '...' : ''}
                              </p>
                            )}
                          </div>
                          {section.pdf_section.pdf_btn_title && (
                            <div className="d-flex mt-0">
                              <button
                                className="hover:underline bg-white border-0 rounded px-4 py-2 mb-1"
                                onClick={() => window.open(`${apiUrl}${section.pdf_section.pdf_file.data.attributes.url}`, "_blank")}
                              >
                                <span className={styles.download_text}>{section.pdf_section.pdf_btn_title}</span>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      }


                      {section.image?.data && (
                        <div className={styles.sectionImage}>
                          <Image
                            src={`${apiUrl}${section.image.data.attributes.url}`}
                            alt={section.image.data.attributes.alternativeText || section.title}
                            width={670}
                            height={403}
                            className={styles.image}
                          />
                        </div>
                      )}

                    </section>
                  )
                })}
              </div>


             



            </div>
            <aside>
              <div className={styles.asidemain} style={{ top: professor_section && shareArticle.length > 0 ? "0.5rem" : professor_section && shareArticle.length == 0 ? "6.6rem" : "8rem" }}>
                {shareArticle.length > 0 && <ShareArticle styles={styles} shareArticle={shareArticle} apiUrl={apiUrl} title={articlesData.data[0].attributes.Share_article_title} />}
                {professor_section && <FacultySection shareArticle={shareArticle} professor_section={professor_section} apiUrl={apiUrl} />}
                <div >
                  {track_details.length > 0 && <h4 className={styles.programmesforyou}>
                    {articlesData.data[0].attributes.programme_for_you_title}
                  </h4>}

                  <ProgeammesForYou track_details={track_details} apiUrl={apiUrl} currentQuery={currentQuery} />

                </div>
              </div>
            </aside>
          </div>
        </article>

        {/* <div className={styles.leadFormWrapper}>
          <LeadForm leadFormData={leadFormData} apiUrl={apiUrl} />
        </div> */}

        <RecommendedForYou date={post_first_section.date} readTimePermin={readTimePermin} combinedArray={combinedArray} apiUrl={apiUrl} currentQuery={currentQuery} router={router} title={articlesData.data[0].attributes.Recommended_title} />
      </div>
      <TrackPageBottomFold navData={navbarData} apiUrl={apiUrl} />
    </>
  );
}


export const getStaticPaths = async () => {
  const APIUrl = process.env.API_BASE_URL;
  const res = await fetch(`${APIUrl}/api/infographics?populate=deep,10`);
  const response = await res.json();
  const paths = response.data.map((article) => {
    const slug = String(article.attributes.post_id);
    return {
      params: {
        slug: slug
      }
    };
  });

  return {
    paths,
    fallback: 'blocking'
  };
};

export async function getStaticProps({ params }) {
  const { slug } = params;
  const APIUrl = process.env.API_BASE_URL;
  const Baseurl = process.env.NEXT_PUBLIC_BASE_URL;

  const [articlesRes, leadFormRes, metaRes] = await Promise.all([
    fetch(`${APIUrl}/api/infographics?filters[post_id][$eq]=${slug}&populate=deep,4`),
    fetch(`${APIUrl}/api/edge-leadform?populate=deep,3`),
    fetch(`${APIUrl}/api/edge-main-page?populate=meta_tags.image`)
  ]);

  const [articlesData, leadFormData, metaTagsData] = await Promise.all([
    articlesRes.json(),
    leadFormRes.json(),
    metaRes.json()
  ]);

  return {
    props: {
      articlesData: articlesData,
      leadFormData: leadFormData,
      perspectivesMetaData: metaTagsData,
      apiUrl: APIUrl,
      baseurl: Baseurl,
    },
    revalidate: 120,
  };
}