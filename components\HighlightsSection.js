import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Slider from 'react-slick';

const HighlightsSection = ({
  classes,
  title,
  data,
  apiUrl,
  flag,
}) => {

  const[highlightData, setHighlightData] = useState()
  
  const[isClient, setIsClient] = useState()

  useEffect(() => {
      setIsClient(true)
      const fetchData = async () => {
        try {
          const response = await fetch(`${apiUrl}/api/lpv2highlightsection?populate=deep,3`);
          if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}`);
          }
          const data = await response.json();
          setHighlightData(data.data.attributes);
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      };
  
      fetchData();
    }, [apiUrl]);

if(!isClient){
  return null;
}
 
  return (
   <div>
     {highlightData && <div className={`${classes.highlightbackground} pt-4 pb-5 px-lg-0 px-4 m-0 `}>
        <div className={`col-12 mx-auto  ${!flag ? classes.highlight_section_main :""}`}>
          
        <HighlightsSectionBody
          title={title? title : data.why_isb_online_title}
          data={data ? data : data}
          apiUrl={apiUrl}
          classes={classes}
          flag={flag}

        />
        </div>
      </div>}
   </div>
  );
};

export default HighlightsSection;

 

export const HighlightsSectionBody = ({ title, data, apiUrl, classes, flag }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 991);
    };

    handleResize(); // Check the initial window size
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const sliderSettings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
  };
const CustGap = 'gap-4'

function verticalLine(index) {
  return data.length - 1 === index || index % 3 === 2 || flag;
}

  return (
    <div>
      <h2 className={`text-center ${classes.main_blue_head} pb-5 pt-3 m-0`}>
        {title}
      </h2>
      <div className={`${flag ? "mx-2" : `${CustGap} mx-2`} row justify-content-center d-flex px-lg-0`}>
        {isMobile ? (
          <Slider {...sliderSettings}>
            {data.map((card, index) => (
              <div key={index} className="col-12">
                <div className={`d-flex flex-column justify-content-center align-items-center`}>
                  {/* <Image
                    src={apiUrl + card.image.data?.attributes.url}
                    alt="Card"
                    width="42"
                    height="41"
                    sizes="100vw"
                  /> */}
                  <div className={`${data.length - 1 === index || index === 2 || flag ? "" : `${classes.borderedcls}`} `}>
                    <h2 className={`card-title text-black ${classes.prgrmhighlightcards_title} pt-2`}>
                      {card.title}
                    </h2>
                    <div
                      className={`card-title text-black d-flex text-center ${classes.advnew_highlightcards_description}`}
                      dangerouslySetInnerHTML={{ __html: card.description }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </Slider>
        ) : (
          data.map((card, index) => (
            <div key={index} className={`col-lg-4 ${!flag? classes.prgm_highlights: classes.lp_whyisbonline} d-flex justify-content-between px-0 ${CustGap}`}>
              <div >
                <div className={`d-flex flex-column justify-content-center align-items-center`}>
                  {card.image.data?.attributes.url && <Image
                    src={apiUrl + card.image.data?.attributes.url}
                    alt="Card"
                    width="42"
                    height="41"
                    sizes="100vw"
                  /> }
                  <div className={`${verticalLine(index) ? "" : `${classes.borderedcls}`} `}>
                    <h2 className={`card-title text-black ${classes.prgrmhighlightcards_title} pt-2`}>
                      {card.title}
                    </h2>
                    <div
                      className={`card-title text-black d-flex text-center ${classes.advnew_highlightcards_description}`}
                      dangerouslySetInnerHTML={{ __html: card.description }}
                    />
                  </div>
                </div>
              
              </div>
             {/* <div className='d-flex flex-wrap align-content-end'>{ data.length - 1 === index || index === 2 || flag ?"": <div style={{height:"70%", borderRight:"1px solid lightblue", margin:"auto"}}></div>}</div> */}
             { verticalLine(index) ?"": <div style={{height:"70%", borderRight:"1px solid lightblue", margin:"auto"}}></div>}
            </div>

          ))
        )}
      </div>
    </div>
  );
}