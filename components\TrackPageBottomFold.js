import React, { useEffect, useState } from 'react'
import BottomFold from './bottom_fold_one';

const TrackPageBottomFold = ({apiUrl, getIsbMail, navData, faqsection}) => {
 
    const[bottomFoldData, setBottomFoldData] = useState()
    const[isClient, setIsClient] = useState()

    useEffect(() => {
        setIsClient(true)
        const fetchData = async () => {
          try {
            const response = await fetch(`${apiUrl}/api/bottom-fold?populate=deep,5`);
            if (!response.ok) {
              throw new Error(`API request failed with status ${response.status}`);
            }
            const data = await response.json();
            setBottomFoldData(data);
            getIsbMail(data.data.attributes.contact_us_email)
          } catch (error) {
            console.error('Error fetching data:', error);
          }
        };
    
        fetchData();
      }, [apiUrl, getIsbMail]);

if(!isClient){
    return null;
}
  return (
    bottomFoldData &&  <BottomFold navData={navData} faqsection={faqsection} trackpage={true} data={bottomFoldData}></BottomFold>
  )
}

export default TrackPageBottomFold