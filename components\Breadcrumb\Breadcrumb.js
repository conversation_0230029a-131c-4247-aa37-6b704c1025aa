import Link from 'next/link';
import styles from './Breadcrumb.module.css';
import { useRouter } from 'next/router';

export default function Breadcrumb({ items, headlinetext, containermaxwidth, }) {
  const router = useRouter();
  
  const getUrlWithParams = (href) => {
     // Check if href is already a URL with query parameters
     let pathname = href;
     let existingParams = {};
     
     // Parse existing query params if present
     if (typeof href === 'string' && href.includes('?')) {
       const [path, queryString] = href.split('?');
       pathname = path;
       
       // Parse existing query params
       const searchParams = new URLSearchParams(queryString);
       searchParams.forEach((value, key) => {
         existingParams[key] = value;
       });
     }
     
     // Add UTM parameters 
    const utmParams = {};
    Object.entries(router.query).forEach(([key, value]) => {
      if (key.startsWith('utm_') && value && value !== 'undefined') {
        utmParams[key] = value;
      }
    });
    
    // Combine existing params with UTM params
    return {
      pathname,
      query: { ...existingParams, ...utmParams }
    };
  };

  return (
    <div className={`${styles.breadcrumbContainer} d-none d-md-block ${containermaxwidth ? "" :"px-0"} `} style={{backgroundColor: "var(--isb-edge-body-background-color)"}}>
    <div className={`${styles.breadcrumbsection} d-flex justify-content-center row px-0`} style={{ maxWidth: containermaxwidth}} >
      <div className='col-md col-12 align-content-center px-md-0 ps-3 '>
      <nav className={styles.breadcrumb} aria-label="breadcrumb">
      <ol>
        <li>
          <Link href={getUrlWithParams("/")}>ISB Online</Link>
        </li>
        {items.map((item, index) => (
          <li key={index}>
            {index === items.length - 1 ? (
              <span>{item.label}</span>
            ) : (
              <Link href={getUrlWithParams(item.href=="/perspectives/article" ?"/perspectives":item.href)}>{item.label}</Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
      </div>
      {/* <div className={`${styles.topRightText} col-md-5 col-12 px-0 align-content-center`} >
        <p>{headlinetext}</p>
      </div> */}
    </div>
  </div>
   
  );
}
