import React, { useEffect, useRef, useState } from 'react'
import CardComponent from './CardComponent'
import OnlineAdvantage from './OnlineAdvantage'
import { FacultySlider } from './FacultySlider'
import { StoriesSlider } from './StoriesSlider'
import { WhatYouGain } from './WhatYouGain'
import { Whowillbenefit } from './Whowillbenefit'
import { KeyConcepts } from './KeyConcepts'
import { ApplyNowBtn } from './ApplyNowBtn'
import Image from 'next/image'
import TrackPageBottomFold from './TrackPageBottomFold'
import banner from "../assets/isb_mage.png";
import { IsbAdvantage } from './IsbAdvantage'
import HighlightsSection from './HighlightsSection'
import { InterestedSection } from './InterestedSection'
import { AiComponent } from './AiComponent'

export const StickyBanner = ({
  isv2,
  getModalStat,
  interestedSection,
  landingPageResponse,
    classes,
    modalStatus,
    learnTrackData,
    andQpms,
    qpms,
    baseURL,
    apiUrl,
    trackFoldData,
    courseData,
    queQpms,
    programDetails,
    courseProf,
    getIsbMail,
    fullUrl,
    meetupId,
    utm_source,
    utm_medium,
    utm_campaign,
    utm_term,
    utm_content,
    query

}) => {
  const [bannerFullData, setBannerFullData] = useState()
  const [bannerData, setBannerData] = useState()
  const [pdfdata, setPdfdata] = useState()
  const [elementToShow, setElementToShow] = useState(true);  
  const [activeElement, setActiveElement] = useState() 
  const [isScrolled, setIsScrolled] = useState(false);
  const isScrollingRef = useRef(false);
  const targetElementRef = useRef(null);
const handleListItemClick = (id) => {
  setIsScrolled(true);
    isScrollingRef.current = true;
    const element = document.getElementById(id);
    const offset = 135;
    if (element) {
      if (id) {
        const elementPosition = element.getBoundingClientRect().top + window.scrollY;
        const offsetPosition = elementPosition - offset;
  
        window.scrollTo({
          top: offsetPosition,
          behavior: 'auto'
        });
      }
      targetElementRef.current = id;
      setActiveElement(id);
  
      setTimeout(() => {
        isScrollingRef.current = false;
      }, 1000);  
    }
  };
   
  
  
    useEffect(() => {

        const handleScroll = () => {
          if (window.scrollY >= 950) {
            setIsScrolled(true);
          } else {
            setIsScrolled(false);
          }
        };
      
        window.addEventListener('scroll', handleScroll);
        return () => {
          window.removeEventListener('scroll', handleScroll);
        };
    
      }, []);

useEffect(() => {
    const handleResize = () => {
      const isSmallScreen = window.innerWidth <= 460;
      setElementToShow(!isSmallScreen);  
    };
    handleResize();
    window.addEventListener('resize', handleResize);
  
    return () => window.removeEventListener('resize', handleResize);
  }, []); 

   
    useEffect(() => {
        const fetchData = async () => {
          try {
            const response = await fetch(`${apiUrl}/api/sticky-banners?populate=deep,4`);
            if (!response.ok) {
              throw new Error(`API request failed with status ${response.status}`);
            }
            const data = await response.json();

            setBannerFullData(data.data[0].attributes);
            setBannerData(data.data[0].attributes.sticky_banner_item)
            setPdfdata({ pdfLink: learnTrackData.pdf_download_link.data.attributes.url })
          } catch (error) {
            console.error('Error fetching data:', error);
          }
        };
    
        fetchData();
      }, [apiUrl]);

      useEffect(() => {
        const handleScrollBanner = () => {
          if (isScrollingRef.current) return;
      
          let mostRelevantSection = null;
          let mostRelevantSectionBottom = Number.NEGATIVE_INFINITY;
      
          bannerData&& bannerData.forEach((sectionId) => {
            const element = document.getElementById(sectionId.banner_item);
            if (element) {
              const rect = element.getBoundingClientRect();
              // Check if the bottom of the section is in view but not the top
              if (rect.bottom >= 0 && rect.top <= window.innerHeight/2) {
                if (rect.bottom > mostRelevantSectionBottom) {
                  mostRelevantSectionBottom = rect.bottom;
                  mostRelevantSection = sectionId.banner_item;
                }
              }
            }
          });
      
          if (mostRelevantSection && targetElementRef.current !== mostRelevantSection) {
            targetElementRef.current = mostRelevantSection;
            setActiveElement(mostRelevantSection);
          }
        };
        window.addEventListener('scroll', handleScrollBanner);
        return () => {
          window.removeEventListener('scroll', handleScrollBanner);
        };
      }, [bannerData, activeElement]);


      if( !bannerData){
        return <div className={`${classes.equalPadding} mb-5 container d-flex justify-content-center`}>
          <div className={`spinner-border  isb-text-color`}  role="status"/>
        </div>
         
      }

const containerMargin = 'container-fluid mt-md-5 mt-2 px-0'
const storiesArrayLength = learnTrackData.story_fold?.length
  return (
    <div>
      <div className={`${!isScrolled ? classes.banner : classes.sticky_banner}`} style={{ top: modalStatus ? "0px" : window.innerWidth <= 768 ? "56px" : "90px" }}>
        <div className={` `}>
          <div className={`row mx-auto container px-0  ${classes.bannerstyles} ${classes.equalPadding}`}>
            <div className="col px-0 py-2 align-items-center  ">
              <ul className={`${classes.banner_ul} ${!elementToShow ? '  px-2' : ''} w-100 d-flex py-1 pe-lg-4 ps-0 justify-content-md-end justify-content-center align-items-center mb-0`}>
                {(elementToShow ? bannerData : bannerData.slice(storiesArrayLength ===0 ? 0 : 1, -1)).map((item, index) =>{ 
                  if(storiesArrayLength===0 && item.banner_item ==="Stories"){
                    return null;
                  }else return(
                  <li className="list-group-item px-md-4 px-2 pointer " key={index}>
                    <a
                      onClick={() => handleListItemClick(item.banner_item)}
                      className={`${activeElement === item.banner_item
                          ? classes.banner_active_item
                          : classes.banner_inactive_item
                        } text-black`}
                    >
                      {storiesArrayLength===0 && item.banner_item ==="Stories" ? "":  item.banner_item}
                    </a>
                  </li>
                )})}
              </ul>

            </div>
            <ApplyNowBtn
            fullUrl={fullUrl}
              learnTrackData={learnTrackData}
              classes={classes}
              bannerFullData={bannerFullData}
              qpms={qpms}
              andQpms={andQpms}
              baseURL={baseURL}
            />
          </div>
        </div>
      </div>
         
          <div id={bannerData[0].banner_item} className={`${containerMargin}`}>
            <div  className=" align-items-center d-flex flex-column">

             {/* Key Concepts */}
             <KeyConcepts 
             divbsclasses={`${classes.equalPadding}  shadow-sm bg-white col-lg-12 py-4 py-lg-4 py-md-4 px-md-4 px-lg-4 px-1`}
             headingClass={classes.sideHeads}
             ulclass ={'d-flex row ps-md-4 ms-md-3 ps-3 me-lg-0 mx-0 p-2 gap-md'}
             liclass={`${classes.custombulleticon} pe-lg-4 pt-1 col-lg-6 `}
             arrayData={learnTrackData} 
             title={bannerFullData.key_concepts_title}
             />
            
              {/* who will benefit */}
              <div className={`${classes.equalPadding} ${containerMargin}`}>
                <Whowillbenefit apiUrl={apiUrl} learnTrackData={learnTrackData} bannerFullData={bannerFullData}/>
              </div>

              {/* what you gain */}
          
              <div className={`${classes.equalPadding} ${containerMargin}`}>
                {courseData.data[0] && <WhatYouGain
                bdcnote={landingPageResponse.bdc_note}
                classes={classes}
                title={bannerFullData.certs_badges_title}
                certificateData={courseData.data[0].attributes.Course_page_certificates}
                qpms={qpms}
                queQpms={queQpms}
                apiUrl={apiUrl}
                
                />}
              </div>
              {/* what you gain end*/}
            </div>
          </div>


        {landingPageResponse.AI_Component?.ai_image_component?.length>0 &&  <div  className={`${containerMargin} ${classes.equalPadding}`}>
          <div className={`${classes.equalPadding}`}>
         <AiComponent  apiUrl={apiUrl} landingPageResponse={landingPageResponse}/>
         </div>
          </div>}

     {learnTrackData.new_highlightcards?.length>0 &&  <div className={`${containerMargin} ${classes.equalPadding} shadow-sm  justify-content-center px-0 bg-white`}>
        <HighlightsSection
          classes={classes}
          apiUrl={apiUrl}
          data={learnTrackData.new_highlightcards}
          title={landingPageResponse.new_highlight_title}
        //  cssflag={true}
        // flag={true}

        />
      </div>}
          
          <div id={bannerData[1].banner_item} className={containerMargin}>
            <div className=" align-items-center d-flex flex-column">
              <div className={`${classes.equalPadding} container px-md-4 p-3 shadow-sm bg-white col-lg-12 py-4 py-lg-4   `}  >
                <CardComponent utm_source={utm_source}
                query={query}
                utm_medium={utm_medium}
                utm_campaign={utm_campaign}
                utm_content={utm_content}
                utm_term={utm_term}
                 meetupId={meetupId} isv2={isv2} getModalStat={getModalStat} landingPageResponse={landingPageResponse} title={bannerFullData.syllabus_title} pdfdata={pdfdata} apiUrl={apiUrl} programDetails={programDetails} courseData={courseData} baseURL={baseURL}/>
              </div>

              {/* onlineAdvantage */}
             {learnTrackData.what_sets_us_apart && <div className={`${classes.equalPadding} ${containerMargin}`}  >
                <OnlineAdvantage apiUrl={apiUrl}  />
              </div>}
            </div>
          </div>
          <div id={bannerData[2].banner_item} className={`${classes.faulty_slider} ${classes.equalPadding} px-0 ${containerMargin}`}>

            <FacultySlider
              courseProf={courseProf}
              title={bannerFullData.faculty_fold_title}
              apiUrl={apiUrl}
            />

          </div>
        {learnTrackData.story_fold?.length>0 &&  <div id={bannerData[3].banner_item} className={`${containerMargin} d-flex justify-content-center px-0 `}>
            <StoriesSlider title={bannerFullData.story_fold_title} apiUrl={apiUrl} storyData={learnTrackData.story_fold} default_settings={true} />
          </div>}

        

<IsbAdvantage landingPageResponse={landingPageResponse} classes={classes}/>

          
        <Image
          className="img-fluid w-100 mt-2 mt-md-4"
          src={banner}
          priority={true}
          alt="banner_image"
          sizes="(max-width: 640px) 100vw, (max-width: 1280px) 50vw, 25vw"
        />




       {interestedSection && <InterestedSection 
       classes={classes}
       interestedSection={interestedSection} 
       apiUrl={apiUrl} 
       qpms={qpms}
       queQpms={queQpms}
       trackFoldData={trackFoldData}

       />  }   


      <TrackPageBottomFold getIsbMail={getIsbMail} apiUrl={apiUrl}/>
          </div>
          
  )
}
