import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import dynamic from 'next/dynamic';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import classes from '../pages/v2/[learning_track_id]/index.module.css';


const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });

const OnlineAdvantage = (props) => {
    const apiUrl = props.apiUrl;
    const lpflag = props.lpflag;
    const [activeItem, setActiveItem] = useState();
    const [videoBool, setVideoBool] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [isSmallscreen, setIsSmallscreen] = useState(false);
    const [advantagesData, setAdvantagesData] = useState(null);
    const [isClient, setIsClient] = useState(false);
    const [videoMob, setVideoMob] = useState(false);
    const [videoPauseFlag, setVideoPauseFlag]= useState()
    const [advVideoPauseFlag, setAdvVideoPauseFlag]= useState(true)
    const mediaContainerRef = useRef(null);
    const Advantageref = useRef(null);
    const mediaWidth = 535;
    const mediaMobileWidth = 340;

    const handleItemClick = (item) => {
        setActiveItem(item);
        setVideoBool(false);
        if(item.video_url){
            setAdvVideoPauseFlag(true)
        }else{

            setAdvVideoPauseFlag(false)
        }
    };

    const handleVideoPlayer = () => {
        setVideoBool(true);
    };

    const handleVideoMobPlayer = () => {
        setVideoMob(true);
        setVideoPauseFlag(true)
        setAdvVideoPauseFlag(true)
    };

    const handleScroll = () => {
        if (mediaContainerRef.current) {
            const rect = mediaContainerRef.current.getBoundingClientRect();
            const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;
            if (!isVisible) {
                setVideoPauseFlag(false);
            }
        }
    };
    const handleScrollAdv = () => {
        if (Advantageref.current) {
            const rect = Advantageref.current.getBoundingClientRect();
            const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;
            if (!isVisible) {
                setAdvVideoPauseFlag(false);
            }
        }
    };

    useEffect(() => {
        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);
    useEffect(() => {
        window.addEventListener('scroll', handleScrollAdv);

        return () => {
            window.removeEventListener('scroll', handleScrollAdv);
        };
    }, []);


    useEffect(() => {
        setIsClient(true);
        const fetchData = async () => {
            try {
                const response = await fetch(`${apiUrl}/api/new-isb-online-advantage?populate=deep,3`);
                if (!response.ok) {
                    throw new Error(`API request failed with status ${response.status}`);
                }
                const data = await response.json();
                setAdvantagesData(data.data.attributes);
                setActiveItem(data.data.attributes.new_onlineadvantage[0] || {});
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, [apiUrl]);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth <= 990);
            setVideoMob(false);
            setIsSmallscreen(window.innerWidth <= 499);
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    if (!isClient) {
        return null;
    }
const nextcustomStyles={
    paddingTop:"10rem",
    marginRight:"10px"
    
    
}
const prevcustomStyles={
    paddingLeft:"10px",
    paddingTop:"10rem"
    

}

    const sliderSettings = {
        dots: true,
        infinite: false,
        speed: 400,
        slidesToShow: 1,
        slidesToScroll: 1,
        nextArrow: <NextArrows nextcustomStyles={nextcustomStyles} />,
        prevArrow: <PrevArrows prevcustomStyles={prevcustomStyles} />,
        responsive: [
      {
        breakpoint: 990,
        settings: {
          arrows: true,
          slidesToShow: 1,
        },
      },
    ],
    };
    return (
        <>
            {advantagesData && (
                <div className={`${lpflag ? "":"shadow-sm"} pt-4 px-md-4 p-0 bg-white `}>
                    <h2 className={`${classes.sideHeads} pb-4`}>{advantagesData.main_title}</h2>
                    <p className="description px-3">{advantagesData.description}</p>
                    <div className={classes.slider_main}>
                        <div className={`${classes.advantageContent} px-4 pb-5`}>
                            {isMobile ? (
                                <Slider {...sliderSettings}>
                                    {advantagesData.new_onlineadvantage.map((item) => (
                                        <div key={item.id} className="">
                                            <div
                                                className={`${classes.advantageitem} mb-0 px-0 pb-0 ${
                                                    item.id === activeItem.id
                                                        ? classes.activeadvantageitem
                                                        : classes.inactiveadvantageitem
                                                }`}
                                                onClick={() => handleItemClick(item)}
                                            >
                                            <div className={`${classes.mediaContainer} mb-4`} ref={mediaContainerRef}>
                                                    {item.video_url && item.video_url.trim() !== '' ? (
                                                        !videoMob || !videoPauseFlag? (
                                                            <ReactPlayer
                                                                url={item.video_url}
                                                                width={isSmallscreen ? '100%' : mediaMobileWidth}
                                                                height={videoMob ? 'auto' : '200px'}
                                                                light={true}
                                                                style={{ cursor: 'pointer', backgroundColor: 'black', minHeight: '190px' }}
                                                                className="react-player"
                                                                onClickPreview={() => handleVideoMobPlayer()}
                                                                playing={videoMob}
                                                                controls={true}
                                                                preload={true}
                                                            />
                                                        ) : (
                                                            <iframe
                                                                allow="autoplay; fullscreen; picture-in-picture"
                                                                src={item.video_url}
                                                                width={`${mediaMobileWidth + 'px'}`}
                                                                height={200}
                                                            />
                                                        )
                                                    ) : item.advantage_image &&
                                                      item.advantage_image.data &&
                                                      item.advantage_image.data.attributes &&
                                                      item.advantage_image.data.attributes.url ? (
                                                        <Image
                                                            loading='lazy'
                                                            width={mediaMobileWidth}
                                                            height={200}
                                                            src={apiUrl + item.advantage_image.data.attributes.url}
                                                            alt="ISB Online Advantage "
                                                            style={{ objectFit: 'cover' }}
                                                        />
                                                    ) : (
                                                        <p className="">No media available</p>
                                                    )}
                                                </div>
                                                <div className="position-relative d-flex gap-2 px-1">
                                                    <Image
                                                        width={35}
                                                        height={35}
                                                        src={apiUrl + item.icon.data.attributes.url}
                                                        alt=""
                                                    />
                                                    <div
                                                        className={`${classes.advitemlist} mb-0`}
                                                        dangerouslySetInnerHTML={{ __html: item.description }}
                                                    />
                                                </div>
                        
                                            </div>
                                        </div>
                                    ))}
                                </Slider>
                            ) : (
                                <div className="main row">
                                    <ul className={`${classes.advitemlist} col`}>
                                        {advantagesData.new_onlineadvantage.map((item) => (
                                            <li
                                                key={item.id}
                                                className={`${classes.advantageitem} ${
                                                    item.id === activeItem.id
                                                        ? classes.activeadvantageitem
                                                        : classes.inactiveadvantageitem
                                                }`}
                                                onClick={() => handleItemClick(item)}
                                            >
                                                <div className="position-relative d-flex gap-2">
                                                    <Image
                                                        width={35}
                                                        height={35}
                                                        src={apiUrl + item.icon.data.attributes.url}
                                                        alt=""
                                                    />
                                                    <div
                                                        className={classes.advitemlist}
                                                        dangerouslySetInnerHTML={{ __html: item.description }}
                                                    />
                                                </div>
                                            </li>
                                        ))}
                                    </ul>
                                    <div className={`${classes.oAdivplayer} col px-0 right`} style={{ height: 'fit-content' }} ref={Advantageref}>
                                        {activeItem.video_url && activeItem.video_url.trim() !== '' ? (
                                            <ReactPlayer
                                                style={{ minHeight: '300px' }}
                                                allow="autoplay; fullscreen; picture-in-picture"
                                                url={activeItem.video_url}
                                                width={mediaWidth}
                                                height={300}
                                                playing={advVideoPauseFlag}
                                                                controls={true}
                                                                preload={true}
                                            />
                                        ) : activeItem.advantage_image &&
                                          activeItem.advantage_image.data &&
                                          activeItem.advantage_image.data.attributes &&
                                          activeItem.advantage_image.data.attributes.url ? (
                                            <Image
                                                width={mediaWidth}
                                                height={300}
                                                loading='lazy'
                                                src={apiUrl + activeItem.advantage_image.data.attributes.url}
                                                alt="ISB Online Advantage"
                                                style={{ objectFit: 'cover' }}
                                            />
                                        ) : (
                                            <p className="">No media available</p>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default OnlineAdvantage;

export function NextArrows(props) {
    const { className, style, onClick, nextcustomStyles } = props;
    return (
        <div
            className={className}
            style={{ ...style, ...nextcustomStyles, display: "block" }}
            onClick={onClick}
        />
    );
}

export function PrevArrows(props) {
    const { className, style, onClick, prevcustomStyles } = props;
    return (
        <div
            className={className}
            style={{ ...style, ...prevcustomStyles, display: "block" }}
            onClick={onClick}
        />
    );
}