:root{
  --isb-blue-color:#057092;
  --isb-purple-color:#632568;
  --isb-lightblue-color:rgba(241, 250, 255, 1);
  --isb-child-banner-minheight:8rem;
  --isb-container-max-width:1200px;
  --isb-edge-p-font-size:12px;
  --isb-edge-p-font-color:#7F7F7F;
  --isb-main-navbar-height:90px;
  --isb-edge-breadcrumb-navbar-height:60px;
  --isb-edge-border : 1px solid #D9D9D9;
  --isb-edge-body-background-color:#F4F8FA;
  --isb-edge-blue-font-color:#192890;
  --isb-edge-green-color:#AAF055;
  --isb-edge-font-family-Reckless:Reckless;
  --isb-edge-font-family-inter:Inter;
  --isb-edge-font-family-inter-variable:"inter-variable",sans-serif;

}
html {
  overflow-y: scroll;
}
.custom-font p {
  font-family: var(--isb-edge-font-family-inter), sans-serif;
}
/* ::-webkit-scrollbar {
  width:12px !important;
  height: 10px !important;
}
::-webkit-scrollbar-thumb {
  background: #afafaf;
  border-radius: 5px;
  
}

::-webkit-scrollbar-track-piece {
  background:  #e4e3e3;
  border-radius: 5px;
}  */
body,html {
  padding: 0px;
  margin: 0px;
  font-family: Open Sans, sans-serif !important;
  background-color: #F4F4F4;
  overflow-x: clip;  
}
.pointer{
  cursor: pointer;
}
.c-default{
  cursor: default;
}
p,li{
  
    color: #585865;
    text-decoration: none;
    align-items: baseline;
    font-size: 17px;
    vertical-align: center;
    font-weight:400;
  
}

h1{
font-size: 2rem;
}

h3{
  font-style: normal;
font-weight: 700;
font-size: 20px;
line-height: 27px;
}

ol{
  color: #585865;
    text-decoration: none;
    align-items: center;
    font-size: 17px;
    vertical-align: center;    
}

h5{
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  color: #057092;
  margin-top: 8px;
}


.slick-dots li.slick-active button:before {
  color: #057092 !important;
  font-size: 15px !important;
  opacity: 1 !important;
}
/* .purpleslidercontainer .slick-dots li.slick-active button:before {
  color: var(--isb-purple-color) !important;
  font-size: 15px !important;
  opacity: 1 !important;
} */
.slick-dots li button:before{
  color: #D9D9D9 !important;
  font-size: 15px !important;
  opacity: 1 !important;
}


iframe {
  min-height: 320px !important;
  /* max-height: 320px !important; */
  width: 100vw;
}

.form-control {
  border-radius: 0;
  border-color: #708FAB;
}

.form-control:focus {
  border-color: #708FAB;
  box-shadow: 0 0 0 0.0rem rgba(16, 29, 53, 0.25);
}

iframe {
  min-height: 280px !important;
  width: 100vw;
}

/* Increased specificity to override default styles */
.slick-prev
.slick-next {
  font-size: 20px; /* Adjust the font size as needed */
  line-height: 1;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.slick-prev {
  left: 100px; /* Adjust the left position as needed */
}

.slick-next {
  right: 10px; /* Adjust the right position as needed */
}

/* Customize the arrow icons */
.slick-prev::before{
  color: transparent !important;
  display: inline-block;
  background: url('../assets/Back.svg') no-repeat center center;
  width: 20px; /* Adjust the width as needed */
  height: 20px; 

}
.slick-next::before {
  color: transparent !important;
  display: inline-block;
  background: url('../assets/Black.svg') no-repeat center center;
  width: 20px; /* Adjust the width as needed */
  height: 20px; /* Adjust the height as needed */
}

/* Additional CSS for hover effects or other styles */
.slick-prev:hover,
.slick-next:hover {
  color: transparent !important;
  /* Add hover styles as needed */
}

/* Additional CSS for active or disabled styles */
.slick-prev.slick-disabled,
.slick-next.slick-disabled {
  color: transparent !important;
  /* Add disabled styles as needed */
}

  .react-tel-input .flag-dropdown{
    background-color: #EFF9FE !important;
    border-color: #708FAB !important;
    border:0px !important;
  }
  div[aria-describedby = "rpv-core__tooltip-body-theme-switch"]{
    display:none !important;

  }
 .rpv-open__input-wrapper{
  display:none !important;
 }
 .rpv-toolbar{
  font-size:14px !important;
 }
 .rpv-core__icon{
  width:15px !important;
 }

 .bg-isb{
  background-color: var(--isb-blue-color);
 }
 .isb-text-color{
  color: var(--isb-blue-color);
 }