import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from './TopicsDropdown.module.css';

const TopicsDropdown = ({ apiPosts: propApiPosts, apiPost: propApiPost, apiPodcast: propApiPodcast, apiVideo: propApiVideo }) => {
  // Initialize state for API data
  const [apiPosts, setApiPosts] = useState(propApiPosts || []);
  const [apiPost, setApiPost] = useState(propApiPost || []);
  const [apiPodcast, setApiPodcast] = useState(propApiPodcast || []);
  const [apiVideo, setApiVideo] = useState(propApiVideo || []);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const closeTimeoutRef = useRef(null);
  const dropdownRef = useRef(null);
  const visibilityTimeoutRef = useRef(null);
  const router = useRouter();

  // static fallback of content types
  const [contentTypes, setContentTypes] = useState([   
    { type: 'article', label: 'Articles' },
    { type: 'infographic', label: 'Infographics' },
    // { type: 'podcast', label: 'Podcasts' },
    // { type: 'video', label: 'Videos' }
  ]);


  const [allTopics, setAllTopics] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

  // Add an effect to handle clicks outside the dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        handleDropdownClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch edge-main-page API data if props not provided
  useEffect(() => {
    const fetchMainPageData = async () => {
      // Skip if props were provided or if we already loaded the API data
      if ((propApiPosts && propApiPost && propApiPodcast && propApiVideo) || isApiLoaded) {
        return;
      }
      
      try {
        const response = await fetch(`${baseUrl}/api/edge-main-page?populate=deep`);
        if (!response.ok) {
          throw new Error('Failed to fetch edge-main-page data');
        }
        
        const data = await response.json();
        
        // Extract the data from the response
        if (data?.data?.attributes) {
          setApiPosts(data.data.attributes.articles?.data || []);
          setApiPost(data.data.attributes.infographics?.data || []);
          setApiPodcast(data.data.attributes.podcasts?.data || []);
          setApiVideo(data.data.attributes.videos?.data || []);
          setIsApiLoaded(true);
        }
      } catch (error) {
        console.error('Error fetching edge-main-page data:', error);
      }
    };
    
    fetchMainPageData();
  }, [baseUrl, propApiPosts, propApiPost, propApiPodcast, propApiVideo, isApiLoaded]);

  // Fetch topics from API
  useEffect(() => {
    const fetchTopics = async () => {
      setIsLoading(true);
      try {
        // First create a combined array similar to alltopics page
        const combinedArray = [
          ...(apiPosts || []).map(post => ({
            id: post.id,
            attributes: {
              ...(post.attributes || {}),
              topics: post.attributes?.topics,
              post_type: 'article'
            }
          })),
          ...(apiPost || []).map(post => ({
            id: post.id,
            attributes: {
              ...(post.attributes || {}),
              topics: post.attributes?.topics,
              post_type: 'infographic'
            }
          })),
          ...(apiPodcast || []).map(post => ({
            id: post.id,
            attributes: {
              ...(post.attributes || {}),
              topics: post.attributes?.topics,
              post_type: 'podcast'
            }
          })),
          ...(apiVideo || []).map(post => ({
            id: post.id,
            attributes: {
              ...(post.attributes || {}),
              topics: post.attributes?.topics,
              post_type: 'video'
            }
          }))
        ];
        
        // Extract topics using the same approach as alltopics.js
        const topicsSet = new Set();
        combinedArray.forEach(item => {
          if (item.attributes?.topics && item.attributes.topics.data) {
            item.attributes.topics.data.forEach(topicItem => {
              if (topicItem.attributes && topicItem.attributes.topic_name) {
                topicsSet.add(topicItem.attributes.topic_name);
              }
            });
          }
        });
        
        const extractedTopics = Array.from(topicsSet).sort();
        
        if (extractedTopics.length > 0) {
          setAllTopics(extractedTopics);
        } else {
          const response = await fetch(`${baseUrl}/api/topics?populate=*`);
          if (!response.ok) {
            throw new Error('Failed to fetch topics');
          }
          const data = await response.json();
          
          if (data && data.data && Array.isArray(data.data)) {
            const topicNames = data.data
              .filter(topic => topic && topic.attributes && topic.attributes.topic_name)
              .map(topic => topic.attributes.topic_name)
              .filter(name => name)
              .sort();
              
            setAllTopics(topicNames);
          }
        }
      } catch (error) {
        console.error('Error extracting or fetching topics:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTopics();
  }, [apiPosts, apiPost, apiPodcast, apiVideo, baseUrl]);


  useEffect(() => {
    if (!apiPosts?.length && !apiPost?.length && !apiVideo?.length && !apiPodcast?.length) {
      return;
    }
    const types = new Set();

    const extractContentTypes = (items) => {
      if (!items || !Array.isArray(items) || items.length === 0) return;
      
      items.forEach(item => {
        if (!item || !item.attributes) return;
        
        const attributes = item.attributes;
        
        Object.keys(attributes).forEach(key => {
          if (key.includes('_first_section') && attributes[key]?.post_type) {
            types.add(attributes[key].post_type);
          }
        });
        
        if (attributes.post_type) {
          types.add(attributes.post_type);
        }
      });
    };

    extractContentTypes(apiPosts);
    extractContentTypes(apiPost);
    extractContentTypes(apiVideo);
    extractContentTypes(apiPodcast);
    if (types.size > 0) {
    const typesArray = Array.from(types).map(type => {
      return { 
        type, 
        label: type.charAt(0).toUpperCase() + type.slice(1).toLowerCase() + 's'
      };
    });

    setContentTypes(typesArray);
    }
  }, [apiPosts, apiPost, apiPodcast, apiVideo]);

  useEffect(() => {
    if (isDropdownOpen) {
      setIsDropdownVisible(true);
      if (visibilityTimeoutRef.current) {
        clearTimeout(visibilityTimeoutRef.current);
      }
    } else {
      if (visibilityTimeoutRef.current) {
        clearTimeout(visibilityTimeoutRef.current);
      }
      visibilityTimeoutRef.current = setTimeout(() => {
        setIsDropdownVisible(false);
      }, 300); // Match transition duration in CSS
    }
  }, [isDropdownOpen]);

  const handleMouseEnter = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
    setIsDropdownOpen(true);
  };

  const handleMouseLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      handleDropdownClose();
    }, 300); // 300ms delay before closing
  };

  const handleDropdownClose = () => {
    setIsDropdownOpen(false);
  };

  useEffect(() => {
    if (isDropdownOpen && allTopics.length > 0) {
      router.prefetch('/perspectives/alltopics');
    }
  }, [isDropdownOpen, allTopics, router]);
  
  const handleTopicClick = (topic) => {
    if (!topic) {
      console.error("Topic is undefined or null");
      return;
    }
    
    const formattedTopic = encodeURIComponent(topic.trim().replace(/\s+/g, '_'));
    const queryParams = { topic: formattedTopic };
    Object.keys(router.query).forEach(key => {
      if (key.startsWith('utm_')) {
        queryParams[key] = router.query[key];
      }
    });

    if (typeof window !== 'undefined') {
      sessionStorage.setItem('isb_topic_loading', 'true');
      sessionStorage.setItem('isb_selected_topic', topic);
      sessionStorage.setItem('isb_formatted_topic', formattedTopic);
      
      sessionStorage.setItem('isb_direct_navigation', 'true');
    }

    handleDropdownClose();

    
    router.push({
      pathname: '/perspectives/alltopics',
      query: queryParams
    });
  };

  const renderTopicsGrid = () => {
    if (isLoading) {
      return <div className={styles.topicItem}>Loading topics...</div>;
    }

    if (!allTopics || allTopics.length === 0) {
      return <div className={styles.topicItem}>No topics available</div>;
    }

    // Detect if we're on mobile
    const isMobileView = typeof window !== 'undefined' && window.innerWidth <= 768;
    
    // Use a single-column layout on mobile, or the standard 3-column layout on desktop
    const rowsPerColumn = isMobileView ? [12] : [4, 4, 3]; 
    const maxTopicsToDisplay = rowsPerColumn.reduce((sum, rows) => sum + rows, 0);
    
    const filteredTopics = allTopics.filter(topic => !!topic); // Ensure no null topics
    const topicsToDisplay = filteredTopics.slice(0, maxTopicsToDisplay);
    
    const totalTopics = topicsToDisplay.length;
    
    let grid = [];
    
    if (totalTopics <= 4) {
      const columnItems = topicsToDisplay.map((topic, index) => (
        <div
          key={`topic-${index}`}
          className={styles.topicItem}
          onClick={() => handleTopicClick(topic)}
        >
          {topic}
        </div>
      ));
      
      grid = [
        <div key="column-0" className={styles.topicColumn}>
          {columnItems}
          {filteredTopics.length > maxTopicsToDisplay && (
            <div
              key="view-all-topics"
              className={`${styles.topicItem} ${styles.viewAllTopics}`}
              onClick={() => {
                const queryParams = {};
                Object.keys(router.query).forEach(key => {
                  if (key.startsWith('utm_')) {
                    queryParams[key] = router.query[key];
                  }
                });
                
                router.push({
                  pathname: "/perspectives/alltopics", 
                  query: queryParams
                });
              }}
            >
              View All Topics
            </div>
          )}
        </div>
      ];
    } else if (totalTopics <= 8) {
      // Two columns for 5-8 topics
      const column1Count = Math.ceil(totalTopics / 2);
      const column1Items = topicsToDisplay.slice(0, column1Count).map((topic, index) => (
        <div
          key={`topic-${index}`}
          className={styles.topicItem}
          onClick={() => handleTopicClick(topic)}
        >
          {topic}
        </div>
      ));
      
      const column2Items = topicsToDisplay.slice(column1Count).map((topic, index) => (
        <div
          key={`topic-${column1Count + index}`}
          className={styles.topicItem}
          onClick={() => handleTopicClick(topic)}
        >
          {topic}
        </div>
      ));
      
      grid = [
        <div key="column-0" className={styles.topicColumn}>
          {column1Items}
        </div>,
        <div key="column-1" className={styles.topicColumn}>
          {column2Items}
          {filteredTopics.length > maxTopicsToDisplay && (
            <div
              key="view-all-topics"
              className={`${styles.topicItem} ${styles.viewAllTopics}`}
              onClick={() => {
                const queryParams = {};
                Object.keys(router.query).forEach(key => {
                  if (key.startsWith('utm_')) {
                    queryParams[key] = router.query[key];
                  }
                });
                
                router.push({
                  pathname: "/perspectives/alltopics", 
                  query: queryParams
                });
              }}
            >
              View All Topics
            </div>
          )}
        </div>
      ];
    } else {
      // Three columns for 9+ topics
      const perColumn = Math.ceil(totalTopics / 3);
      
      const columns = [[], [], []];
      
      // Distribute topics evenly
      topicsToDisplay.forEach((topic, index) => {
        const columnIndex = Math.min(Math.floor(index / perColumn), 2);
        columns[columnIndex].push(
          <div
            key={`topic-${index}`}
            className={styles.topicItem}
            onClick={() => handleTopicClick(topic)}
          >
            {topic}
          </div>
        );
      });
      
      // Add "View All Topics" link to the third column
      if (filteredTopics.length > maxTopicsToDisplay) {
        columns[2].push(
          <div
            key="view-all-topics"
            className={`${styles.topicItem} ${styles.viewAllTopics}`}
            onClick={() => {
              const queryParams = {};
              Object.keys(router.query).forEach(key => {
                if (key.startsWith('utm_')) {
                  queryParams[key] = router.query[key];
                }
              });
              
              router.push({
                pathname: "/perspectives/alltopics", 
                query: queryParams
              });
            }}
          >
            View All Topics
          </div>
        );
      }
      
      // Create grid with columns
      grid = columns.map((columnItems, colIndex) => (
        <div key={`column-${colIndex}`} className={styles.topicColumn}>
          {columnItems}
        </div>
      ));
    }

    return (
      <div className={styles.topicsGridLayout}>
        {grid}
      </div>
    );
  };

  return (
    <div
      ref={dropdownRef}
      className={styles.topicsDropdownContainer}
    >
      <div className={styles.topicsNavContainer}>
        <ul className={styles.navTabs}>
          <li
            className={`${styles.navTab} ${styles.topicsTab} ps-0`}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <span className='ps-0'>Topics</span> <span className={styles.downArrow}>▼</span>
          </li>

          {/* Dynamically generate content type links based on available content types */}
          { 
            contentTypes.map((contentType, index) => {
              // Get all current query parameters
              const currentQuery = { ...router.query };
              
              // Update with the new content type
              const newQuery = { ...currentQuery, type: contentType.type };
              
              // Create the href with all parameters preserved
              const href = {
                pathname: '/perspectives/alltopics',
                query: newQuery
              };
              
              return (
                <li key={`content-type-${index}`} className={`${styles.navTab} ${index === contentTypes.length - 1 ? "pe-0" : ''}`}>
                  <Link
                    href={href}
                    className={styles.navLink}
                  >
                    {contentType.label}
                  </Link>
                </li>
              );
            })
          }
        </ul>

        {isDropdownVisible && (
          <div
            className={`${styles.dropdownContent} ${isDropdownOpen ? styles.dropdownOpen : styles.dropdownClosed}`}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <div className={styles.topicsGrid}>
              {/* Dynamically render the topics grid */}
              {renderTopicsGrid()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TopicsDropdown;
