import { useState, useEffect, useRef } from "react";
import classes from "../pages/v2/[learning_track_id]/index.module.css";
import Image from "next/image";
import userIcon from "../assets/user_icon.svg"

export const Whowillbenefit = ({ bannerFullData, bgColor, apiUrl, learnTrackData}) => {
    const TabContentMain = ({ i, index }) => {
        return (
            <div key={index} className={`container px-lg-5 px-md-4 mx-3 pt-4 pb-3  ${classes.wwb_tabContentMain}`}>
                <div className="row d-flex gap-md-0 gap-4 align-items-center">
                    <div className="col-lg-6 col-12 pe-md-5 ps-md-3 align-self-baseline">
                        <span className={`fs-5 ${classes.p_inside_span}`} dangerouslySetInnerHTML={{ __html: (i.left_content) }} />
                    </div>
                   {(i.learner_details?.learner_name || i.right_content )&& <div className={`col flex-column gap-3 align-items-center align-self-baseline`}>
                        <span className={classes.p_inside_span} dangerouslySetInnerHTML={{ __html: (i.right_content) }} />
                        <div className={`${classes.profilediv} d-flex gap-3`}>
                           {i.learner_details.learner_image.data && <div className={classes.wwb_profile_img_div}>
                                <Image
                                    alt="Profile Image"
                                    width={55}
                                    height={55}
                                    priority={true}
                                    sizes="100vw"
                                    style={{ borderRadius: "50%" }}
                                    src={`${apiUrl}${i.learner_details.learner_image.data.attributes.formats.thumbnail.url}`}
                                />
                            </div>}
                            <div>
                                <h6>{i.learner_details.learner_name}</h6>
                                <span>{i.learner_details.learner_designation}</span>
                            </div>
                        </div>
                    </div>}
                </div>
            </div>
        );
    };
    const tabs = learnTrackData.wwb.map((i, index) => ({
        label: i.sub_title,
        content: <TabContentMain i={i} index={index} />
    }));

    const [activeTab, setActiveTab] = useState(tabs && tabs[0].label);
    const [showTriangle, setShowTriangle] = useState(true);
    const navRef = useRef(null);

    useEffect(() => {
        const checkIfItemsWrap = () => {
            if (navRef.current) {
                const navItems = navRef.current.children;
                let top = navItems[0].offsetTop;
                let allInSingleRow = true;

                for (let i = 1; i < navItems.length; i++) {
                    if (navItems[i].offsetTop !== top) {
                        allInSingleRow = false;
                        break;
                    }
                }

                setShowTriangle(allInSingleRow);
            }
        };

        checkIfItemsWrap();
        window.addEventListener('resize', checkIfItemsWrap);
        return () => window.removeEventListener('resize', checkIfItemsWrap);
    }, [tabs]);

    useEffect(() => {
        if (tabs.length > 0) {
            setActiveTab(tabs[0].label); // Set the first tab as active initially
        }
    }, [tabs[0].label]);

    const handleTabSelect = (selectedTab) => setActiveTab(selectedTab);

    const Tab = ({ label, isActive, onSelect }) => (
        <li
            className={`${classes.nav_item} ${(tabs.length <= 2 && label.length < 15) ? 'px-md-5' : "px-md-3"} ${isActive ? `${classes.lpactive_tab} shadow-sm` : ''}`}
            onClick={() => onSelect(label)}
            style={{maxWidth :tabs.length <= 3 ? "355px":"180px"}}
        >
            <div className={`text-center ${classes.listItemContent}`} >
                {label}
            </div>
            {isActive && showTriangle && <div className={bgColor ? classes.lptriangle : classes.triangle}></div>}
        </li>
    );

    const TabContent = ({ children, isActive }) => (
        <div className={`tab-pane shadow-sm align-items-center ${ classes.tabContainer } ${isActive ? ' py-3 mt-md-4 mt-0 d-flex justify-content-center show active' : ''}`}>
            {children}
        </div>
    );

    return (
        <div className={` px-0 pt-md-0 pt-md-4 pt-2 ${classes.wwb_main} ${classes.equalPadding} mx-auto`}>
            <h2 className={`${classes.sideHeads} mb-4 pb-2 px-3`}>{bannerFullData.who_will_benefit}</h2>

            <div className="d-flex justify-content-center">
                <ul ref={navRef} className={`${"classes.navtab_ul"} container px-2 nav ${tabs.length <= 4 && activeTab.length < 5 ? 'gap-md-5' : ""} justify-content-between d-flex mx-0`}>
                    {tabs.map((tab) => (
                        <Tab
                            key={tab.label}
                            label={tab.label}
                            isActive={activeTab === tab.label}
                            onSelect={handleTabSelect}
                        />
                    ))}
                </ul>
            </div>
            <div className="tab-content">
                {tabs.map((tab) => (
                    <TabContent key={tab.label} isActive={activeTab === tab.label}>
                        {tab.content}
                    </TabContent>
                ))}
            </div>
        </div>
    );
}
