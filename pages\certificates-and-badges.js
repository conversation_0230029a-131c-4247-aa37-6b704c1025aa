import Link from "next/link";
import classes from "./certificates-and-badges.module.css";
import Image from "next/image";
import BottomFold from "../components/bottom_fold_one";
import { useRouter } from "next/router";


function CertsAndBadges(props) {
  const data = props.apiData.data.attributes
  const apiUrl = props.apiUrl
  const router = useRouter();
  const { query } = router;
  const qpms = query.utm_source!=undefined?`?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`:``

  return (
    <>
    <div className={`${classes.hideCrumb} mx-auto pt-lg-5 px-3 px-lg-0`}>
               <div className={`d-flex ${classes.breadCrumb} px-1`}>
                 <Link style={{ textDecoration: 'none' }} href={`/${qpms}`}><p className="m-0">ISB Online</p></Link>
                 <svg
                    style={{fill:" #057092"}}
                    width="24"
                    height="18"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_3275_9282)">
                      <path
                        d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_3275_9282">
                        <rect width="24" height="24" fill="#057092" />
                      </clipPath>
                    </defs>
                  </svg>
                  <p className={`m-0 ${classes.breadCrumblast}`}>Certificates and Badges</p>
               </div> 
      </div>
      <div className={`${classes.equalPadding} mx-auto`}>
        <h1 className={`${classes.bluetTitle} mt-3 px-3 px-lg-0`}>{data.bages_and_certs_title}</h1>
        <div className={`${classes.fontEighteenBlack} my-4`}>{data.badges_certs_description}</div>

        <div className="row mt-3 bg-white shadow-sm p-lg-5 p-3 my-5 mx-lg-0 mx-3 text-center text-lg-start">
          <div className="col-12 p-0 col-lg-3">
            <Image
              width="0"
              sizes="100vw"
              className={`img-fluid w-100 ${classes.imageShadow}`} height={350} src={apiUrl + data.master_certificate.certificate_image.data.attributes.url} alt={data.master_certificate.certificate_image.data.attributes.alternativeText} />
          </div>
          <div className="col-lg-9 px-lg-5 col-12">
            <h3 className={`${classes.fontEighteenBlue} mt-lg-0 mt-3`}>{data.master_certificate.certificate_title}</h3>
            <div
              dangerouslySetInnerHTML={{
                __html: data.master_certificate.certificate_description,
              }}
            ></div>
          </div>
        </div>

        <div className="row mt-3 bg-white shadow-sm p-lg-5 p-3 my-lg-5 mx-lg-0 mx-3 text-center text-lg-start">
          <div className="col-12 p-0 col-lg-3">
            <Image
              width="0"
              sizes="100vw"
              className={`img-fluid w-100 ${classes.imageShadow}`} height={350} src={apiUrl + data.course_certificate.certificate_image.data.attributes.url} alt={data.course_certificate.certificate_image.data.attributes.alternativeText} />
          </div>
          <div className="col-lg-9 px-lg-5 col-12">
            <h3 className={`${classes.fontEighteenBlue} mt-lg-0 mt-3`}>{data.course_certificate.certificate_title}</h3>
            <div
              dangerouslySetInnerHTML={{
                __html: data.course_certificate.certificate_description,
              }}
            ></div>
          </div>
        </div>

        <div className="row bg-white shadow-sm p-lg-5 p-3 mt-3 mb-lg-5 mx-lg-0 mx-3 text-center text-lg-start">
          <div className="col-12 p-0 col-lg-3">
            <Image
              priority={true}
              width="0"
              sizes="100vw"
              className={`img-fluid w-100 ${classes.imageShadow}`} height={350} src={apiUrl + data.module_badge.certificate_image.data.attributes.url} alt={data.module_badge.certificate_image.data.attributes.alternativeText} />
          </div>

          <div className="col-lg-9 px-lg-5 col-12">
            <h3 className={classes.fontEighteenBlue}>{data.module_badge.certificate_title}</h3>
            <div
              dangerouslySetInnerHTML={{
                __html: data.module_badge.certificate_description,
              }}
            ></div>
          </div>
        </div>





        <div className="text-center">
       
        </div>
      </div>
      <BottomFold data = {props.bottomFoldData}></BottomFold>
    </>
  )

}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;

  const [response,bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/learning-track-completion?populate[module_badge][populate]=*&populate[course_certificate][populate]=*&populate[master_certificate][populate]=*&populate[meta_tags][populate]=*`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      apiData: response,
      apiUrl: APIUrl,
      bottomFoldData:bottomFoldData
    },
    revalidate:240
  };

}

export default CertsAndBadges
