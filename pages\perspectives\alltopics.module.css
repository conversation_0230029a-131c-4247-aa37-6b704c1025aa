.card {
  position: relative;
  background: white;
  border: 1px solid #e0e0e0;
  height: 100%;
  border-bottom: 3px solid #192890;
  display: flex;
  flex-direction: column;
}
.alltopicsmain{
  max-width: var(--isb-container-max-width);
  margin: 0 auto;
  padding-bottom: 40px;
}
.verticalCard {
  flex-direction: column;
}

.clickable {
  cursor: pointer;
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 200px;
}

.image {
  object-fit: cover !important;
}

.iconWrapper {
  position: absolute;
  bottom: -18px;
  right: 16px;
  z-index: 2;
  background: #AAF05A;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 32px;
  height: 32px;
}

.cardContent {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.category {
  font-size: 14px;
  margin-bottom: 0.5rem;
  text-transform: capitalize;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #192890;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.description {
  font-size: 14px;
  color: #555;
  margin-bottom: 1rem;
  line-height: 1.5;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  line-clamp: 5;
  -webkit-box-orient: vertical;
}

.metaInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #666;
  margin-top: auto;
}

.metaInfo span {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

/* Filter Sidebar Styles */
.filterSidebar {
  padding-top: 0;
  padding: 0 0 1.5rem 0;
  margin-bottom: 1.5rem;
}

.contentTypeFilters{
  border-bottom: 1px solid #ccc;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
}

.filterSection h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #8A8A8A;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filterList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.filterList li {
  margin-bottom: 0.3rem;
}

.filterItem {
  cursor: pointer;
  font-size: 14px;
  color: var(--isb-edge-p-font-color);
  transition: color 0.2s, font-weight 0.2s;
  padding: 2px 4px;
  font-family: var(--isb-edge-font-family-inter);
  font-weight: 400;
}

.filterItem:hover {
  color: #245BFF;
}

.selectedFilter {
  color: #245BFF;
  font-weight: 500;
  position: relative;
}

.filterCheckbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  color: #333;
  transition: color 0.2s;
}

.filterCheckbox input {
  margin-right: 0.5rem;
}

.filterRadio {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  color: #333;
  transition: color 0.2s;
}
.filterRadio input {
  margin-right: 0.5rem;
}
.activeFilter {
  color: #192890;
  font-weight: 600;
}

.activeFilter span {
  position: relative;
}

.activeFilter span::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 100%;
  height: 2px;
  background-color: #192890;
  transform: scaleX(0.7);
  transition: transform 0.2s;
}

.activeFilter:hover span::after {
  transform: scaleX(1);
}

/* Pagination Styles */
.paginationContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
  gap: 10px;
}

.pageNumbers {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin: 0 10px;
}

.paginationButton {
  background-color: white;
  color: #333;
  border: 1px solid #e0e0e0;
  padding: 6px 15px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(.disabled) {
  background-color: #f5f5f5;
}

.pageNumber {
  min-width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  padding: 0;
  border: 1px solid #e0e0e0;
  background-color: white;
  font-size: 14px;
  color: #333;
}

.activePage {
  background-color: #192890;
  color: white;
  border: 1px solid #192890;
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.resultsCount {
  display: inline-block;
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.loadMoreBtn {
  outline: 1px solid var(--isb-edge-blue-font-color) !important;
  color: var(--isb-edge-blue-font-color);
  border: none;
  padding: 0.7rem 1.6rem;
  border-radius: 0;
  font-weight: 600 !important;
  font-size: 14px;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  transition: background-color 0.5s;
}

.loadMoreBtn:hover {
  background-color: #121d6e;
  color: white;
  outline: 1px solid white !important;
}

.noResultsMessage {
  text-align: center;
  padding: 40px 20px;
  margin: 20px auto;
  border-radius: 8px;
  width: 100%;
}

.noResultsMessage p {
  margin-bottom: 10px;
  color: var(--isb-edge-p-font-color);
  font-family: var(--isb-edge-font-family-inter);
}

.noResultsMessage p:first-child {
  font-size: 18px;
  font-weight: 500;
  color: #245BFF;
}

/* Content header styles */
.contentHeader {
  margin-bottom: 1.5rem;
}

.pageTitle {
  font-family: var(--isb-edge-font-family-Reckless);
  font-size: 2rem;
  font-weight: 600;
  color: var(--isb-edge-blue-font-color);
  margin-bottom: 0.5rem;
}

.resultsContainer {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.resultsCount {
  display: inline-block;
  font-size: 1rem;
  color: #8F8F8F;
  padding: 8px 0px 0 ;
  border-radius: 16px;
  font-family: var(--isb-edge-font-family-inter);
}

.noResults {
  background-color: #f8f9fa;
  padding: 2rem;
  text-align: center;
  border-radius: 8px;
  margin: 2rem 0;
}

.resetBtn {
  background-color: #192890;
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  margin-top: 1rem;
  transition: background-color 0.2s;
}

.resetBtn:hover {
  background-color: #121d6e;
}
.allTopicsmain, .allTopicshead{
  background-color: var(--isb-edge-body-background-color);
}
.alltopicsfirstdiv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
  border-bottom: 1.5px solid #00000040;
  padding-bottom: 12px;
}
@media (max-width: 768px) {
  .resultsCount{
    font-size: 12px;
    margin-bottom: 0;
    font-weight: 400;line-height: 100%;
  }
  .pageTitle {
    font-size: 20px;
    font-weight: 600;
    line-height: 100%;
    margin-bottom: 0;
  }
  .alltopicsmain{
    padding: 16px;
    margin-top: 0;
  }
  
  .allTopicshead {
    margin-top: 0;
  }
  
  /* Hide desktop filters and show mobile ones */
  .filterSidebar {
    display: none;
  }
  
  /* .mobileFiltersContainer {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
  } */
  
  /* Mobile dropdown styles */
  .mobileFilterDropdown {
    position: relative;
    /* flex: 0.5 1 100%; */
  }
  
  .mobileFilterButton {
    width: 100%;
    padding: 8px 8px;
    background-color: transparent;
    border: 1px solid #B5B5B5;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    color: #8F8F8F;
  }
  
  .mobileFilterButton:after {
    content: '▼';
    font-size: 12px;
    margin-bottom: -3px;
    color: #8F8F8F;
  }
  .first_drpdwn{
    left: 5px !important;
  }
  .mobileFilterContent {
    position: absolute;
    top: 100%;
    left: 0;
    width: 97%;
    background: var(--isb-edge-body-background-color);
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    z-index: 10;
    max-height: 50vh;
    overflow-y: auto;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  
  .mobileFilterList {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .mobileFilterItem {
    padding: 10px 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    font-size: 14px;
    
  }
  
  .mobileFilterItem:hover {
    background-color: var(--isb-edge-body-background-color);
  }
  
  .mobileSelectedFilter {
    color: var(--isb-edge-blue-font-color);
    font-weight: 500;
  }
}
