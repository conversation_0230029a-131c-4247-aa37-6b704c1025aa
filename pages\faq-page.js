import Link from "next/link";
import classes from "./faq-page.module.css";
import React, {  useState} from "react";
import useCollapse from "react-collapsed";
import BottomFold from "../components/bottom_fold_one";
import { useRouter } from "next/router";



function FAQPage(props) {
  const faqs = props.faqData.data;
  const router = useRouter();
  const { query } = router;
  const qpms = query.utm_source!=undefined?`?utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content}&utm_device=${query.utm_device}&gclid=${query.gclid}&utm_matchtype=${query.utm_matchtype}`:``

  // Get the previous path and its query parameters
  const C = (
    <div className="d-flex">
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.75 10.25H10.25V17.75H7.75V10.25H0.25V7.75H7.75V0.25H10.25V7.75H17.75V10.25Z"
          fill="#057092"
        />
      </svg>
    </div>
  );

  const E = (
    <div className="d-flex">
      <svg
        width="18"
        height="4"
        viewBox="0 0 18 4"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M17.75 3.25H0.25V0.75H17.75V3.25Z" fill="#057092" />
      </svg>
    </div>
  );

  const Collapse = (props) => {
    const { getCollapseProps, getToggleProps, isExpanded } = useCollapse();

    const [lessons, setLessons] = useState([props.faq_answer]);
    return (
      <>
        <div>
          <h5
            className={`${classes.faqQuestion} d-flex justify-content-between`}
            {...getToggleProps()}
          >
            {props.faq_question}
            {isExpanded ? E : C}
          </h5>
          <div {...getCollapseProps()}>
            <div
              className={`${classes.faqAnswer} px-3 py-2`}
              dangerouslySetInnerHTML={{
                __html: props.faq_answer,
              }}
            ></div>
          </div>
          <hr className={classes.horizontalLine}></hr>
        </div>
      </>
    );
  };
  return (
    <>
      <div className={`${classes.fold1bg}`}>
      <div className={`${classes.hideCrumb} mx-auto pt-lg-5 px-3 px-lg-0`}>
               <div className={`d-flex ${classes.breadCrumb} px-1`}>
                 <Link style={{ textDecoration: 'none' }} href={`/${qpms}`}><p className="text-white m-0">ISB Online</p></Link>
                 <svg
                    style={{fill:"#ffffff"}}
                    width="24"
                    height="18"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_3275_9282)">
                      <path
                        d="M16.01 11H4V13H16.01V16L20 12L16.01 8V11Z"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_3275_9282">
                        <rect width="24" height="24" fill="#057092" />
                      </clipPath>
                    </defs>
                  </svg>
                  <p className={`text-white m-0 ${classes.breadCrumblast}`}>Frequently Asked Questions</p>
               </div> 
      </div>
        <div
          className={`py-lg-5 py-md-4 px-lg-1 px-md-3 px-sm-3 px-4  py-4 ${classes.equalPadding} mx-auto`}
        >
          <div className="text-white">
            <h1>
              <b>{faqs.attributes.Faq_title}</b>
            </h1>
          </div>
          <p className={`py-0 ${classes.p_text_lg}`}>
            {faqs.attributes.faq_description}
          </p>
        </div>
      </div>
      <div className={`mx-auto ${classes.equalPadding} text-center mt-5`}>
        {faqs.attributes.FAQ.map((faqData) => {
          return (
            <Link href={`#${faqData.id}`} scroll={false} key={faqData.id}>
              <button
                type="button"
                className={`${classes.viewbtn} btn btn-primary my-3 ${classes.faqSecWrap}`}
              >
                {faqData.faq_section}
              </button>
            </Link>
          );
        })}

        {faqs.attributes.FAQ.map((faqData) => {
          return (
            <div key={faqData.id}>
              <div className="pt-2" id={faqData.id} style={{color:"transparent"}}>.</div>
              <h4 className={`text-center ${classes.faqSection} pt-3 pb-2`}>{faqData.faq_section}</h4>
              <div className={`${classes.whiteBackground} px-lg-5 py-4 px-3`}>
                {faqData.faq.map((item) => {
                  return <Collapse key={item.id} {...item} />;
                })}
              </div>
            </div>
          );
        })}
      </div>
      <BottomFold data = {props.bottomFoldData}></BottomFold>
    </>
  );
}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;

  const [response,bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/frequenty-asked-question?populate[FAQ][populate]=*&populate[meta_tags][populate]=*`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      faqData: response,
      apiUrl: APIUrl,
      bottomFoldData:bottomFoldData
    },
    revalidate:240
  };
}

export default FAQPage;
