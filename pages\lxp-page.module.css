.breadCrumb p{
  font-size: 12px;
  color: white;
}

.breadCrumblast{
  font-size: 12px;
  color: white;
  text-decoration: none !important;
}

.breadCrumb p:hover {
  font-size: 12px;
  color: white;
  text-decoration: underline;
}

.hideCrumb{
  max-width: 1000px;
}

.fold1bg{
    background-image: url('../assets/TealBackground.jpg');
    background-size: cover;
    background-color: #057092
  }

  .equalPadding{
    max-width: 1000px;
  }

  .contentTitle{
    font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 27px;
  color: #057092;
  }

  .viewbtn
  {
      background-color: #057092 !important;
      border:none;
      padding: 10px 36px 10px 36px;
      font-weight: 600;
      flex-wrap: wrap;
      margin-right: 8px;
      margin-left: 8px;
      border-radius: 0px!important;
  }

  @media screen and (min-width:0px) and (max-width: 499px) {

    .hideCrumb{
      display: none;
    }
  }
  
  @media screen and (min-width:500px) and (max-width: 976px){
  
    .hideCrumb{
      display: none;
    }
  }
  
   
  .hidden{
    content-visibility: hidden;
  }
  .auto{
    content-visibility: auto;
  }