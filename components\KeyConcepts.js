export const KeyConcepts=({arrayData, title, divbsclasses, headingClass,ulclass,liclass})=>{
    return(
      <div className={`${divbsclasses} `}  >
      <h2 className={headingClass}>
        {title}
      </h2>
      <ul className={ulclass}>
        {arrayData.concepts_desc.map(i => (
          <li key={i.id} className={liclass}>
            {i.concept_text}
          </li>
        ))}
      </ul>
  
      {/* <CustomList htmlString={learnTrackData.learning_track_for}/> */}
    </div>
    )
  }
