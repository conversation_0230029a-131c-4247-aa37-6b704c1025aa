import Image from 'next/image';
import Link from 'next/link';
import React from 'react'

export const InterestedSection = ({apiUrl, interestedSection, qpms, classes, trackFoldData, queQpms}) => {

  return (
    <div
    className={`justify-content-center ${classes.equalPadding} mx-auto py-5 px-lg-0 px-2 px-md-3`}
  >
    <h2 style={{ fontSize: "30px" }} className="text-center text-dark">
      <b>{trackFoldData.interested_courses_title}</b>
    </h2>
    <p className="text-center">
      {trackFoldData.interested_courses_description}
    </p>
    <div
      className={`row ${classes.equalPadding} mx-auto py-lg-4 py-md-4 py-sm-0 py-4 px-lg-0 px-md-3 px-sm-3 px-3`}
    >
      {interestedSection.map((item, i) => {
        return (
          <div
            key={item.id}
            className={`${"px-lg-1"
              } px-0 col-lg-4 mb-0 d-flex align-items-stretch col-md-6 col-sm-12 my-lg-0 my-md-3 my-3`}
          >
            <div className={`card ${classes.boxshadow} mb-0 mb-md-2`}>
              <Image
                width="0"
                height="0"
                sizes="100vw"
                src={
                  apiUrl +
                 ( item.attributes.learning_track_short_image.data.attributes
                    .formats?.small?.url || item.attributes.learning_track_short_image.data.attributes
                    .url)
                }
                className={`card-img-top ${classes.image_border} w-100 h-auto`}
                alt={
                  item.attributes.learning_track_short_image.data.attributes
                    .alternativeText
                }
              />
              <div
                className={`card-body d-flex flex-column ${classes.buttonPos}`}
              >
                <h5 className={`card-title ${classes.learnTrackTitle}`}>
                  {item.attributes.learning_track_name}
                </h5>
                <p className={`pb-5 card-text`}>
                  {item.attributes.learning_track_short_description}
                </p>
               { <div className={`mb-3 ${classes.buttonPosition}`}>
                  <Link
                    rel="canonical"
                    href={`${item.attributes.learning_track_id}${qpms != `` ? queQpms : ``
                      }`}
                    className={`btn btn-primary mt-auto align-self-start ${classes.viewbtn}`}
                  >
                    Learn More
                  </Link>
                </div>}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  </div>
  )
}
