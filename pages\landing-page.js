import Image from "next/image";
import classes from "./landing-page.module.css";
import React, { useState } from 'react';
import landingPageImage from "../assets/lp_image.svg";
import Router from "next/router";
import { useRouter } from "next/router";
import BottomFold from "../components/bottom_fold_one";

export default function LandingPage(props) {
  const learningTracks = props.apiData.data;
  const landingPage = props.landingPage.data.attributes;
  const { query } = useRouter();
  const [agree, setAgree] = useState(true);

 
  // When the button is clicked
  const btnHandler = () => {
    alert('The buttion is clickable!');
  };

  const submitContact = async (event) => {
    event.preventDefault();
    const json = {
        first_name: `${event.target.name.value}`,
        email: `${event.target.email.value}`,
        mobile: `${event.target.number.value}`,
        tags:`utm_source=${query.utm_source}&utm_medium=${query.utm_medium}&utm_campaign=${query.utm_campaign}&utm_term=${query.utm_term}&utm_content=${query.utm_content }`
    };
    const response = await fetch(
      `https://isbdev2.quantana.top/backend/sfdc_leadAPI`,
      {
        body: JSON.stringify(json),
        headers: {
          "Content-Type": "application/json",
        },
        method: "POST",
      }
    );
    if (response.status == 200) {
      Router.push("/thankyou");
    }
  };

  function onChange() {
    alert(`So your name is `);
  }

  return (
    <>
      <div className={`${classes.fold1bg}`}>
        <div className={`row py-lg-5 p-0 ${classes.equalPadding} mx-auto `}>
        <div className={`${classes.secstart} ${classes.mobileFoldbg}  col-lg-8 col-md-12 py-lg-0 py-md-4 py-sm-4 py-3 px-0 d-flex flex-column justify-content-center`}>
            <div className="px-lg-0 px-md-3 px-sm-3 px-3">
              <h1 className={`text-white pt-lg-0 pt-lg-4 pt-0 ${classes.title}`}>
                <b>{landingPage.title}</b>
              </h1>
              <p className={`my-lg-4 my-0 text-white pe-lg-3 ${classes.paragraph}`}>
                {landingPage.description}
              </p>
            </div>
            <div></div>
          </div>
        <div className="col-lg-4 col-md-12 p-4 bg-white">
            <div>
              <p className={`p-0 m-0 ${classes.blueBoldText}`}>
               {landingPage.lead_form_title}
              </p>
              <p style={{ fontSize: "15px" }} className="py-2 m-0">
               {landingPage.lead_form_description}
              </p>
              <form onSubmit={submitContact} className="my-0">
                <div className="form-group col-12">
                  {/* <label
                    className={`control-label ${classes.formLabelText}`}
                    for="name"
                  >
                    Name
                  </label> */}
                  <input
                    className="form-control"
                    type="text"
                    id="name"
                    name="name"
                    placeholder="Name"
                    required
                  />
                </div>
                <div className="form-group col-12 py-3">
                  {/* <label
                    className={`control-label ${classes.formLabelText}`}
                    for="email"
                  >
                    Email
                  </label> */}
                  <input
                    className="form-control"
                    type="text"
                    id="email"
                    name="email"
                    required
                    placeholder="Email"
                    pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                    title="Please enter valid email address"
                  />
                </div>
                <div className="form-group col-12">
                  {/* <label
                    className={`control-label ${classes.formLabelText}`}
                    for="number"
                  >
                    Phone Number
                  </label> */}
                  <input
                    className="form-control"
                    type="text"
                    id="number"
                    name="number"
                    placeholder="Phone Number"
                    required
                    pattern="[6789][0-9]{9}"
                    title="Please enter valid phone number"
                    onKeyPress={(event) => {
                      if (!/[0-9]/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                  />
                </div>
                  <div className={`mt-3 ${classes.disablePointer}`}>
                  <label
                  style={{fontSize:"13px"}}
                  for="agree">
                    {landingPage.lead_form_terms_content}
                  </label>
                  </div>
                  
                <div className="text-center">
                  <button disabled={!agree} className={`${classes.viewbtn} mt-3`} type="submit">
                  {landingPage.lead_form_btn_title}
                  </button>
                </div>
              </form>
            </div>
          </div>
          
          
        </div>
      </div>
      <div className="bg-white">
        <div className={`${classes.equalPadding} mx-auto`}>
          <p className={`text-center ${classes.blackHeading} m-0 pt-4 pb-2`}>
            {landingPage.isb_online_title}
          </p>
          {landingPage.isb_online.map((item, i) => {
            return (
              <div key={i} className="row py-2">
                <Image
                  width="0"
                  height="0"
                  sizes="100vw"
                  className="col-lg-1 col-md-2 col-3 p-0 my-auto img-fluid align-center mx-auto"
                  src={`${props.apiUrl}${item.io_image.data.attributes.url}`}
                  alt={item.io_image.data.attributes.alternativeText}
                />

                <div className=" col-lg-11 col-12 pe-2 text-left py-lg-0 py-3">
                  <div className={`${classes.lightBlueBorder}`}>
                    <p className={`px-3 pt-3 pb-2 m-0 ${classes.blueBoldText}`}>
                      {item.title}
                    </p>
                    <p className={`px-3 pb-3 m-0`}>{item.description}</p>
                  </div>
                </div>
              </div>
            );
          })}
          <div className={`p-0 pt-3`}>
            <p className={`text-center ${classes.blackHeading} m-0 pt-3`}>
              {landingPage.lt_title}
            </p>
            <p className={`text-center my-0 px-lg-0 px-md-3 px-sm-3 px-3 pt-2`}>
              {landingPage.lt_description}
            </p>
          </div>
          <div
            className={`row  py-lg-4 py-md-4 py-sm-0 py-4 px-lg-0 px-md-3 px-sm-3 px-3`}
          >
            {learningTracks.map((item, i) => {
              return (
                <div
                  key={item.id}
                  className={`${
                    i === 0 || i === 2 ? "px-lg-2" : "px-lg-1"
                  } px-1 col-lg-3 mb-0 d-flex align-items-stretch col-md-6 col-sm-12 my-lg-0 my-md-3 my-3`}
                >
                  <div className={`card ${classes.boxshadow}`}>
                    <Image
                      width="0"
                      height="0"
                      sizes="100vw"
                      src={
                        props.apiUrl +
                        item.attributes.learning_track_short_image.data
                          .attributes.formats.small.url
                      }
                      className={`w-100 h-auto card-img-top ${classes.image_border}`}
                      alt={item.attributes.learning_track_short_image.data
                        .attributes.alternativeText}
                    />
                    <div
                      className={`card-body d-flex flex-column ${classes.buttonPos}`}
                    >
                      <h5 className={`card-title ${classes.learnTrackTitle}`}>
                        {item.attributes.learning_track_name}
                      </h5>
                      <p className={`pb-2 card-text`}>
                        {item.attributes.learning_track_short_description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <div className={`${classes.execBlock} py-3`}>
          <div className={`${classes.equalPadding} mx-auto`}>
            <p className={`text-center ${classes.whiteHeading} pt-3 m-0`}>
              {landingPage.isb_exec_title}
            </p>
            <div className="row py-3 px-lg-0 px-3">
              {landingPage.exec_education.map((item, i) => {
                return (
                  <div
                    key={i}
                    className="col-lg-4 col-md-6 col-sm-12 col-12 m-0"
                  >
                    <div className="bg-white mt-2">
                      <p
                        className={`${classes.blackHeading} text-center m-0 py-2`}
                      >
                        {item.title_head}
                      </p>
                    </div>
                    <p className={`m-0 p-0 text-center text-white`}>
                      {item.keyword}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <div className={`bg-white pb-5`}>
          <div
            className={`${classes.equalPadding} mx-auto ${classes.lightBlueBg} mt-4 px-3 py-3`}
          >
            <div className="row flex-row-reverse">
              <Image className="col-lg-4 col-md-12" src={landingPageImage} alt="landing_page_background"/>
              <div className="col-lg-8 col-md-12">
                <p className={`${classes.blackHeading} m-0 px-3 pt-3`}>
                  {landingPage.lp_btm_fold_title}
                </p>
                <div
                  className="py-3"
                  dangerouslySetInnerHTML={{
                    __html: landingPage.isb_btm_fold_description,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BottomFold data = {props.bottomFoldData}></BottomFold>
    </>
  );
}

export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;

  const [learningTracks, landingPageResponse,bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/learning-tracks?populate[learning_track_short_image][populate]=*`
    ).then((r) => r.json()),
    fetch(
      `${APIUrl}/api/landing-page?populate[isb_online][populate]=*&populate[exec_education][populate]=*`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      apiData: learningTracks,
      landingPage: landingPageResponse,
      apiUrl: APIUrl,
      bottomFoldData:bottomFoldData
    },
    revalidate:240
  };
}
