import { useState, useEffect } from 'react';
import dynamic from "next/dynamic";
import classes from '../pages/v2/[learning_track_id]/index.module.css';

const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });
const VideoPopup = ({ videolength, video_desc, videoSrc }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [playerKey, setPlayerKey] = useState(0);
  const [closeFlag, setCloseFlag] = useState(false);

  useEffect(() => {
    setIsClient(true);

    setTimeout(() => {
      setCloseFlag(true)
    }, 1000);
  }, [closeFlag]);
  useEffect(() => {
    document.addEventListener('keydown', function(e) {
      if(e.key=='Escape'||e.key=='Esc'|| e.key==27){
        setIsOpen(false)
        setPlayerKey(prevKey => prevKey + 1);
        setCloseFlag(false)
      }
  });
   
  }, [])
  
  const handleModalOpen= async ()=>{
    setIsOpen(true);
    setCloseFlag(false)

  }

  const handlemodalClose=()=>{
    setIsOpen(false);
    setPlayerKey(prevKey => prevKey + 1);
    setCloseFlag(false)
  }
 if(!isClient){
  return null
 }
  return (
    <div className={classes.videopopup_main}>
     <div className={classes.popupvideo_sub} >
       <ReactPlayer
          key={playerKey}
          url={!isOpen ? videoSrc : ""}
          playing={false}
          width={"155px"}
          height={"86px"}
          light={true}
          onClickPreview={() =>handleModalOpen( )}
          style={{ cursor: 'pointer', backgroundColor:"black", }}
        />
     </div>
      {videoSrc && <p>{video_desc}</p>}

      <div className={classes.container}>
        <div className={classes.video_container}>
          <div className={`${classes.popup_video} ${isOpen ? 'd-block' : 'd-none'}`}>
            <iframe
            allowfullscreen
              src={!isOpen ? '' : `${videoSrc}&autoplay=1`}
              allow="autoplay; fullscreen; picture-in-picture;"
              style={{backgroundColor:"black"}}
              priority={true}
            />
           { closeFlag && <span onClick={() => handlemodalClose()}>&times;</span>}
          </div>
        </div>
      </div>

   
   
   
    </div>
  );
};

export default VideoPopup;


 
 