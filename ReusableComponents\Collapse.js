import React, { useState } from "react";
import useCollapse from "react-collapsed";

const collapseStyle = {
  moduleTitle: {
    color: '#057092',
    textDecoration: 'none',
    alignItems: 'center',
    verticalAlign: 'center',
    fontSize: '17.5px',
    fontWeight: '600',
    margin: '0'
  },
  content: {
    padding:'25px',
    backgroundColor: '#F9F9F9',
    marginTop: '10px',
    alignItems:'center',
  },
  lessonContent: {
    color: '#7C8495',
    textDecoration: 'none',
    alignItems: 'center',
    margin: '0px',
    verticalAlign: 'center',
  }
};

const C = (
  <div className="d-flex">
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19 8.5L12 15.5L5 8.5"
        stroke="#057092"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </div>
);

const E = (
  <div className="d-flex">
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 15.5L12 8.5L19 15.5"
        stroke="#057092"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </div>
);

export const Collapse = (props) => {
  const { getCollapseProps, getToggleProps, isExpanded } = useCollapse();
  const [lessons] = useState([props.attributes.lessons]);

  return (
    <div style={collapseStyle.content}>
      <h6
        style={{ ...collapseStyle.moduleTitle, display: 'flex', justifyContent: 'space-between' }}
        {...getToggleProps()}
      >
        {props.attributes.module_name}
        {isExpanded ? E : C}
      </h6>
      <div {...getCollapseProps()} className="pt-3">
        {lessons[0].map((item) => (
          <ul key={item.id} style={collapseStyle.lessonContent}>
            <li>{item.lesson_name}</li>
          </ul>
        ))}
      </div>
    </div>
  );
};
