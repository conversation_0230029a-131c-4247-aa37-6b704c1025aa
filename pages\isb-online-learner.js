import classes from "./blog.module.css";
import Image from "next/image";
import Link from "next/link";
import BottomFold from "../components/bottom_fold_one";
import girl from "../assets/girllaptopp Large.webp"
import illusion from "../assets/illusion.webp"
import mock from "../assets/Mock.webp"
import { NextSeo } from 'next-seo';





export default function blog(props) {

  return (

    
    <>
         <NextSeo
            title="Isb Online Learner"
            description="Day in the life of an ISB Online learner"
        />

      <div>
        <div div className={`${classes.fold1bg}`}>
          <div className={`row flex-row-reverse col-lg-12  mx-auto ${classes.equalPadding}`}>
            <Image
              width="0"
              height="0"
              sizes="100vw"
              className="img-fluid col-lg-6 col-md-12 p-0 py-lg-4"
              src={girl}
            alt="girlLaptopLarge"
            />

            <div className="col-lg-6 px-lg-0 px-3 py-lg-0 py-4 col-md-12 align-items-center d-flex justify-content-center pe-lg-4">
              <div>
                <h1 className={classes.heading}>
                  Insights
                </h1>
                <h2 className={`my-3 ${classes.description}`}>Day in the life of an ISB Online learner</h2>
              </div>
            </div>
          </div>

        </div>
        <div className={`row flex-row-reverse col-lg-12 mt-4 mx-auto ${classes.equalPadding} py-0 pb-lg-1 py-md-0`}>
          <p>
          The Indian School of Business (ISB) is a world-class institution that is at the forefront of business education. Through ISB Online, the school intends to provide the same high-quality online education to global learners. The platform offers a unique and immersive learning experience combining learning science with the needs of the modern-day learner.         
          </p>
          <p>
            <b>What would a day in the life of an ISB Online learner look like?</b><br /> As soon as learners log in to the platform, the learning dashboard details where they are in their learning journey and provides a personalised, graphic representation of how much time learners have spent on their learning journey over weeks. The dashboard provides a holistic view motivating learners to plan time better. 
          </p>

          <p>
          Learners can access various resources, tools, and features in each course to enhance their learning experience. However, the <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}/lxp-page`}>distinct learning experience</Link> revolves around <b>Reflection, Experience, Application & Learning (REAL). </b> 
          </p>
          

        </div>

        <div className={`row flex-row-reverse col-lg-12 m-0 mx-auto ${classes.equalPadding} py-4`}>
          <div className="col-lg-6 px-lg-0 px-3 col-md-12 d-flex justify-content-center pe-lg-4">
            <p>
            <b>Reflection:</b> In the preparatory phase of learning, learners are encouraged through reflective questions to prime themselves for the learning content that follows. This allows them to understand the context and focus of the content that follows. 
            <br/><br/><b>Experience: </b>The experience of learning from renowned ISB faculty consists of short, byte-sized, micro-learning videos where the faculty uses examples, stories, and research data to build on knowledge and skills for the learner. 
            </p>
          </div>
          <Image
            width="0"
            height="0"
            sizes="100vw"
            className="img-fluid col-lg-6 col-md-12 pt-lg-0 pt-3"
            src={illusion}
          alt="illusion"
          />

        </div>

        <div className={`row flex-row-reverse col-lg-12 mx-auto ${classes.equalPadding} py-0 pb-lg-1 py-md-0`}>
            <p>
            <b>Application:</b> What is learning without enough opportunities to recall or apply? Some videos are followed with reflective prompts that will help learners fortify learning. These are done through quotes, visuals, and critical insights. Further, knowledge check questions help learners immediately apply a concept they have learned. They collaborate with peers and engage in collaborative discussion, problem-solving and decision-making through social learning activities. 
            </p>
        </div>

        <div className={`row flex-row-reverse col-lg-12 m-0 mx-auto ${classes.equalPadding} py-4`}>
          <Image
            width="0"
            height="0"
            sizes="100vw"
            className="img-fluid col-lg-6 col-md-12"
            src={mock}
          alt="mock"
          />
          <div className="col-lg-6 px-lg-2 px-3 py-lg-0  col-md-12  d-flex justify-content-center pe-lg-4">
          <p className="mt-5">
            <b>Learning:</b> At varied levels, therefore through reflection, experiencing learning, followed up with reinforcement and application, each learner is provided with a flexible path to achieving the learning outcomes for each course.  
            </p>
          </div>

        </div>


        <div className={`col-lg-12 mx-auto ${classes.equalPadding}`}>
          <p className="px-lg-0 px-3 py-4">
          ISB Online offers learners academic advising and technical support services anytime during their journey. Programme managers help learners navigate the <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}/learning-tracks`}>course content</Link> and provide guidance on educational planning while timely technical support assists learners with any technical issues that may arise during the course.          
          </p>
          <p className="px-lg-0 px-3 py-4">
           With its <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}/about-page`}>world-class faculty, cutting-edge curriculum, and personalized approach</Link> to learning, ISB Online makes every day a learning day!           
          </p>
        </div>

        <div className="text-center">
          <Link rel="canonical" href="/learning-tracks">
            <button className={`${classes.showBtn} mb-5`} type="submit">
              Exploring Learning Tracks
            </button>
          </Link>
        </div>

        <BottomFold data={props.bottomFoldData}></BottomFold>


      </div>


    </>
  );
}
export async function getStaticProps(context) {
  const { req, query, res, asPath, pathname, params } = context;
  const APIUrl = process.env.API_BASE_URL;
  const [tqData, bottomFoldData] = await Promise.all([
    fetch(
      `${APIUrl}/api/thankyou-ref`
    ).then((r) => r.json()),
    fetch(`${APIUrl}/api/bottom-fold?populate=*`).then((r) => r.json()),
  ]);
  return {
    props: {
      tqData: tqData,
      apiUrl: APIUrl,
      bottomFoldData: bottomFoldData
    },
  };
}