
.breadCrumb p{
  font-size: 12px;
  color: white;
}

.breadCrumblast{
  font-size: 12px;
  color: white;
  text-decoration: none !important;
}

.breadCrumb p:hover {
  font-size: 12px;
  color: white;
  text-decoration: underline;
}

.hideCrumb{
  max-width: 1000px;
}

.p_text{
    font-weight: 4 00;
    font-size: 14px;
    line-height: 18px;
    color:#7C8495;
}


.mainHeading{
font-size: 26px;
font-weight: 700;
line-height: 39px;
letter-spacing: 0em;
text-align: left;
}

.paragraphSize{
  font-size: 14px;
}

.line {
  border-top:1px solid rgb(187, 185, 185);
  margin: px;
}

.p_text_lg{
    font-size: 18px;
    font-weight: 400;
    line-height: 32.2px;
    color:white; 
}
.equalPadding{
  max-width: 1000px;
}
.lrng_trck_txt{
    margin-top: 1.5rem;
}

.buttonPos{
  position: relative;
}

.buttonPosition{
  bottom: 0px;
  position: absolute;
}

.card_title{
  font-style: normal;
  font-weight: 700;
  font-size: 17px;
  line-height: 27px;
  text-align: center;
  
  color: #000000;
}

.taxestext
{
	font-weight: 650;
    font-size: 16px;
    margin-bottom:0px
}

.image_border{
  border-radius: 0%;
  min-height: 132px;
  }

  .boxshadow{
    border: none;
    border-radius: 0%;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12); 
  }

  .fold1bg{
    background-image: url('../assets/TealBackground.jpg');
    background-size: cover;
    background-color: #057092;
  }

  .extraSpace{
    padding-top: 32px;
    
  }

  .fold1FontWhite{
    color: white!important;
  }

.hrstext
{
	margin-bottom: 0px;
    font-size: 14px;
}
  .blackHeading{
    font-weight: 700;
    font-size: 36px;
    line-height: 54.47px;
    color: #000;
  }
  .learningtracktext
{
	  font-size: 14px;
    text-align: center;
    color: #7C8495;
}
.boxshadow{
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12); 
  }
  .card_body_cal{
    min-height:120px
  }
 .header_h1_title{
  color:#057092;
  font-size:40px;
  font-weight:700;

 }
  .learnTrackTitle{
    color: black;
    font-size: 18px;
    font-weight: 700;
  }
  .learningtracktext
{
	  font-size: 14px;
    text-align: center;
    color: #7C8495;
}

.viewbtn
{
	background-color: #057092 !important;
    padding: 6px 20px;
    color:#FFFFFF;
border:none;
    border-radius: 0px!important;
}
  
@media screen and (min-width: 1700px){
  .card_response {
    padding:40px;
  } 
  .blackHeading{
    font-size:25px
  }
  .blueHeading{
   font-size:24px;
  }
 .padset{
  padding:30px;

 }

}

@media screen and (min-width:0px) and (max-width: 499px) {

  .hideCrumb{
    display: none;
  }
}

@media screen and (min-width:500px) and (max-width: 976px){

  .hideCrumb{
    display: none;
  }
}

.learnTrackTitle_comprises_title{
  color: black;
  font-size: 18px;
  font-weight: 600;
}