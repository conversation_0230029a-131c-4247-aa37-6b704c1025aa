.contactUsCard{
    font-size: 16px;
    color: #fff !important;
  text-decoration: none;
  text-align: right;
  }

.emailLink {
  color: #0d89de;
  text-decoration: none;
}

.emailLink:hover {
  text-decoration: underline;
}

.financeTable {
  font-family: var(--isb-edge-font-family-inter-variable);
}
.finacing_link Link{
    font-size: 1em;
}
.container-ee {
    width: 100%;
    max-width: 855pt;
    margin: 0 auto;
    position: relative;
}
.header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 0;
    width: 100%;
    height: 130px;
    background-color: #fff;
    font-family: Poppins, sans-serif;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
.header__logo_box_wrapper {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 42rem;
    flex: 0 1 42rem;
    height: 100%;
    -webkit-filter: drop-shadow(0 1rem 2.4rem rgba(0, 0, 0, .1));
    filter: drop-shadow(0 1rem 2.4rem rgba(0, 0, 0, .1));
    padding-left: 7rem;
    
}
.header__content_wrapper {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 fit-content;
    flex: 1 1 fit-content;
}
.header__logo-box {
    height: 100%;
    -webkit-clip-path: path(nonzero, "\a M -1500 0 C -150 0 486.116 0 414.745 0 C 343.373 0 360.138 126.502 288.22 130 C 216.301 133.498 -150 130 -1500 130 V 0 Z");
    clip-path: path(nonzero, "\a M -1500 0 C -150 0 486.116 0 414.745 0 C 343.373 0 360.138 126.502 288.22 130 C 216.301 133.498 -150 130 -1500 130 V 0 Z");
    background-color: #fff;
    position: relative;
}
.header__logo_box::before {
    content: "";
    display: inline-block;
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    background-color: #fff;
    right: 100%;
}
.header__logo_box {
    height: 100%;
    -webkit-clip-path: path(nonzero, "\a M -1500 0 C -150 0 486.116 0 414.745 0 C 343.373 0 360.138 126.502 288.22 130 C 216.301 133.498 -150 130 -1500 130 V 0 Z");
    clip-path: path(nonzero, "\a M -1500 0 C -150 0 486.116 0 414.745 0 C 343.373 0 360.138 126.502 288.22 130 C 216.301 133.498 -150 130 -1500 130 V 0 Z");
    background-color: #fff;
    position: relative;
    align-content:center;
}


/* ------------------ */

.bc_and_menu {
    height: 3rem;
    background-color: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    gap: 0;
    font-family: Poppins, sans-serif;
}
.bc_and_menu__breadcrumb {
    -webkit-box-flex: 2;
    -ms-flex: 2;
    flex: 2;
    -ms-flex-item-align: center;
    align-self: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: .5rem;
}
.bc_and_menu__menu {
    height: 41px;
    padding-left: 4.9rem;
    padding-right: 7.5rem;
    background-color: #057092;
    -webkit-clip-path: path(nonzero, "M 18.6792 9.74089 C 22.2894 3.69931 28.8095 0 35.8475 0 H 3990 V 41 H 0 L 18.6792 9.74089 Z");
    clip-path: path(nonzero, "M 18.6792 9.74089 C 22.2894 3.69931 28.8095 0 35.8475 0 H 3990 V 41 H 0 L 18.6792 9.74089 Z");
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 33px;
    position: relative;
    overflow: visible;
}
.bc_and_menu__menu span{
    text-decoration: none;
    align-items: baseline;
    font-size: 17px;
    font-weight: 400;
}
.bc_and_menu__menu_item {
    color: white;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 1.3rem;
}
 


.hero_banner__slide_container__small {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    gap: 0;
}
.hero_banner__slide_container {
    width: 100%;
    position: relative;
}
.hero_banner__content_wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.container_eee {
    width: 100%;
    height:100%;
    max-width: 855pt;
    margin: 0 auto;
    position: relative;
}
.hero_banner__content {
    padding: 0;
    max-width: 70%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.hero_banner__content h3 {
    font-size: 2.8rem;
    line-height:4.6rem;
    margin-bottom: 0px;
    margin-top: 0px;
}
.hero_banner__img {
    height: 100%;
    width: 99.6vw;
    object-fit: cover;
}
.hero_banner__title{
    position:absolute;
    top:28%;
    left:10%

}
.contentext{
    max-width: var(--isb-container-max-width);
}
.contentext p {
    font-size: 0.999rem;
    font-weight: 400;
    line-height: 2.2rem;
    color: #1a3563;
    max-width:var(--isb-container-max-width);
}
.contentext > .contentext_child{
    max-width:var(--isb-container-max-width);   
    
}
.contentext_child p{
    color: #585865;
    text-decoration: none;
    align-items: baseline;
    font-size: 17px;
    vertical-align: center;
    font-weight: 400;
}
.footer_content_container {
    background-color: #057092;
    color:white;
    min-height:var(--isb-child-banner-minheight);
}
.footer_logo_container{
    color:black;
    min-height:var(--isb-child-banner-minheight);
    background-color: #fff;
}
.footer_content_container p{
    color:white;
}
.footer_content_container hr{
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 0px;
    margin-bottom: 0;
}
.pdf{
    width:100%;
    aspect-ratio: 4 / 3;
    min-height: 85vh !important;
  }
  .iframe_main{
    padding: 10px 10px 0px 0px;
    width: 100%;
    display:flex;

  }
  .associate_main h4{
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 22px;
    margin-top: 0;
  }
  .associate_ul li{
    padding: 0;
    margin-right: 40px;
  }
  .associate_ul{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    gap: 0;
    list-style: none;
    padding-left: 0;
    align-items: center;
  }
  .footer_logo_wrpr_main{
align-items:center;
display:flex;
  }

  .footer_logo_wrpr a{
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 0;
    max-width: 100%;
    padding-left: 1.2rem;
    padding-right: 1.2rem;
    list-style: none;
  }
  .footer_logo_wrpr svg{
    height:45px;
    width:45px;
  } 
  .footer_logo_wrpr{
    padding-left: 0;
    list-style: none;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 0;
    margin-bottom:0px;
  }
  .logo_wrapper_main{
    max-width:1000px;
    padding-top: 35px;
    padding-bottom: 35px;
  }
  .financing_banner{
    background-color:var(--isb-blue-color);
    min-height:14rem;
    width:100%;
  }
.financing_banner > .sub_banner_main{
width:100%;
margin-left:auto;
margin-right:auto;
height:100%;
max-width:var(--isb-container-max-width);
}
 .sub_banner_main h3{
    color:white;
    font-size:2rem;
    text-align:left;
    align-items:center;
 }

 .pdf_viewer_main iframe{
    min-height:450px !important;  
    display:flex;
    min-width: 300px;

  }
.sdk-Branding-hudBranding{
display:none !important;
}
@media screen and (max-width:768px){
 
    .financing_banner{
        min-height:8rem;
    }
    .header__logo_box_wrapper{
        padding-left:3rem;
    }
    .header{
        height:113px;
    }
    .footer_logo_wrpr{
justify-content:space-between;
width:100%;
    }
    .footer_logo_wrpr a{
        padding-left: 0.8rem;
        padding-right:0.8rem;
        justify-content:space-between;
    }
    .footer_logo_wrpr_main{
        justify-content:space-evenly;
    }
    .financing_banner > .sub_banner_main{
        display:flex;
        justify-content:center;
    }
}

