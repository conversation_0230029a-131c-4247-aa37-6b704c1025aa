.popupOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popupContent {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.contentPadding {
  padding: 2rem;
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #192890;
  z-index: 3;
}

.header {
  margin-bottom: 2rem;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header h2 {
  color: #192890;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.header p {
  color: #192890;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formField {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  position: relative;
}

.formField input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #E2E8F0;
  border-radius: 4px;
  font-size: 1rem;
}

.interestsSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.interestsGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.interestButton {
  padding: 0.5rem 1rem;
  border: 1px solid #E2E8F0;
  border-radius: 20px;
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.interestButton.active {
  background: #192890;
  color: white !important;
  border-color: #192890;
}

.interestsSelect {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #E2E8F0;
  border-radius: 4px;
  font-size: 1rem;
  color: #192890;
  background-color: white;
  min-height: 120px;
}

.interestsSelect option {
  padding: 8px;
  cursor: pointer;
}

.interestsSelect option:checked {
  background: #192890;
  color: white;
}

.selectHint {
  display: block;
  color: #666;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.footerSection {
  position: relative;
  margin-top: 2rem;
  width: 100%;
}

.illustration {
  width: 100%;
  position: relative;
  height: 120px;
}

.campusImage {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.submitButton {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  color: #002D72;
  background: white;
  padding: 0.75rem 2rem;
  border: 2px solid #002D72;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s ease;
}

.submitButton:hover {
  background: #002D72;
  color: white;
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.multiSelect {
  width: 100%;
  height: 45px;
  border: 1px solid #E2E8F0;
  border-radius: 4px;
  padding: 0.75rem 1rem;
  background: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selectedText {
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  font-size: 14px;
}

.dropdownArrow {
  color: #192890;
  font-size: 12px;
  margin-left: 0.5rem;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #E2E8F0;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 4px;
  flex-direction: column;
}

.dropdownItem {
  padding: 0.75rem 1rem;
  cursor: pointer;
  color: #192890;
  font-size: 14px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
}

.dropdownItem:hover {
  background-color: #F5F5F5;
}

.dropdownItem.selected {
  background-color: #F5F5F5;
}

.dropdownItem span {
  user-select: none;
}

.checkbox {
  margin: 0;
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #192890;
}

.dropdown::-webkit-scrollbar {
  width: 8px;
}

.dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.dropdown::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.dropdown::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.selectedItems {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.placeholder {
  color: #666;
}

.selectedTags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: #192890;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 16px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.removeTag {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.removeTag:hover {
  opacity: 0.8;
}
