.freelesson_btn{
  background-color: #057092;
  color: white;
  border: none;
  padding: 10px;
  margin-right: 10px;
}


.breadCrumb p{
  font-size: 12px;
  color: white;
}


.imageWrapper {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.playIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.breadCrumb p:hover{
  font-size: 12px;
  color: white;
  text-decoration: underline;
}

.breadCrumblast{
  font-size: 12px;
  color: white;
  text-decoration: none !important;
}

.disclaimerText{
  font-size: 12px;
}

.hideCrumb{
  max-width: 1000px;
}

.para{
    color: black !important;
    text-decoration: none;
    align-items: baseline;
    vertical-align: center;
    margin: 0;
}

.profRow{
  justify-content: center;
  align-items: center
}

.roundedCircle{
  border-radius:2%;
  height: 147px;
  width: 147px;
  object-fit: cover;
  }

  .alignLeft{
    text-align: left;
    font-size: 16px;
    width: 100%;
  }

  .alignRight{
    text-align: end !important;
    color: #057092;
  }


.fontTwenty{
  font-style: normal;
font-weight: 700;
font-size: 20px;
line-height: 27px;
color: #000000;
}

.slide {
  height: 100%;
}

.buttonPos{
  position: relative;
}

.buttonPosition{
  bottom: 0px;
  position: absolute;
}


.image_border{
  border-radius: 0px;
  }

.boxshadow{
  border: none;
  border-radius: 0%;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.12); 
}

.courseCardHeight{
  min-height: 482px;
}

.grey_text{
  color: #585865;
  text-decoration: none;
  font-size: 14px;
  align-items: baseline;
  vertical-align: center;
  font-weight:400;
}

.container {
  position: relative;
  text-align: center;
  color: white;
  font-weight: 700;
}

.centered {
  position: absolute;
  top: 100%;
  font-size: 14px;
  max-height: 135px;
  transform: translate(-0%, -100%);
  padding: 6px;
  background-color: rgba(29, 28, 28, 0.603);
}


.fontEighteenBlue{
  font-style: normal;
font-weight: 700;
font-size: 18px;
line-height: 27px;
color: #057092;
}

.courseDescription{
  font-size: 17px;
  vertical-align: center;
  font-weight: 400 !important;
  line-height: normal;
 
}

.sidebar{
  position: sticky !important;
  top: 58px !important;
  height: fit-content;
}

.btnprimary{
  background-color: #057092;
  border-radius: 0px;
}

.blueCardBackground{
  background-color: #F9F9F9;
}

.fold1btn
{
  min-height: 38px;
  background-color: #fff!important;
  border: 2px solid #fff!important;
  color: #2E2661!important;
  font-weight: 600;
  border-radius: 0px!important;
  fill:  #2E2661!important;
}

.fold1btn:hover{
  background-color: transparent!important;
  border: 3px solid #fff!important;
  color: #fff!important;
  font-weight: 600;
  border-radius: 0px!important;
  fill: white !important;
}


.viewbtn
{
	background-color: #057092 !important;
    padding: 6px 20px;
    color:#FFFFFF;
border:none;
    border-radius: 0px!important;
}

.viewbtn:hover
{
	background-color: #057092 !important;
    padding: 6px 20px;
    font-weight: 600;
    color:#FFFFFF;
    border:none;
    border-radius: 0px!important;
}

.custm_font{
  color:#057092;
  font-weight: 600;
}
.resp_btn_caursl{
  display:flex;
  justify-content: space-between;
margin-top: 20px;
}
.thmbnil_hyt{
  min-height:80px;
  text-align: center;
  flex-direction: column;
}
.sizeten{
  font-size: 10px;
  color:#708FAB;
}

.moduleTitle{
  color: #057092 !important;
  text-decoration: none;
  align-items: baseline;
  vertical-align: center;
  font-size: 17.5px;
  font-weight: 600;
  margin: 0;
}

.hideCourses{
  display: none;
}

.lessonContent{
  color: #7C8495 !important;
  text-decoration: none;
  align-items: baseline;
  margin: 0px;
  vertical-align: center;
}


.header {
    width: 100%;
    height: 5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #FFFFFF;
    padding: 0 10%;
    position: sticky;
  }
  
  .fontwhite{
    color: white !important;
  }
  
  .showApplication{
    display: none;
  }

  .navelems{
    right: 0;
    padding-right: 32px;
    padding-left: 32px;
  }

  .equalPadding{
    max-width: 1000px;
  }

  .content{
    padding: 25PX;
    background-color: #F9F9F9;
    margin-top: 10px;
  }
  
  .learnTrackTitle{
    color: #057092;
    font-size: 16px;
    font-weight: 700;
  }
  

  .fold1{
    background-image: url('../../../assets/darkblue_bg.webp');
    background-size: cover;
    background-color: #2E2661;
  }

  .sliderContainer{
    background-color: white;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05); 
  }

  .bottomFold{
    padding: 0;
    background-color: white;
    margin-bottom: 48px;
    margin-left: 48px;
    margin-right: 28px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
  }

  .carouselFold{
    padding: 0;
    margin-top: 48px;
    margin-left: 48px;
    margin-right: 28px;

  }
.custm_padding{
  padding:48px
}
.slide_btn{
  background-color: #057092 ;
  padding: 15px 20px;
  color:#FFFFFF;
  border:none;
  border-radius: 0px!important;
  margin: 0px 8px;
  display: flex;
  
}
.slide_btn:disabled{
opacity: 50%;
}
.download_syllabus_btn{
  background-color: #057092;
  color: white;
  height: 36px;
  padding: 0px 16px;
  border: none;
}
  .certif_txt{
margin-top:0px
  }
  @media screen and (max-width: 720px){
    .carouselFold{
      padding: 0;
      margin: 10px
  
    }
    .bottomFold{
      padding: 0;
      background-color: white;
      margin:10px;
      box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
    }
    .custm_padding{
      padding:30px
    }
    .custm_algin{
      margin:auto
    }
    .resp_btn_caursl{
      display:flex;
      justify-content: center;
    flex-direction: column-reverse;
    gap: 10px;
    align-items: center;
  margin-top: 5px;

    }
    .custm_padding{
      padding:30px
    }
 
    .slide_btn{
      background-color: #057092;
      padding: 5px 15px;
            color:#FFFFFF;
  border:none;
      border-radius: 0px!important;
      margin:5px 30px
    }
    .certif_txt{
      margin-top:20px
    }
    .hideCourses{
      display: block;
    }
  }

  @media screen and (min-width:0px) and (max-width: 499px) {

    .hideCrumb{
      display: none;
    }

    .sliderContainer{
      background-color: transparent;
      box-shadow: 0px 0px 0px rgba(0, 0, 0, 0); 
    }

    .displayNone{
      display: none;
    }

    .hideSlider{
      display: none !important;
    }

    .hideCourses{
      display: block !important;
    }

    .alignLeft{
      text-align: center;
    }
    .alignRight{
      text-align: center !important;
      color: #057092;
    }

    .sidebar{
      display: none;
    }
    .showApplication{
      display: block;
    }

   }
 
   @media screen and (min-width:500px) and (max-width: 976px){

    .hideCrumb{
      display: none;
    }

    .sliderContainer{
      background-color: transparent;
      box-shadow: 0px 0px 0px rgba(0, 0, 0, 0); 
    }

    .sidebar{
      display: none;
    }

    .showApplication{
      display: block;
    }

    .alignLeft{
      text-align: center;
    }
    .alignRight{
      text-align: center !important;
      color: #057092;
    }

    .hideSlider{
      display: none !important;
    }

    .hideCourses{
      display: block;
    }

    .bottomFold{
      padding: 0;
      background-color: white;
      margin-bottom: 18px;
      margin-left: 16px;
      margin-right: 16px;
      box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
    }
  
    .resp_btn_caursl{
      display:none;
      justify-content: space-between;
      margin-top: 18px;
    }
    .displayNone{
      display: none;
    }
   
   }

   .group_icons_list ol:nth-child(2n-1){
    background-color:#F9FAFA;
  padding:12px;
  
  }
  .group_icons_list ol:nth-child(2n ){
  padding:12px
  }
  .group_icons_list ol svg{
  margin-right:1%
  }
  .group_icon_ul ul{
    padding-left:0px;
  }
  .bg_custm{
    color:#057092;
    background-color:white;
    font-weight: 700;
  font-size: 20px;
  line-height: 27px;
  }

  .applicationBtn
{
	background-color: #057092 !important;
    padding: 8px 48px;
    color:#FFFFFF;
    border:none;
     
    border-radius: 0px!important;
}

.applicationBtn:hover
{
	background-color: #057092 !important;
  padding: 8px 48px;
  font-weight: 600;
    color:#FFFFFF;
    border:none;
    
    border-radius: 0px!important;
}

.image_container{

  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;

}

.hidden{
  content-visibility: hidden;
}
.auto{
  content-visibility: auto;
}

.video_box{
  position:relative;
}
.video_overlays {
  position:absolute;
 
}


@media (min-width: 989px) {
  .first_div {
    display: none !important;
  }
 
}
@media (max-width: 990px) {
  .second_div {
    display: none !important;
  }


}


.fontTwentyBlueHeading_new {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  text-align: center;
  line-height: 27px;
  color: #057092;
}

.application_custm_font_blue {
  color: #057092;
  font-weight: 600;
  font-size: 16px;
}

.application_custm_font_new {
  color: #000000;
  font-weight: 700;
  font-size: 16px;
}


.group_icons_list ol:nth-child(2n-1){
  background-color:#F9FAFA;
padding:12px;

}
.group_icons_list ol:nth-child(2n ){
padding:12px
}
.group_icons_list ol svg{
margin-right:1%
}
.group_icon_ul ul{
  padding-left:0px;
}

.sideHeads{
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 27px;
}